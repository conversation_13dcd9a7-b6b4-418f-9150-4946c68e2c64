package com.xm.dal.reconciliation.repository.elastic;

import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.query_dsl.QueryBuilders;
import co.elastic.clients.elasticsearch._types.query_dsl.TermsQueryField;
import com.xm.dal.domain.Profile;
import com.xm.dal.reconciliation.repository.elastic.entity.ProfileDocument;
import java.util.Collection;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.core.convert.ConversionService;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.client.elc.NativeQuery;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.FetchSourceFilter;
import org.springframework.data.elasticsearch.core.query.Query;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor
class ProfileRepositoryImpl implements ProfileRepository {

    public static final String BRAND_ACCOUNT_ID = "brandAccountId";
    public static final String PROFILE_ID = "profileId";
    private final ConversionService conversionService;
    private final ElasticsearchOperations elasticsearchOperations;

    @Override
    public List<Profile> findAllByBrandAccountIds(final Collection<Long> brandAccountIds, final String indexName,
                                                  final String... includes) {
        return findAll(BRAND_ACCOUNT_ID, brandAccountIds, indexName, includes);
    }

    @Override
    public List<Profile> findAllByProfileIds(final Collection<Long> profileIds, final String indexName,
                                             final String... includes) {
        return findAll(PROFILE_ID, profileIds, indexName, includes);
    }

    private List<Profile> findAll(final String field, final Collection<Long> ids, final String indexName,
                                  final String... includes) {
        if (ids.isEmpty()) {
            return List.of();
        }
        List<FieldValue> values = ids.stream().map(FieldValue::of).toList();
        Query query = new NativeQuery(
            QueryBuilders.bool().filter(
                    QueryBuilders.terms()
                        .field(field)
                        .terms(TermsQueryField.of(b -> b.value(values))).build()._toQuery())
                .build()._toQuery());

        query.setPageable(Pageable.ofSize(ids.size()));
        query.addSourceFilter(FetchSourceFilter.of(null, includes, null));

        SearchHits<ProfileDocument> result =
            elasticsearchOperations.search(query, ProfileDocument.class, IndexCoordinates.of(indexName));

        return result.stream()
            .map(SearchHit::getContent)
            .map(profileDocument -> conversionService.convert(profileDocument, Profile.class))
            .toList();
    }

}
