package com.xm.dal.reconciliation.converter;

import com.xm.dal.domain.FinancialInfo;
import com.xm.dal.domain.Label;
import com.xm.dal.domain.LatestLeadInfo;
import com.xm.dal.domain.LeadData;
import com.xm.dal.domain.PhoneCallsInfo;
import com.xm.dal.domain.Profile;
import com.xm.dal.domain.ProfileData;
import com.xm.dal.domain.RafInfo;
import com.xm.dal.domain.account.DemoAccount;
import com.xm.dal.domain.account.EducationAccount;
import com.xm.dal.domain.account.WebinarAccount;
import com.xm.dal.domain.account.trading.TradingAccount;
import com.xm.dal.reconciliation.domain.oracle.ActiveAccount;
import com.xm.dal.reconciliation.domain.oracle.AffiliateInfo;
import com.xm.dal.reconciliation.domain.oracle.BrandAccount;
import com.xm.dal.reconciliation.domain.oracle.BrandAccountComment;
import com.xm.dal.reconciliation.domain.oracle.BrandAccountPhone;
import com.xm.dal.reconciliation.domain.oracle.BrandAccountRating;
import com.xm.dal.reconciliation.domain.oracle.DemoAccountOracle;
import com.xm.dal.reconciliation.domain.oracle.DemoAffiliateInfo;
import com.xm.dal.reconciliation.domain.oracle.EducationAccountOracle;
import com.xm.dal.reconciliation.domain.oracle.EmailHeader;
import com.xm.dal.reconciliation.domain.oracle.FinancialInfoOracle;
import com.xm.dal.reconciliation.domain.oracle.LabelOracle;
import com.xm.dal.reconciliation.domain.oracle.LeadAccount;
import com.xm.dal.reconciliation.domain.oracle.LeadAccountLatestInfo;
import com.xm.dal.reconciliation.domain.oracle.LoginActivityOracle;
import com.xm.dal.reconciliation.domain.oracle.PhoneCallsInfoOracle;
import com.xm.dal.reconciliation.domain.oracle.PhoneCallsOther;
import com.xm.dal.reconciliation.domain.oracle.ProfileAccountManager;
import com.xm.dal.reconciliation.domain.oracle.ProfileAffiliateInfo;
import com.xm.dal.reconciliation.domain.oracle.RafInfoOracle;
import com.xm.dal.reconciliation.domain.oracle.TradingAccountOracle;
import com.xm.dal.reconciliation.domain.oracle.TradingAccountStatusOracle;
import com.xm.dal.reconciliation.domain.oracle.WebinarAccountOracle;
import org.springframework.stereotype.Component;

@Component
public class Converter {

    public Profile convert(BrandAccount from) {
        return Profile.builder()
            .email(from.email())
            .brand(from.brand())
            .profileId(from.profileId())
            .brandAccountId(from.brandAccountId())
            .leadId(from.leadId())
            .profileStatus(from.profileStatus())
            .profileData(from.profileData())
            .validationStatus(from.validationStatus())
            .sat(from.sat())
            .isProfessionalClient(from.isProfessionalClient())
            .confirmations(from.confirmation())
            .rafInfo(from.rafInfo())
            .build();
    }

    public Profile convert(LeadAccount from) {
        return Profile.builder()
            .profileId(from.profileId())
            .leadId(from.leadId())
            .profileStatus(from.profileStatus())
            .email(from.email())
            .brand(from.brand())
            .profileData(from.profileData())
            .confirmations(from.confirmation())
            .rafInfo(from.rafInfo())
            .build();
    }

    public Profile convert(LeadAccountLatestInfo from) {
        return Profile.builder()
            .profileId(from.profileId())
            .leadData(LeadData.builder()
                .latestInfo(LatestLeadInfo.builder()
                    .name(from.name())
                    .phone(from.phone())
                    .country(from.country())
                    .preferredLanguage(from.preferredLanguage())
                    .registrationDate(from.registrationDate())
                    .build())
                .build())
            .build();
    }

    public TradingAccount convert(TradingAccountOracle from) {
        return TradingAccount.builder()
            .login(from.login())
            .serverInfo(from.serverInfo())
            .accountType(from.accountType())
            .accountManager(from.accountManager())
            .groupName(from.groupName())
            .comment(from.comment())
            .leverage(from.leverage())
            .isIslamic(from.isIslamic())
            .socialTradingStatus(from.socialTradingStatus())
            .currency(from.currency())
            .registrationDate(from.registrationDate())
            .gid(from.gid())
            .source(from.source())
            .gwClickDate(from.gwClickDate())
            .closedDate(from.closedDate())
            .isEnabled(from.isEnabled())
            .isReadOnly(from.isReadOnly())
            .sendReports(from.sendReports())
            .bonusTermsAgreed(from.bonusTermsAgreed())
            .autoRebate(from.autoRebate())
            .build();
    }

    public TradingAccount convert(TradingAccountStatusOracle from) {
        return TradingAccount.builder()
            .login(from.login())
            .archivedDate(from.archivedDate())
            .accountStatus(from.accountStatus())
            .build();
    }

    public Profile convert(FinancialInfoOracle from) {
        return Profile.builder()
            .brandAccountId(from.brandAccountId())
            .financialInfo(
                new FinancialInfo(
                    from.depositCount(),
                    from.firstDepositDate(),
                    from.latestDepositDate(),
                    from.latestMonthPnlEur(),
                    from.totalBalanceEur(),
                    from.totalCreditEur(),
                    from.totalDepositsEur(),
                    from.totalEquityEur(),
                    from.totalPnlEur(),
                    from.totalWithdrawalsEur()
                )
            )
            .latestTradeDate(from.latestTradeDate())
            .build();

    }

    public Label convert(LabelOracle from) {
        return new Label(
            from.id(),
            from.name(),
            from.categoryColor(),
            from.isActive()
        );
    }

    public Profile convert(PhoneCallsInfoOracle from) {
        return Profile.builder()
            .brandAccountId(from.brandAccountId())
            .phoneCallsInfo(new PhoneCallsInfo(from.latestValidPhoneCallDate(),
                from.latestInvalidPhoneCallDate(),
                from.latestPhoneCallAttemptDate(),
                from.totalPhoneCallAttempts(),
                from.totalInvalidPhoneCalls(),
                from.totalValidBeforeClientActivation(),
                from.totalInvalidBeforeClientActivation(),
                from.totalAttemptsBeforeClientActivation(),
                null))
            .build();
    }

    public Profile convert(PhoneCallsOther from) {
        return Profile.builder()
            .brandAccountId(from.brandAccountId())
            .phoneCallsInfo(new PhoneCallsInfo(
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                from.totalOtherCommunicatedPhoneCalls()
            )).build();

    }

    public Profile convert(RafInfoOracle from) {
        return Profile.builder()
            .brandAccountId(from.brandAccountId())
            .rafInfo(RafInfo.builder()
                .isReferredClient(from.isReferredClient())
                .isRafQualified(from.isRafQualified())
                .connectionStatusId(from.connectionStatusId())
                .connectionStatusDesc(from.connectionStatusDesc())
                .build()
            ).build();
    }

    public Profile convert(ActiveAccount from) {
        return Profile.builder()
            .brandAccountId(from.brandAccountId())
            .hasActiveTradingAccounts(from.hasActiveTradingAccounts())
            .build();
    }

    public Profile convert(ProfileAccountManager from) {
        return Profile.builder()
            .profileId(from.profileId())
            .accountManager(from.accountManager())
            .build();
    }

    public Profile convert(ProfileAffiliateInfo from) {
        return Profile.builder()
            .profileId(from.profileId())
            .affiliate(from.affiliate())
            .affiliateManager(from.affiliateManager())
            .build();
    }

    public TradingAccount convert(AffiliateInfo from) {
        return TradingAccount.builder()
            .login(from.login())
            .affiliate(from.affiliate())
            .affiliateManager(from.affiliateManager())
            .build();
    }

    public DemoAccount convert(DemoAffiliateInfo from) {
        return DemoAccount.builder()
            .login(from.login())
            .affiliate(from.affiliate())
            .affiliateManager(from.affiliateManager())
            .build();
    }

    public Profile convert(BrandAccountComment from) {
        return Profile.builder()
            .brandAccountId(from.brandAccountId())
            .comments(from.comments())
            .build();
    }

    public Profile convert(BrandAccountPhone from) {
        return Profile.builder()
            .brandAccountId(from.brandAccountId())
            .profileData(ProfileData.builder()
                .phones(from.phones())
                .build())
            .build();
    }

    public Profile convert(BrandAccountRating from) {
        return Profile.builder()
            .brandAccountId(from.brandAccountId())
            .ratings(from.ratings())
            .build();
    }

    public Profile convert(EmailHeader from) {
        return Profile.builder()
            .brandAccountId(from.brandAccountId())
            .latestEmailDate(from.latestEmailDate())
            .build();
    }

    public TradingAccount convert(LoginActivityOracle from) {
        return TradingAccount.builder()
            .login(from.login())
            .loginActivity(from.loginActivity())
            .build();
    }

    public EducationAccount convert(EducationAccountOracle from) {
        return EducationAccount.builder()
            .id(from.id())
            .name(from.name())
            .phone(from.phone())
            .country(from.country())
            .accountManager(from.accountManager())
            .registrationDate(from.registrationDate())
            .acceptedContact(from.acceptedContact())
            .build();
    }

    public WebinarAccount convert(WebinarAccountOracle from) {
        return WebinarAccount.builder()
            .id(from.id())
            .registrationDate(from.registrationDate())
            .name(from.name())
            .phone(from.phone())
            .country(from.country())
            .city(from.city())
            .accountManager(from.accountManager())
            .affiliate(from.affiliate())
            .affiliateManager(from.affiliateManager())
            .build();
    }

    public DemoAccount convert(DemoAccountOracle from) {
        return DemoAccount.builder()
            .login(from.login())
            .serverInfo(from.serverInfo())
            .accountType(from.accountType())
            .accountManager(from.accountManager())
            .groupName(from.groupName())
            .comment(from.comment())
            .leverage(from.leverage())
            .socialTradingStatus(from.socialTradingStatus())
            .currency(from.currency())
            .registrationDate(from.registrationDate())
            .gid(from.gid())
            .source(from.source())
            .gwClickDate(from.gwClickDate())
            .closedDate(from.closedDate())
            .isEnabled(from.isEnabled())
            .isReadOnly(from.isReadOnly())
            .sendReports(from.sendReports())
            .registrationIp(from.registrationIp())
            .registrationIpCountry(from.registrationIpCountry())
            .simCountry(from.simCountry())
            .accountName(from.accountName())
            .accountStatus(from.accountStatus())
            .socialTradingStatus(from.socialTradingStatus())
            .build();
    }

}
