package com.xm.dal.reconciliation.domain.oracle;

import com.xm.dal.domain.Brand;
import com.xm.dal.domain.Confirmation;
import com.xm.dal.domain.ProfileData;
import com.xm.dal.domain.ProfileStatus;
import com.xm.dal.domain.RafInfo;
import com.xm.dal.domain.ValidationStatus;
import lombok.Builder;
import lombok.Getter;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

@Table
@Builder
@Getter
public class BrandAccount {
    private final String email;
    private final Brand brand;
    private final Long profileId;
    @Id
    private final Long brandAccountId;
    private final Long leadId;
    private final ProfileStatus profileStatus;
    private final ProfileData profileData;
    private final ValidationStatus validationStatus;
    private final Boolean sat;
    private final Boolean isProfessionalClient;
    private final Confirmation confirmation;
    private final RafInfo rafInfo;

    public static final String[] ELASTIC_INCLUDES = {
        "email",
        "brand.brandId",
        "brand.brandName",
        "profileId",
        "brandAccountId",
        "leadId",
        "profileStatus",
        "profileData.registrationDate",
        "profileData.registrationStatus",
        "profileData.country",
        "profileData.preferredLanguage",
        "profileData.dateOfBirth",
        "profileData.name",
        "profileData.address",
        "validationStatus",
        "sat",
        "isProfessionalClient",
        "confirmations.promoAgreed",
        "rafInfo.referrerToken"
    };

    public static final String EMAIL = "email";
    public static final String BRAND_ID = "brand_id";
    public static final String BRAND_NAME = "brand_name";
    public static final String XM_PROFILE_ID = "xm_profile_id";
    public static final String BRAND_ACCOUNT_ID = "brand_account_id";
    public static final String LEAD_ID = "lead_id";
    public static final String PROFILE_STATUS = "profile_status";
    public static final String REGISTRATION_DATE = "registration_date";
    public static final String REGISTRATION_STATUS = "registration_status";
    public static final String COUNTRY_CODE = "country_code";
    public static final String COUNTRY_NAME = "country_name";
    public static final String PREFERRED_LANGUAGE = "preferred_language";
    public static final String LANGUAGE_NAME = "language_name";
    public static final String DATE_OF_BIRTH = "date_of_birth";
    public static final String FIRST_NAME = "first_name";
    public static final String LAST_NAME = "last_name";
    public static final String MIDDLE_NAME = "middle_name";
    public static final String TITLE = "title";
    public static final String NATIVE_FIRST_NAME = "native_first_name";
    public static final String NATIVE_LAST_NAME = "native_last_name";
    public static final String ADDRESS_TYPE_ID = "address_type_id";
    public static final String ADDRESS_TYPE_DESC = "address_type_desc";
    public static final String ADDRESS_FULL = "address_full";
    public static final String IS_VERIFIED = "is_verified";
    public static final String CITY = "city";
    public static final String ZIP_CODE = "zip_code";
    public static final String STATE = "state";
    public static final String PROVINCE = "province";
    public static final String PO_BOX = "po_box";
    public static final String STREET_TYPE_NAME = "street_type_name";
    public static final String STREET_TYPE = "street_type";
    public static final String STREET_NAME = "street_name";
    public static final String STREET_NUMBER = "street_number";
    public static final String BUILDING_NAME = "building_name";
    public static final String BUILDING_NUMBER = "building_number";
    public static final String VALIDATION_STATUS = "validation_status";
    public static final String VALIDATION_DATE = "validation_date";
    public static final String IS_REVALIDATION_REQUESTED = "is_revalidation_requested";
    public static final String SAT = "sat";
    public static final String IS_PROFESSIONAL_CLIENT = "is_professional_client";
    public static final String PROMO_AGREED = "promo_agreed";
    public static final String REFERRER_TOKEN = "referrer_token";
}

