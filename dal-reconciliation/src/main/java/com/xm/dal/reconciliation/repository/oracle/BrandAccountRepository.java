package com.xm.dal.reconciliation.repository.oracle;

import com.xm.dal.reconciliation.domain.oracle.BrandAccount;
import java.util.List;
import org.springframework.data.jdbc.repository.query.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface BrandAccountRepository extends CrudRepository<BrandAccount, Long> {
    @Query("""
            SELECT xmp.xm_profile_id,
                   xmp.registration_status,
                   ba.brand_account_id,
                   null as lead_id,
                   null as profile_status,
                   ba.brand_id,
                   badob.date_of_birth,
                   ba.last_update_date,
                   ban.title,
                   ban.first_name,
                   ban.middle_name,
                   ban.last_name,
                   ban.native_first_name,
                   ban.native_last_name,
                   cb.brandname                     as brand_name,
                   baa.country_code,
                   countries.countryname            as country_name,
                   baa.address_type_id,
                   at.address_type_desc,
                   baa.address_full,
                   baa.is_verified,
                   baa.city,
                   baa.zip_code,
                   baa.state,
                   baa.province,
                   baa.po_box,
                   baa.street_type,
                   cst.street_type_name,
                   baa.street_name,
                   baa.street_number,
                   baa.building_name,
                   baa.building_number,
                   bae.email,
                   ba.registration_date,
                   bavs.is_validated                as validation_status,
                   bavs.date_added                  as validation_date,
                   bapci.is_professional_client,
                   bas.sat,
                   bac.agree_promo                  as promo_agreed,
                   NVL(xmpi.preferred_language, bapl.preferred_language) as preferred_language,
                   lang.languageName                as language_name,
                   xmpi.raf_id                      as referrer_token
            FROM JTHALES.brand_accounts ba
            JOIN JTHALES.brand_acc_names ban ON CASE ban.is_active
                WHEN 1 THEN ban.brand_account_id END = ba.brand_account_id
            JOIN JTHALES.brand_acc_addresses baa ON CASE baa.is_active
                WHEN 1 THEN baa.brand_account_id END = ba.brand_account_id
            JOIN JTHALES.address_types at ON at.address_type_id = baa.address_type_id
            LEFT JOIN JTHALES.country_street_types cst ON cst.country_code = baa.country_code
                AND cst.street_type_abbreviation = baa.street_type
            JOIN JTHALES.countries ON countries.iso2CountryCode = baa.country_code
            JOIN JTHALES.companybrands cb ON cb.brandid = ba.brand_id
            JOIN JTHALES.brand_acc_emails bae ON CASE bae.is_active
                WHEN 1 THEN bae.brand_account_id END = ba.brand_account_id
            JOIN JTHALES.brand_acc_date_of_birth badob ON CASE badob.is_active
                WHEN 1 THEN badob.brand_account_id END = ba.brand_account_id
            LEFT JOIN JTHALES.brand_acc_confirmations bac ON CASE bac.is_active
                WHEN 1 THEN bac.brand_account_id END = ba.brand_account_id
            LEFT JOIN JTHALES.brand_acc_preferred_language bapl ON CASE bapl.is_active
                WHEN 1 THEN bapl.brand_account_id END = ba.brand_account_id
            LEFT JOIN JTHALES.brand_acc_validation_stat bavs ON CASE bavs.is_active
                WHEN 1 THEN bavs.brand_account_id END = ba.brand_account_id
            LEFT JOIN JTHALES.brand_acc_prof_client_info bapci ON CASE
                WHEN bapci.is_active = 1 THEN bapci.brand_account_id END = ba.brand_account_id
            LEFT JOIN JTHALES.brand_acc_sat bas ON CASE bas.is_active
                WHEN 1 THEN bas.brand_account_id END = ba.brand_account_id
            LEFT JOIN JTHALES.xm_profile xmp ON ba.brand_account_id = xmp.brand_account_id
            LEFT JOIN JTHALES.xm_profile_info xmpi ON xmpi.xm_profile_id = xmp.xm_profile_id
            LEFT JOIN JTHALES.languages lang
                ON lang.languageiso639_1 = NVL(xmpi.preferred_language, bapl.preferred_language)
            WHERE (xmp.PROFILE_STATUS IS NULL OR xmp.PROFILE_STATUS = 'BRAND_ACCOUNT')
                AND ba.last_update_date BETWEEN TO_TIMESTAMP(:start_timestamp, 'YYYY-MM-DD HH24:MI:SS.FF')
                    AND TO_TIMESTAMP(:end_timestamp, 'YYYY-MM-DD HH24:MI:SS.FF')
            ORDER BY ba.brand_account_id
            OFFSET :offset ROWS FETCH NEXT :pageSize ROWS ONLY
        """)
    List<BrandAccount> getBrandAccountsModified(@Param("start_timestamp") String startTimestamp,
                                                @Param("end_timestamp") String endTimestamp,
                                                @Param("pageSize") int pageSize,
                                                @Param("offset") int offset);


    @Query("""
            SELECT xmp.xm_profile_id,
                   xmp.registration_status,
                   ba.brand_account_id,
                   xmp.lead_id,
                   xmp.profile_status,
                   ba.brand_id,
                   badob.date_of_birth,
                   ba.last_update_date,
                   ban.title,
                   ban.first_name,
                   ban.middle_name,
                   ban.last_name,
                   ban.native_first_name,
                   ban.native_last_name,
                   cb.brandname                                 as brand_name,
                   NVL(xmpi.country_code, baa.country_code)     as country_code,
                   c.countryname                                as country_name,
                   baa.address_type_id,
                   at.address_type_desc,
                   baa.address_full,
                   baa.is_verified,
                   baa.city,
                   baa.zip_code,
                   baa.state,
                   baa.province,
                   baa.po_box,
                   baa.street_type,
                   cst.street_type_name,
                   baa.street_name,
                   baa.street_number,
                   baa.building_name,
                   baa.building_number,
                   bae.email,
                   NVL(xmp.created_on, ba.registration_date)             as registration_date,
                   bavs.is_validated                                     as validation_status,
                   bavs.date_added                                       as validation_date,
                   bapci.is_professional_client,
                   bas.sat,
                   NVL(xmpi.agree_promo ,bac.agree_promo)                as promo_agreed,
                   NVL(xmpi.preferred_language, bapl.preferred_language) as preferred_language,
                   lang.languageName                                     as language_name,
                   xmpi.raf_id                                           as referrer_token,
                   xmp.is_revalidation_requested
            FROM JTHALES.xm_profile xmp
            JOIN JTHALES.brand_accounts ba ON xmp.brand_account_id = ba.brand_account_id
            JOIN JTHALES.brand_acc_emails bae ON CASE bae.is_active
                WHEN 1 THEN bae.brand_account_id END = ba.brand_account_id
            JOIN JTHALES.companybrands cb ON cb.brandid = ba.brand_id
            LEFT JOIN JTHALES.brand_acc_names ban ON CASE ban.is_active
                WHEN 1 THEN ban.brand_account_id END = ba.brand_account_id
            LEFT JOIN JTHALES.brand_acc_addresses baa ON CASE baa.is_active
                WHEN 1 THEN baa.brand_account_id END = ba.brand_account_id
            LEFT JOIN JTHALES.address_types at ON at.address_type_id = baa.address_type_id
            LEFT JOIN JTHALES.country_street_types cst ON cst.country_code = baa.country_code
                AND cst.street_type_abbreviation = baa.street_type
            LEFT JOIN JTHALES.brand_acc_date_of_birth badob ON CASE badob.is_active
                WHEN 1 THEN badob.brand_account_id END = ba.brand_account_id
            LEFT JOIN JTHALES.brand_acc_confirmations bac ON CASE bac.is_active
                WHEN 1 THEN bac.brand_account_id END = ba.brand_account_id
            LEFT JOIN JTHALES.brand_acc_preferred_language bapl ON CASE bapl.is_active
                WHEN 1 THEN bapl.brand_account_id END = ba.brand_account_id
            LEFT JOIN JTHALES.brand_acc_validation_stat bavs ON CASE bavs.is_active
                WHEN 1 THEN bavs.brand_account_id END = ba.brand_account_id
            LEFT JOIN JTHALES.brand_acc_prof_client_info bapci ON CASE
                WHEN bapci.is_active = 1 THEN bapci.brand_account_id END = ba.brand_account_id
            LEFT JOIN JTHALES.brand_acc_sat bas ON CASE bas.is_active
                WHEN 1 THEN bas.brand_account_id END = ba.brand_account_id
            LEFT JOIN JTHALES.xm_profile_info xmpi ON xmpi.xm_profile_id = xmp.xm_profile_id
            LEFT JOIN JTHALES.languages lang
                ON lang.languageiso639_1 = NVL(xmpi.preferred_language, bapl.preferred_language)
            LEFT JOIN jthales.countries c
                ON c.iso2CountryCode = NVL(xmpi.country_code, baa.country_code)
            WHERE ba.last_update_date BETWEEN TO_TIMESTAMP(:start_timestamp, 'YYYY-MM-DD HH24:MI:SS.FF')
                    AND TO_TIMESTAMP(:end_timestamp, 'YYYY-MM-DD HH24:MI:SS.FF')
            ORDER BY ba.brand_account_id
            OFFSET :offset ROWS FETCH NEXT :pageSize ROWS ONLY
        """)
    List<BrandAccount> getBrandAccountsModifiedRt(@Param("start_timestamp") String startTimestamp,
                                                  @Param("end_timestamp") String endTimestamp,
                                                  @Param("pageSize") int pageSize,
                                                  @Param("offset") int offset);

    @Query("""
        SELECT DISTINCT outer.brand_account_id
        FROM JTHALES.brand_acc_emails outer
        WHERE outer.is_active = 0
          AND date_added BETWEEN TO_TIMESTAMP(:start_timestamp, 'YYYY-MM-DD HH24:MI:SS.FF')
                                AND TO_TIMESTAMP(:end_timestamp, 'YYYY-MM-DD HH24:MI:SS.FF')
          AND NOT EXISTS(SELECT inner.brand_account_id
                         FROM JTHALES.brand_acc_emails inner
                         WHERE inner.is_active = 1
                           AND inner.brand_account_id = outer.brand_account_id)
        """)
    List<Long> getDeletedBrandAccountIds(@Param("start_timestamp") String startTimestamp,
                                         @Param("end_timestamp") String endTimestamp);
}
