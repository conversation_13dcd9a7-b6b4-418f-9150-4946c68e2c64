package com.xm.dal.reconciliation.config.rowmapper;

import com.xm.dal.domain.Address;
import com.xm.dal.domain.AddressDetails;
import com.xm.dal.domain.AddressType;
import com.xm.dal.domain.Brand;
import com.xm.dal.domain.Confirmation;
import com.xm.dal.domain.Country;
import com.xm.dal.domain.Language;
import com.xm.dal.domain.Name;
import com.xm.dal.domain.ProfileData;
import com.xm.dal.domain.ProfileStatus;
import com.xm.dal.domain.RafInfo;
import com.xm.dal.domain.RegistrationStatus;
import com.xm.dal.domain.StreetType;
import com.xm.dal.domain.ValidationStatus;
import com.xm.dal.reconciliation.domain.oracle.BrandAccount;
import java.sql.Date;
import java.sql.ResultSet;
import java.sql.SQLException;
import lombok.NonNull;

public class BrandAccountRowMapper extends BaseRowMapper<BrandAccount> {

    @Override
    public BrandAccount mapRow(@NonNull ResultSet rs, int rowNum) throws SQLException {
        return BrandAccount.builder()
            .email(rs.getString(BrandAccount.EMAIL))
            .brand(new Brand(
                rs.getInt(BrandAccount.BRAND_ID),
                rs.getString(BrandAccount.BRAND_NAME)
            ))
            .profileId(rs.getObject(BrandAccount.XM_PROFILE_ID, Long.class))
            .brandAccountId(rs.getObject(BrandAccount.BRAND_ACCOUNT_ID, Long.class))
            .leadId(rs.getObject(BrandAccount.LEAD_ID, Long.class))
            .profileStatus(getOrNull(rs.getString(BrandAccount.PROFILE_STATUS), ProfileStatus::valueOf))
            .profileData(ProfileData.builder()
                .registrationDate(
                    getOrNull(rs.getTimestamp(BrandAccount.REGISTRATION_DATE), BaseRowMapper::convertToOffsetDateTime))
                .registrationStatus(
                    getOrNull(rs.getString(BrandAccount.REGISTRATION_STATUS), RegistrationStatus::valueOf))
                .country(Country.builder()
                    .code(rs.getString(BrandAccount.COUNTRY_CODE))
                    .name(rs.getString(BrandAccount.COUNTRY_NAME))
                    .build())
                .preferredLanguage(Language.builder()
                    .code(rs.getString(BrandAccount.PREFERRED_LANGUAGE))
                    .name(rs.getString(BrandAccount.LANGUAGE_NAME))
                    .build())
                .dateOfBirth(getOrNull(rs.getDate(BrandAccount.DATE_OF_BIRTH), Date::toLocalDate))
                .name(Name.builder()
                    .firstName(rs.getString(BrandAccount.FIRST_NAME))
                    .lastName(rs.getString(BrandAccount.LAST_NAME))
                    .middleName(rs.getString(BrandAccount.MIDDLE_NAME))
                    .title(rs.getString(BrandAccount.TITLE))
                    .nativeFirstName(rs.getString(BrandAccount.NATIVE_FIRST_NAME))
                    .nativeLastName(rs.getString(BrandAccount.NATIVE_LAST_NAME))
                    .build())
                .address(rs.getString(BrandAccount.ADDRESS_FULL) != null
                    ? Address.builder()
                    .addressType(AddressType.builder()
                        .id(rs.getObject(BrandAccount.ADDRESS_TYPE_ID, Integer.class))
                        .description(rs.getString(BrandAccount.ADDRESS_TYPE_DESC))
                        .build())
                    .addressFull(rs.getString(BrandAccount.ADDRESS_FULL))
                    .isVerified(rs.getObject(BrandAccount.IS_VERIFIED, Boolean.class))
                    .city(rs.getString(BrandAccount.CITY))
                    .country(Country.builder()
                        .code(rs.getString(BrandAccount.COUNTRY_CODE))
                        .name(rs.getString(BrandAccount.COUNTRY_NAME))
                        .build())
                    .details(AddressDetails.builder()
                        .zipCode(rs.getString(BrandAccount.ZIP_CODE))
                        .state(rs.getString(BrandAccount.STATE))
                        .province(rs.getString(BrandAccount.PROVINCE))
                        .poBox(rs.getString(BrandAccount.PO_BOX))
                        .streetType(StreetType.builder()
                            .type(rs.getString(BrandAccount.STREET_TYPE_NAME))
                            .abbreviatedType(rs.getString(BrandAccount.STREET_TYPE))
                            .build())
                        .streetName(rs.getString(BrandAccount.STREET_NAME))
                        .streetNumber(rs.getString(BrandAccount.STREET_NUMBER))
                        .buildingName(rs.getString(BrandAccount.BUILDING_NAME))
                        .buildingNumber(rs.getString(BrandAccount.BUILDING_NUMBER))
                        .build())
                    .build() : null)
                .build())
            .validationStatus(ValidationStatus.builder()
                .isValidated(rs.getObject(BrandAccount.VALIDATION_STATUS, Boolean.class))
                .validationDate(
                    getOrNull(rs.getTimestamp(BrandAccount.VALIDATION_DATE), BaseRowMapper::convertToOffsetDateTime))
                .isRevalidationRequested(rs.getObject(BrandAccount.IS_REVALIDATION_REQUESTED, Boolean.class))
                .build())
            .sat(rs.getObject(BrandAccount.SAT, Boolean.class))
            .isProfessionalClient(rs.getObject(BrandAccount.IS_PROFESSIONAL_CLIENT, Boolean.class))
            .confirmation(Confirmation.builder()
                .promoAgreed(rs.getObject(BrandAccount.PROMO_AGREED, Boolean.class))
                .build())
            .rafInfo(RafInfo.builder()
                .referrerToken(rs.getString(BrandAccount.REFERRER_TOKEN))
                .build())
            .build();
    }

}
