package com.xm.dal.reconciliation.config.rowmapper;

import com.xm.dal.domain.Brand;
import com.xm.dal.domain.Confirmation;
import com.xm.dal.domain.Country;
import com.xm.dal.domain.Language;
import com.xm.dal.domain.ProfileData;
import com.xm.dal.domain.ProfileStatus;
import com.xm.dal.domain.RafInfo;
import com.xm.dal.domain.RegistrationStatus;
import com.xm.dal.reconciliation.domain.oracle.LeadAccount;
import java.sql.ResultSet;
import java.sql.SQLException;
import lombok.NonNull;

public class LeadAccountRowMapper extends BaseRowMapper<LeadAccount> {

    @Override
    public LeadAccount mapRow(@NonNull ResultSet rs, int rowNum) throws SQLException {
        return LeadAccount.builder()
            .profileId(rs.getObject(LeadAccount.XM_PROFILE_ID, Long.class))
            .leadId(rs.getObject(LeadAccount.LEAD_ID, Long.class))
            .profileStatus(getOrNull(rs.getString(LeadAccount.PROFILE_STATUS), ProfileStatus::valueOf))
            .email(rs.getString(LeadAccount.EMAIL_FIELD))
            .brand(Brand.builder()
                .brandId(rs.getInt(LeadAccount.BRAND_ID))
                .brandName(rs.getString(LeadAccount.BRAND_NAME))
                .build())
            .profileData(ProfileData.builder()
                .registrationDate(
                    getOrNull(rs.getTimestamp(LeadAccount.REGISTRATION_DATE), BaseRowMapper::convertToOffsetDateTime))
                .registrationStatus(
                    getOrNull(rs.getString(LeadAccount.REGISTRATION_STATUS), RegistrationStatus::valueOf))
                .country(Country.builder()
                    .code(rs.getString(LeadAccount.COUNTRY_CODE))
                    .name(rs.getString(LeadAccount.COUNTRY_NAME))
                    .build())
                .preferredLanguage(Language.builder()
                    .code(rs.getString(LeadAccount.PREFERRED_LANGUAGE))
                    .name(rs.getString(LeadAccount.LANGUAGE_NAME))
                    .build())
                .build())
            .confirmation(Confirmation.builder()
                .promoAgreed(rs.getObject(LeadAccount.PROMO_AGREED, Boolean.class))
                .build())
            .rafInfo(RafInfo.builder()
                .referrerToken(rs.getString(LeadAccount.REFERRER_TOKEN))
                .build())
            .build();
    }
}
