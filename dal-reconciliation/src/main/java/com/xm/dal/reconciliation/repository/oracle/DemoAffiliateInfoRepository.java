package com.xm.dal.reconciliation.repository.oracle;

import com.xm.dal.reconciliation.domain.oracle.DemoAffiliateInfo;
import java.util.List;
import org.springframework.data.jdbc.repository.query.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;

public interface DemoAffiliateInfoRepository extends CrudRepository<DemoAffiliateInfo, Long> {

    @Query("""
            SELECT da.xm_profile_id,
                   da.login,
                   da.affiliate_id,
                   aah.first_name                                                   affiliate_first_name,
                   aah.last_name                                                    affiliate_last_name,
                   afmu.ldap_id                                                     affiliate_manager_ldap_id,
                   afmu.email                                                       affiliate_manager_email,
                   afmu.firstname                                                   affiliate_manager_first_name,
                   afmu.lastname                                                    affiliate_manager_last_name,
                   CASE WHEN afmu.country_code IS NOT NULL
                       THEN TRIM(afmu.country_code || ' ' || afmu.department) END   affiliate_manager_office
            FROM JTHALES.demo_accounts da
            LEFT JOIN AFFILIATESYSTEMUSR.affiliate_addresses_history aah
                ON CASE WHEN aah.is_active = 'Y' THEN aah.affiliate_id END = da.affiliate_id
            LEFT JOIN AFFILIATESYSTEMUSR.affiliate_manager_ids_history amih
                ON CASE WHEN amih.is_active = 'Y' THEN amih.affiliate_id END = da.affiliate_id
            LEFT JOIN AFFILIATESYSTEMUSR.managers afm
                ON afm.id = CASE WHEN amih.is_active = 'Y' THEN amih.manager_id END
            LEFT JOIN JMANAGER.users afmu ON afmu.ldap_id = afm.ldap_id
            WHERE (
                    amih.from_date BETWEEN
                        CAST(FROM_TZ(TO_TIMESTAMP(:start_timestamp, 'YYYY-MM-DD HH24:MI:SS.FF'), 'UTC')
                            AT TIME ZONE 'Europe/Nicosia' AS TIMESTAMP)
                        AND CAST(FROM_TZ(TO_TIMESTAMP(:end_timestamp, 'YYYY-MM-DD HH24:MI:SS.FF'), 'UTC')
                            AT TIME ZONE 'Europe/Nicosia' AS TIMESTAMP)
                    OR aah.from_date BETWEEN
                        CAST(FROM_TZ(TO_TIMESTAMP(:start_timestamp, 'YYYY-MM-DD HH24:MI:SS.FF'), 'UTC')
                            AT TIME ZONE 'Europe/Nicosia' AS TIMESTAMP)
                        AND CAST(FROM_TZ(TO_TIMESTAMP(:end_timestamp, 'YYYY-MM-DD HH24:MI:SS.FF'), 'UTC')
                            AT TIME ZONE 'Europe/Nicosia' AS TIMESTAMP)
                )
                AND GREATEST(
                            CAST(FROM_TZ(COALESCE(amih.from_date,
                                TO_TIMESTAMP('1970-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS')), 'Europe/Nicosia')
                                    AT TIME ZONE 'UTC' AS TIMESTAMP),
                            CAST(FROM_TZ(COALESCE(aah.from_date,
                                TO_TIMESTAMP('1970-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS')), 'Europe/Nicosia')
                                    AT TIME ZONE 'UTC' AS TIMESTAMP)
                    ) <= TO_TIMESTAMP(:end_timestamp, 'YYYY-MM-DD HH24:MI:SS.FF')
            AND da.xm_profile_id IS NOT NULL
            ORDER BY da.xm_profile_id, da.demo_account_id
            OFFSET :offset ROWS FETCH NEXT :pageSize ROWS ONLY
        """)
    List<DemoAffiliateInfo> getAffiliateInfoModified(@Param("start_timestamp") String startTimestamp,
                                                     @Param("end_timestamp") String endTimestamp,
                                                     @Param("pageSize") int pageSize,
                                                     @Param("offset") int offset);
}
