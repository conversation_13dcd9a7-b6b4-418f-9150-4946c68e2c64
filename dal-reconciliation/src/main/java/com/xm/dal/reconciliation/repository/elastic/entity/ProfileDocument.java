package com.xm.dal.reconciliation.repository.elastic.entity;

import com.xm.dal.domain.Brand;
import com.xm.dal.domain.Comment;
import com.xm.dal.domain.Confirmation;
import com.xm.dal.domain.FinancialInfo;
import com.xm.dal.domain.Label;
import com.xm.dal.domain.LeadData;
import com.xm.dal.domain.PhoneCallsInfo;
import com.xm.dal.domain.ProfileData;
import com.xm.dal.domain.ProfileStatus;
import com.xm.dal.domain.RafInfo;
import com.xm.dal.domain.Rating;
import com.xm.dal.domain.ValidationStatus;
import com.xm.dal.domain.account.AccountManager;
import com.xm.dal.domain.account.DemoAccount;
import com.xm.dal.domain.account.EducationAccount;
import com.xm.dal.domain.account.FirstStep;
import com.xm.dal.domain.account.SecondStep;
import com.xm.dal.domain.account.WalletAccount;
import com.xm.dal.domain.account.WebinarAccount;
import com.xm.dal.domain.account.trading.Affiliate;
import com.xm.dal.domain.account.trading.TradingAccount;
import java.time.OffsetDateTime;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.WriteTypeHint;
import org.springframework.data.elasticsearch.core.query.SeqNoPrimaryTerm;

@Document(
    indexName = /* language=SpEL */ "#{ @environment.getProperty('app.elasticsearch-index') }",
    writeTypeHint = WriteTypeHint.FALSE,
    createIndex = false)
@Getter
@Setter
public class ProfileDocument {

    @Id
    private String id;

    private String email;
    private Brand brand;
    private Long profileId;
    private Long brandAccountId;
    private Long leadId;
    private ProfileStatus profileStatus;
    private ProfileData profileData;
    private List<TradingAccount> tradingAccounts;
    private List<DemoAccount> demoAccounts;
    private List<EducationAccount> educationAccounts;
    private List<WalletAccount> walletAccounts;
    private List<WebinarAccount> webinarAccounts;
    private List<FirstStep> firstSteps;
    private List<SecondStep> secondSteps;
    private ValidationStatus validationStatus;
    private PhoneCallsInfo phoneCallsInfo;
    private FinancialInfo financialInfo;
    private RafInfo rafInfo;
    private List<Label> labels;
    private List<Rating> ratings;
    private List<Comment> comments;
    private Boolean sat;
    private Boolean isProfessionalClient;
    private Boolean hasActiveTradingAccounts;
    private OffsetDateTime latestEmailDate;
    private OffsetDateTime latestTradeDate;
    private Confirmation confirmations;
    private AccountManager accountManager;
    private Affiliate affiliate;
    private AccountManager affiliateManager;
    private LeadData leadData;
    //field to control optimistic locking
    private SeqNoPrimaryTerm seqNoPrimaryTerm;
}
