[versions]
awsSpringCloud = "3.4.0"
caffeine = "3.2.1"
elasticsearchEvolution = "0.6.0"
gatlingPlugin = "3.14.3"
pitestPlugin = "1.15.0"
gitVersionPlugin = "3.3.0"
gitPropertiesPlugin = "2.5.0"
graphqlExtendedScalars = "22.0"
guava = "33.4.8-jre"
logstashLogbackEncoder = "8.1"
lombokMapstructBinding = "0.2.0"
mapstruct = "1.6.3"
newRelicAgent = "8.21.0"
oracleJdbc11 = "23.8.0.25.04"
protobufUtil = "4.31.1"
sonarqubePlugin = "6.2.0.5505"
springAi = "1.0.0-M6"
springBootDockerCompose = "3.4.4"
springBootPlubin = "3.5.3"
springCloud = "2025.0.0"
springDependenciesPlugin = "1.1.7"
xmContractsCommon = "0.23.0"
xmContractsJThalesCommon = "1.0.0"
xmContractsJThalesProfile = "2.0.10"
xmContractsJThalesProfileMetadata = "1.0.0"
xmContractsTradingMeshServerOperation = "0.4.2"
xmContractsLoyaltyProgramCallback = "0.0.1"
xmContractsKycValidationStatus = "0.6.7"
xmStaticCodeAnalyzers = "0.3.0-RELEASE@zip"

[libraries]
aws-spring-cloud = { group = "io.awspring.cloud", name = "spring-cloud-aws-dependencies", version.ref = "awsSpringCloud" }
elasticsearch-evolution-starter = { group = "com.senacor.elasticsearch.evolution", name = "spring-boot-starter-elasticsearch-evolution", version.ref = "elasticsearchEvolution" }
graphql-extendedScalars = { group = "com.graphql-java", name = "graphql-java-extended-scalars", version.ref = "graphqlExtendedScalars" }
guava = { group = "com.google.guava", name = "guava", version.ref = "guava" }
kaffeine = { group = "com.github.ben-manes.caffeine", name = "caffeine", version.ref = "caffeine" }
logstash-logback-encoder = { group = "net.logstash.logback", name = "logstash-logback-encoder", version.ref = "logstashLogbackEncoder" }
lombok-mapstruct-binding = { group = "org.projectlombok", name = "lombok-mapstruct-binding", version.ref = "lombokMapstructBinding" }
mapstruct = { group = "org.mapstruct", name = "mapstruct", version.ref = "mapstruct" }
mapstruct-processor = { group = "org.mapstruct", name = "mapstruct-processor", version.ref = "mapstruct" }
newrelic-agent = { group = "com.newrelic.agent.java", name = "newrelic-agent", version.ref = "newRelicAgent" }
oracle-jdbc11 = { group = "com.oracle.database.jdbc", name = "ojdbc11", version.ref = "oracleJdbc11" }
protobuf-util = { group = "com.google.protobuf", name = "protobuf-java-util", version.ref = "protobufUtil" }
spring-ai-mcp-client-boot-starter = { group = "org.springframework.ai", name = "spring-ai-mcp-client-spring-boot-starter", version.ref = "springAi" }
spring-ai-openai-boot-starter = { group = "org.springframework.ai", name = "spring-ai-openai-spring-boot-starter", version.ref = "springAi" }
spring-boot-docker-compose = { group = "org.springframework.boot", name = "spring-boot-docker-compose", version.ref = "springBootDockerCompose" }
spring-cloud-dependencies = { group = "org.springframework.cloud", name = "spring-cloud-dependencies", version.ref = "springCloud" }
xm-contracts-common = { group = "com.xm", name = "contracts-common", version.ref = "xmContractsCommon" }
xm-contracts-jthales-common = { group = "com.xm", name = "contracts-services-jthales-common", version.ref = "xmContractsJThalesCommon" }
xm-contracts-jthales-profile = { group = "com.xm", name = "contracts-services-jthales-profile", version.ref = "xmContractsJThalesProfile" }
xm-contracts-jthales-profilemetadata = { group = "com.xm", name = "contracts-services-jthales-profilemetadata", version.ref = "xmContractsJThalesProfileMetadata" }
xm-contracts-trading_mesh-server_operation = { group = "com.xm", name = "contracts-services-trading-mesh-server-operation", version.ref = "xmContractsTradingMeshServerOperation" }
xm-contracts-loyalty_program-callback = { group = "com.xm", name = "contracts-services-loyalty_program-callback", version.ref = "xmContractsLoyaltyProgramCallback" }
xm-contracts-kyc-validation-status = { group = "com.xm", name = "contracts-services-kyc-validation", version.ref = "xmContractsKycValidationStatus" }
xm-staticCodeAnalyzers = { group = "com.xm", name = "static-code-analyzers", version.ref = "xmStaticCodeAnalyzers" }

[plugins]
pitest = { id = "info.solidsoft.pitest", version.ref = "pitestPlugin" }
gatling = { id = "io.gatling.gradle", version.ref = "gatlingPlugin" }
git-version = { id = "com.palantir.git-version", version.ref = "gitVersionPlugin" }
git-properties = { id = "com.gorylenko.gradle-git-properties", version.ref = "gitPropertiesPlugin" }
sonarqube = { id = "org.sonarqube", version.ref = "sonarqubePlugin" }
spring-boot = { id = "org.springframework.boot", version.ref = "springBootPlubin" }
spring-dependencies = { id = "io.spring.dependency-management", version.ref = "springDependenciesPlugin" }