# Loyalty points manager service

## Overview
This service captures related call back requests from the Loyalty Points Manager service and publishes them 
to a Kafka topic.

## Model
The top-level message is the [``CallBackRequestEvent``](https://gitlab.xm.com/xlDevs/proto_contracts/-/blob/master/src/xm/protobuf/services/loyalty_program/callback/v1/call_back_request_event.proto)
It may contain any of call back requests or completions.

## Producer
The producing application is the ``Loyalty Points Manager`` Microservice (Java). 
In more detail, the messages are published from the Loyalty Points Manager through a Kafka topic,
then external services of DAL (Java) and CRM (Php) listens for such messages and process them.

### Communication needs
Every time that a callback request gets created or completed the relevant applications will consume 
the relevant data of the client and will process them in order for the line managers to be able to
communicate effectively with the clients.

### Partitioning algorithm
We create all the events in the same partition in order to guarantee correct order, since these events 
will not be in a huge volume since this is for VIP clients there is no need for multiple partitions.

## Consumers
- DAL (Java)
- CRM (Php)

## Kafka topic
- services.loyalty_program.callback.v1.0

### Name
The messages are published to the topic: 
- ``services.loyalty_program.callback.v1.0`` (for XM)

### Schema
For kafka message value, loyalty event protobuf message is serialized to bytes using protobuf
library's encoding.

## Contact
Please contact loyalty team for any issues regarding this service:
- <EMAIL>
- <EMAIL>
- <EMAIL>
