package com.xm.jthales.domainevents.service;

import com.google.protobuf.GeneratedMessage;
import com.google.protobuf.Timestamp;
import com.xm.jthales.domainevents.model.DepositResponse;
import com.xm.jthales.domainevents.model.ServerInfo;
import com.xm.jthales.domainevents.model.TradingAccounts;
import com.xm.jthales.domainevents.model.XmProfileIds;
import com.xm.jthales.domainevents.model.XmProfileMergedDetails;
import com.xm.jthales.integration.shared.kafka.KafkaTemplate;
import com.xm.jthalesutils.revamp.common.dto.profileData.DemoAccountV2;
import jThalesServer.revamp.xmProfile.incomplete.model.XmProfileIncomplete;
import jThalesServer.revamp.xmProfile.model.RiskBasedQuestionsStatus;
import jThalesServer.revamp.xmProfile.model.XmProfile;
import jThalesUtils.jThalesServerOutput.response.revamp.xmProfile.ProfileStatus;
import jThalesUtils.jThalesServerOutput.responseObjects.BrandAccountV2;
import jThalesUtils.jThalesServerOutput.responseObjects.Comment;
import jThalesUtils.jThalesServerOutput.responseObjects.TradingAccount;
import jThalesUtils.jThalesServerOutput.responseObjects.WalletAccount;
import jThalesUtils.jThalesServerOutput.responseObjects.rating.Rating;
import jThalesUtils.us.shared.account.PlatformAccount;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import xm.protobuf.common.TraceDataOuterClass.TraceData;
import xm.protobuf.common.UuidOuterClass.Uuid;
import xm.protobuf.services.jthales.profile.v1.ProfileEventOuterClass.ProfileEvent;
import xm.protobuf.services.jthales.profile.v1.ProfileEventOuterClass.ProfileEvent.Builder;
import xm.protobuf.services.jthales.profilemetadata.v1.CommentEventOuterClass.CommentEvent;
import xm.protobuf.services.jthales.profilemetadata.v1.CommentEventOuterClass.CommentsSnapshotUpdated;
import xm.protobuf.services.jthales.profile.v1.DemoAccountEventOuterClass.DemoAccountClosed;
import xm.protobuf.services.jthales.profile.v1.DemoAccountEventOuterClass.DemoAccountCreated;
import xm.protobuf.services.jthales.profile.v1.DemoAccountEventOuterClass.DemoAccountDeleted;
import xm.protobuf.services.jthales.profile.v1.DemoAccountEventOuterClass.DemoAccountEvent;
import xm.protobuf.services.jthales.profile.v1.DemoAccountEventOuterClass.DemoAccountUpdated;
import xm.protobuf.services.jthales.profilemetadata.v1.ProfileMetadataEventOuterClass.ProfileMetadataEvent;
import xm.protobuf.services.jthales.profilemetadata.v1.RatingEventOuterClass.RatingEvent;
import xm.protobuf.services.jthales.profilemetadata.v1.RatingEventOuterClass.RatingsSnapshotUpdated;
import xm.protobuf.services.jthales.profile.v1.TradingAccountEventOuterClass.TradingAccountClosed;
import xm.protobuf.services.jthales.profile.v1.TradingAccountEventOuterClass.TradingAccountCreated;
import xm.protobuf.services.jthales.profile.v1.TradingAccountEventOuterClass.TradingAccountDeleted;
import xm.protobuf.services.jthales.profile.v1.TradingAccountEventOuterClass.TradingAccountEvent;
import xm.protobuf.services.jthales.profile.v1.TradingAccountEventOuterClass.TradingAccountUpdated;
import xm.protobuf.services.jthales.profile.v1.WalletAccountEventOuterClass.WalletAccountClosed;
import xm.protobuf.services.jthales.profile.v1.WalletAccountEventOuterClass.WalletAccountCreated;
import xm.protobuf.services.jthales.profile.v1.WalletAccountEventOuterClass.WalletAccountDeleted;
import xm.protobuf.services.jthales.profile.v1.WalletAccountEventOuterClass.WalletAccountEvent;
import xm.protobuf.services.jthales.profile.v1.WalletAccountEventOuterClass.WalletAccountUpdated;
import xm.protobuf.services.jthales.profile.v1.XmProfileEventOuterClass.XmProfileAffiliateCreated;
import xm.protobuf.services.jthales.profile.v1.XmProfileEventOuterClass.XmProfileCompleted;
import xm.protobuf.services.jthales.profile.v1.XmProfileEventOuterClass.XmProfileConvertCompleted;
import xm.protobuf.services.jthales.profile.v1.XmProfileEventOuterClass.XmProfileConvertVerified;
import xm.protobuf.services.jthales.profile.v1.XmProfileEventOuterClass.XmProfileCreated;
import xm.protobuf.services.jthales.profile.v1.XmProfileEventOuterClass.XmProfileDeleted;
import xm.protobuf.services.jthales.profile.v1.XmProfileEventOuterClass.XmProfileEvent;
import xm.protobuf.services.jthales.profile.v1.XmProfileEventOuterClass.XmProfileMerged;
import xm.protobuf.services.jthales.profile.v1.XmProfileEventOuterClass.XmProfileReactivated;
import xm.protobuf.services.jthales.profile.v1.XmProfileEventOuterClass.XmProfileSplit;
import xm.protobuf.services.jthales.profile.v1.XmProfileEventOuterClass.XmProfileUpdated;
import xm.protobuf.services.jthales.profile.v1.XmProfileEventOuterClass.XmProfileVerified;
import xm.protobuf.services.jthales.profile.v1.XmProfileEventOuterClass.XmProfileEmailUpdated;
import xm.protobuf.services.jthales.ewallet.transaction.v1.DepositOuterClass.Deposit;
import xm.protobuf.services.jthales.ewallet.transaction.v1.TransactionEventOuterClass.TransactionEvent;

import javax.enterprise.inject.Default;
import javax.inject.Inject;
import javax.inject.Singleton;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Default
@Singleton
public class DomainEventKafkaSenderServiceImpl implements DomainEventSenderService {

    //<editor-fold desc= "XmProfile">
    public void sendXmProfileCreated(final XmProfileIds xmProfileIds) {

        final XmProfileCreated xmProfileCreated = domainEventBuilderService.buildXmProfileCreated(xmProfileIds);
        final XmProfileEvent xmProfileEvent = XmProfileEvent.newBuilder().setXmProfileCreated(xmProfileCreated).build();

        final ProfileEvent event = createXmProfileEvent(xmProfileEvent);

        sendToProfilesKafkaTopic(xmProfileIds.getBrandName(), event);
    }

    @Override
    public void sendXmProfileVerified(final XmProfileIds xmProfileIds,
                                      final ProfileStatus profileStatus,
                                      final XmProfileIncomplete xmProfileIncomplete,
                                      final LocalDateTime registrationDate) {

        final XmProfileVerified xmProfileVerified =
            domainEventBuilderService.buildXmProfileVerified(xmProfileIds, profileStatus, xmProfileIncomplete,
                registrationDate);
        final XmProfileEvent xmProfileEvent =
            XmProfileEvent.newBuilder().setXmProfileVerified(xmProfileVerified).build();

        final ProfileEvent event = createXmProfileEvent(xmProfileEvent);

        sendToProfilesKafkaTopic(xmProfileIncomplete.getBrandName(), event);
    }

    @Override
    public void sendXmProfileConvertVerified(final long xmProfileId,
                                             final XmProfile xmProfile,
                                             final LocalDateTime registrationDate) {

        final XmProfileConvertVerified xmProfileConvertVerified =
            domainEventBuilderService.buildXmProfileConvertVerified(xmProfileId, xmProfile, registrationDate);
        final XmProfileEvent xmProfileEvent =
            XmProfileEvent.newBuilder().setXmProfileConvertVerified(xmProfileConvertVerified).build();

        final ProfileEvent event = createXmProfileEvent(xmProfileEvent);

        sendToProfilesKafkaTopic(xmProfile.getBrandName(), event);
    }

    public void sendXmProfileCompleted(final XmProfileIds xmProfileIds,
                                       final ProfileStatus profileStatus,
                                       final BrandAccountV2 brandAccountV2) {

        sendXmProfileCompleted(xmProfileIds, profileStatus, brandAccountV2, false);
    }

    public void sendXmProfileCompleted(final XmProfileIds xmProfileIds,
                                       final ProfileStatus profileStatus,
                                       final BrandAccountV2 brandAccountV2,
                                       final boolean isRevalidationRequested) {

        final XmProfileCompleted xmProfileCompleted =
            domainEventBuilderService.buildXmProfileCompleted(xmProfileIds, profileStatus, brandAccountV2, isRevalidationRequested);
        final XmProfileEvent xmProfileEvent =
            XmProfileEvent.newBuilder().setXmProfileCompleted(xmProfileCompleted).build();

        final ProfileEvent event = createXmProfileEvent(xmProfileEvent);

        sendToProfilesKafkaTopic(xmProfileIds.getBrandName(), event);
    }

    public void sendXmProfileConvertCompleted(final long xmProfileId, final XmProfile xmProfile) {

        final XmProfileConvertCompleted xmProfileConvertCompleted =
            domainEventBuilderService.buildXmProfileConvertCompleted(xmProfileId, xmProfile);
        final XmProfileEvent xmProfileEvent =
            XmProfileEvent.newBuilder().setXmProfileConvertCompleted(xmProfileConvertCompleted).build();

        final ProfileEvent event = createXmProfileEvent(xmProfileEvent);

        sendToProfilesKafkaTopic(xmProfile.getBrandName(), event);
    }

    @Override
    public void sendXmProfileUpdated(final XmProfileIds xmProfileIds,
                                     final ProfileStatus profileStatus,
                                     final BrandAccountV2 brandAccountV2) {

        sendXmProfileUpdated(xmProfileIds, profileStatus, brandAccountV2, false);
    }

    @Override
    public void sendXmProfileUpdated(final XmProfileIds xmProfileIds,
                                     final ProfileStatus profileStatus,
                                     final BrandAccountV2 brandAccountV2,
                                     final boolean isRevalidationRequested) {

        final XmProfileUpdated xmProfileUpdated =
            domainEventBuilderService.buildXmProfileUpdated(xmProfileIds, profileStatus, brandAccountV2, isRevalidationRequested);
        final XmProfileEvent xmProfileEvent = XmProfileEvent.newBuilder().setXmProfileUpdated(xmProfileUpdated).build();

        final ProfileEvent event = createXmProfileEvent(xmProfileEvent);

        sendToProfilesKafkaTopic(xmProfileIds.getBrandName(), event);
    }

    @Override
    public void sendXmProfileReactivated(final XmProfileIds xmProfileIds,
                                         final ProfileStatus profileStatus,
                                         final BrandAccountV2 brandAccountV2) {

        sendXmProfileReactivated(xmProfileIds, profileStatus, brandAccountV2, false);
    }

    @Override
    public void sendXmProfileReactivated(final XmProfileIds xmProfileIds,
                                         final ProfileStatus profileStatus,
                                         final BrandAccountV2 brandAccountV2,
                                         final boolean isRevalidationRequested) {


        final XmProfileReactivated xmProfileReactivated =
            domainEventBuilderService.buildXmProfileReactivated(xmProfileIds, profileStatus, brandAccountV2, isRevalidationRequested);
        final XmProfileEvent xmProfileEvent =
            XmProfileEvent.newBuilder().setXmProfileReactivated(xmProfileReactivated).build();

        final ProfileEvent event = createXmProfileEvent(xmProfileEvent);

        sendToProfilesKafkaTopic(xmProfileIds.getBrandName(), event);
    }

    public void sendXmProfileSplit(final BrandAccountV2 sourceBrandAccount,
                                   final TradingAccounts sourceTradingAccounts,
                                   final BrandAccountV2 destinationBrandAccount,
                                   final TradingAccounts destinationTradingAccounts) {

        final XmProfileSplit xmProfileSplit = domainEventBuilderService
            .buildXmProfileSplit(sourceBrandAccount, sourceTradingAccounts, destinationBrandAccount,
                destinationTradingAccounts);
        final XmProfileEvent xmProfileEvent = XmProfileEvent.newBuilder().setXmProfileSplit(xmProfileSplit).build();

        final ProfileEvent event = createXmProfileEvent(xmProfileEvent);

        sendToProfilesKafkaTopic(sourceBrandAccount.getBrand().getBrandName(), event);
    }

    public void sendXmProfileMerged(final XmProfileIds sourceXmProfileIds,
                                    final XmProfileIds destinationXmProfileIds,
                                    final XmProfileMergedDetails xmProfileMergedDetails) {

        final XmProfileMerged xmProfileMerged =
            domainEventBuilderService.buildXmProfileMerged(sourceXmProfileIds, destinationXmProfileIds,
                xmProfileMergedDetails);
        final XmProfileEvent xmProfileEvent = XmProfileEvent.newBuilder().setXmProfileMerged(xmProfileMerged).build();

        final ProfileEvent event = createXmProfileEvent(xmProfileEvent);

        sendToProfilesKafkaTopic(destinationXmProfileIds.getBrandName(), event);
    }

    public void sendXmProfileDeleted(final XmProfileIds xmProfileIds,
                                     final List<Long> tradingAccounts,
                                     final List<Long> demoAccounts,
                                     final List<Long> walletAccounts) {

        final XmProfileDeleted xmProfileDeleted =
            domainEventBuilderService.buildXmProfileDeleted(xmProfileIds, tradingAccounts, demoAccounts,
                walletAccounts);
        final XmProfileEvent xmProfileEvent = XmProfileEvent.newBuilder().setXmProfileDeleted(xmProfileDeleted).build();

        final ProfileEvent event = createXmProfileEvent(xmProfileEvent);

        sendToProfilesKafkaTopic(xmProfileIds.getBrandName(), event);
    }

    @Override
    public void sendXmProfileAffiliateCreated(final XmProfileIds xmProfileIds,
                                              final BrandAccountV2 brandAccountV2,
                                              final long affiliateId) {

        final XmProfileAffiliateCreated xmProfileAffiliateCreated =
            domainEventBuilderService.buildXmProfileAffiliateCreated(xmProfileIds, brandAccountV2, affiliateId);
        final XmProfileEvent xmProfileEvent =
            XmProfileEvent.newBuilder().setXmProfileAffiliateCreated(xmProfileAffiliateCreated).build();

        final ProfileEvent event = createXmProfileEvent(xmProfileEvent);

        sendToProfilesKafkaTopic(xmProfileIds.getBrandName(), event);
    }

    @Override
    public void sendXmProfileEmailUpdated(final XmProfileIds xmProfileIds, final String updatedEmail) {
        final XmProfileEmailUpdated xmProfileEmailUpdated =
            domainEventBuilderService.buildXmProfileEmailUpdated(xmProfileIds, updatedEmail);

        final XmProfileEvent xmProfileEvent =
            XmProfileEvent.newBuilder().setXmProfileEmailUpdated(xmProfileEmailUpdated).build();

        final ProfileEvent event = createXmProfileEvent(xmProfileEvent);

        sendToProfilesKafkaTopic(xmProfileIds.getBrandName(), event);
    }
    //</editor-fold>

    //<editor-fold desc= "TradingAccount">
    public void sendTradingAccountCreated(final XmProfileIds xmProfileIds,
                                          final TradingAccount tradingAccount) {

        final TradingAccountCreated tradingAccountCreated =
            domainEventBuilderService.buildTradingAccountCreated(xmProfileIds, tradingAccount);
        final TradingAccountEvent tradingAccountEvent =
            TradingAccountEvent.newBuilder().setTradingAccountCreated(tradingAccountCreated).build();
        final ProfileEvent event = createTradingAccountEvent(tradingAccountEvent);

        sendToProfilesKafkaTopic(xmProfileIds.getBrandName(), event);
    }

    public void sendTradingAccountUpdated(final XmProfileIds xmProfileIds,
                                          final TradingAccount tradingAccount) {

        final TradingAccountUpdated tradingAccountUpdated =
            domainEventBuilderService.buildTradingAccountUpdated(xmProfileIds, tradingAccount);
        final TradingAccountEvent tradingAccountEvent =
            TradingAccountEvent.newBuilder().setTradingAccountUpdated(tradingAccountUpdated).build();
        final ProfileEvent event = createTradingAccountEvent(tradingAccountEvent);

        sendToProfilesKafkaTopic(xmProfileIds.getBrandName(), event);
    }

    public void sendTradingAccountClosed(final TradingAccount tradingAccount,
                                         final boolean hasActiveAccounts,
                                         final RiskBasedQuestionsStatus riskBasedQuestionsStatus,
                                         final XmProfileIds xmProfileIds,
                                         final ServerInfo serverInfo) {

        final TradingAccountClosed tradingAccountClosed =
            domainEventBuilderService.buildTradingAccountClosed(tradingAccount, hasActiveAccounts,
                riskBasedQuestionsStatus, xmProfileIds, serverInfo);
        final TradingAccountEvent tradingAccountEvent =
            TradingAccountEvent.newBuilder().setTradingAccountClosed(tradingAccountClosed).build();
        final ProfileEvent event = createTradingAccountEvent(tradingAccountEvent);

        sendToProfilesKafkaTopic(xmProfileIds.getBrandName(), event);
    }

    public void sendTradingAccountDeleted(final XmProfileIds xmProfileIds,
                                          final long login,
                                          final ServerInfo serverInfo) {

        final TradingAccountDeleted tradingAccountDeleted =
            domainEventBuilderService.buildTradingAccountDeleted(xmProfileIds, login, serverInfo);
        final TradingAccountEvent tradingAccountEvent =
            TradingAccountEvent.newBuilder().setTradingAccountDeleted(tradingAccountDeleted).build();
        final ProfileEvent event = createTradingAccountEvent(tradingAccountEvent);

        sendToProfilesKafkaTopic(xmProfileIds.getBrandName(), event);
    }
    //</editor-fold>

    //<editor-fold desc= "WalletAccount">
    public void sendWalletAccountCreated(final XmProfileIds xmProfileIds,
                                         final WalletAccount walletAccount) {

        final WalletAccountCreated walletAccountCreated =
            domainEventBuilderService.buildWalletAccountCreated(xmProfileIds, walletAccount);
        final WalletAccountEvent walletAccountEvent =
            WalletAccountEvent.newBuilder().setWalletAccountCreated(walletAccountCreated).build();
        final ProfileEvent event = createWalletAccountEvent(walletAccountEvent);

        sendToProfilesKafkaTopic(xmProfileIds.getBrandName(), event);
    }

    public void sendWalletAccountUpdated(final XmProfileIds xmProfileIds,
                                         final WalletAccount walletAccount) {

        final WalletAccountUpdated walletAccountUpdated =
            domainEventBuilderService.buildWalletAccountUpdated(xmProfileIds, walletAccount);
        final WalletAccountEvent walletAccountEvent =
            WalletAccountEvent.newBuilder().setWalletAccountUpdated(walletAccountUpdated).build();
        final ProfileEvent event = createWalletAccountEvent(walletAccountEvent);

        sendToProfilesKafkaTopic(xmProfileIds.getBrandName(), event);
    }

    public void sendWalletAccountClosed(final WalletAccount wa,
                                        final XmProfileIds xmProfileIds,
                                        final ServerInfo serverInfo) {

        final WalletAccountClosed walletAccountClosed =
            domainEventBuilderService.buildWalletAccountClosed(wa, xmProfileIds, serverInfo);
        final WalletAccountEvent walletAccountEvent =
            WalletAccountEvent.newBuilder().setWalletAccountClosed(walletAccountClosed).build();
        final ProfileEvent event = createWalletAccountEvent(walletAccountEvent);

        sendToProfilesKafkaTopic(xmProfileIds.getBrandName(), event);
    }

    public void sendWalletAccountDeleted(final XmProfileIds xmProfileIds,
                                         final long login,
                                         final ServerInfo serverInfo) {

        final WalletAccountDeleted walletAccountDeleted =
            domainEventBuilderService.buildWalletAccountDeleted(xmProfileIds, login, serverInfo);
        final WalletAccountEvent walletAccountEvent =
            WalletAccountEvent.newBuilder().setWalletAccountDeleted(walletAccountDeleted).build();
        final ProfileEvent event = createWalletAccountEvent(walletAccountEvent);

        sendToProfilesKafkaTopic(xmProfileIds.getBrandName(), event);
    }
    //</editor-fold>

    //<editor-fold desc= "DemoAccount">
    @Override
    public void sendDemoAccountCreated(final XmProfileIds xmProfileIds, final DemoAccountV2 demoAccount,
                                       final Long affiliateId,
                                       ServerInfo serverInfo) {

        final DemoAccountCreated demoAccountCreated =
            domainEventBuilderService.buildDemoAccountCreated(xmProfileIds, demoAccount, affiliateId, serverInfo);
        final DemoAccountEvent demoAccountEvent =
            DemoAccountEvent.newBuilder().setDemoAccountCreated(demoAccountCreated).build();
        final ProfileEvent event = createDemoAccountEvent(demoAccountEvent);

        sendToProfilesKafkaTopic(xmProfileIds.getBrandName(), event);
    }

    @Override
    public void sendDemoAccountUpdated(final XmProfileIds xmProfileIds, final DemoAccountV2 demoAccount,
                                       ServerInfo serverInfo) {

        final DemoAccountUpdated demoAccountUpdated =
            domainEventBuilderService.buildDemoAccountUpdated(xmProfileIds, demoAccount, serverInfo);
        final DemoAccountEvent demoAccountEvent =
            DemoAccountEvent.newBuilder().setDemoAccountUpdated(demoAccountUpdated).build();
        final ProfileEvent event = createDemoAccountEvent(demoAccountEvent);

        sendToProfilesKafkaTopic(xmProfileIds.getBrandName(), event);
    }

    @Override
    public void sendDemoAccountClosed(final PlatformAccount platformAccount, final XmProfileIds xmProfileIds,
                                      final ServerInfo serverInfo) {

        final DemoAccountClosed demoAccountClosed =
            domainEventBuilderService.buildDemoAccountClosed(platformAccount, xmProfileIds, serverInfo);
        final DemoAccountEvent demoAccountEvent =
            DemoAccountEvent.newBuilder().setDemoAccountClosed(demoAccountClosed).build();
        final ProfileEvent event = createDemoAccountEvent(demoAccountEvent);

        sendToProfilesKafkaTopic(xmProfileIds.getBrandName(), event);
    }

    @Override
    public void sendDemoAccountDeleted(final XmProfileIds xmProfileIds, final long login, final ServerInfo serverInfo) {

        final DemoAccountDeleted demoAccountDeleted =
            domainEventBuilderService.buildDemoAccountDeleted(xmProfileIds, login, serverInfo);
        final DemoAccountEvent demoAccountEvent =
            DemoAccountEvent.newBuilder().setDemoAccountDeleted(demoAccountDeleted).build();
        final ProfileEvent event = createDemoAccountEvent(demoAccountEvent);

        sendToProfilesKafkaTopic(xmProfileIds.getBrandName(), event);
    }
    //</editor-fold>

    //<editor-fold desc= "Comment">
    @Override
    public void sendCommentsSnapshotUpdated(final XmProfileIds xmProfileIds, final List<Comment> comments) {

        final CommentsSnapshotUpdated commentsSnapshotUpdated =
            domainEventBuilderService.buildCommentsSnapshotUpdated(xmProfileIds, comments);
        final CommentEvent commentEvent =
            CommentEvent.newBuilder().setCommentsSnapshotUpdated(commentsSnapshotUpdated).build();
        final ProfileMetadataEvent event = createCommentEvent(commentEvent);

        sendToProfilesMetadataKafkaTopic(event);
    }
    //</editor-fold>

    //<editor-fold desc= "Rating">
    @Override
    public void sendRatingsSnapshotUpdated(final XmProfileIds xmProfileIds, final List<Rating> ratings) {

        final RatingsSnapshotUpdated ratingsSnapshotUpdated =
            domainEventBuilderService.buildRatingsSnapshotUpdated(xmProfileIds, ratings);
        final RatingEvent ratingEvent =
            RatingEvent.newBuilder().setRatingsSnapshotUpdated(ratingsSnapshotUpdated).build();
        final ProfileMetadataEvent event = createRatingEvent(ratingEvent);

        sendToProfilesMetadataKafkaTopic(event);
    }
    //</editor-fold>

    //<editor-fold desc= "Transactions">
    public void sendInternalWalletTransfer(final XmProfileIds xmProfileIds, final DepositResponse depositResponse){

        final Deposit deposit = domainEventBuilderService.buildDeposit(xmProfileIds, depositResponse);
        final TransactionEvent event = createDeposit(deposit);

        sendToTransactionKafkaTopic(event);
    }
    //</editor-fold>
    private void sendToProfilesKafkaTopic(final String brand, final ProfileEvent message) {

        kafkaTemplate.send(profilesEventTopic, brand.toLowerCase(), message);
    }

    private void sendToProfilesMetadataKafkaTopic(final ProfileMetadataEvent message) {

        kafkaTemplate.send(profilesMetadataEventTopic, message);
    }

     private void sendToTransactionKafkaTopic(final TransactionEvent message) {

        kafkaTemplate.send(ewalletTransactionEventTopic, message);
    }

    private ProfileEvent createXmProfileEvent(final XmProfileEvent event) {

        return newProfileMessageEventBuilder().setXmProfileEvent(event).build();
    }

    private ProfileEvent createTradingAccountEvent(final TradingAccountEvent event) {

        return newProfileMessageEventBuilder().setTradingAccountEvent(event).build();
    }

    private ProfileEvent createWalletAccountEvent(final WalletAccountEvent event) {

        return newProfileMessageEventBuilder().setWalletAccountEvent(event).build();
    }

    private ProfileEvent createDemoAccountEvent(final DemoAccountEvent event) {

        return newProfileMessageEventBuilder().setDemoAccountEvent(event).build();
    }

    public ProfileMetadataEvent createCommentEvent(final CommentEvent event) {

        return newProfileMetadaMessageEventBuilder().setCommentEvent(event).build();
    }

    public ProfileMetadataEvent createRatingEvent(final RatingEvent event) {

        return newProfileMetadaMessageEventBuilder().setRatingEvent(event).build();
    }

    public TransactionEvent createDeposit(final Deposit event) {

        return newTransactionMessageEventBuilder().setDeposit(event).build();
    }

    private Builder newProfileMessageEventBuilder() {

        final TraceData traceData = buildTraceData();

        return ProfileEvent
            .newBuilder()
            .setTrace(traceData);
    }

    private ProfileMetadataEvent.Builder newProfileMetadaMessageEventBuilder() {

        final TraceData traceData = buildTraceData();

        return ProfileMetadataEvent
            .newBuilder()
            .setTrace(traceData);
    }

    private TransactionEvent.Builder newTransactionMessageEventBuilder() {

        return TransactionEvent.newBuilder();
    }

    private TraceData buildTraceData() {

        final Instant now = Instant.now();
        final UUID uuid = UUID.randomUUID();
        final Timestamp timestamp = Timestamp
            .newBuilder()
            .setSeconds(now.getEpochSecond())
            .setNanos(now.getNano())
            .build();

        final Uuid build = Uuid
            .newBuilder()
            .setMostSignificantBits(uuid.getMostSignificantBits())
            .setLeastSignificantBits(uuid.getLeastSignificantBits())
            .build();

        return TraceData
            .newBuilder()
            .setId(build)
            .setTimestamp(timestamp)
            .build();
    }

    @Inject
    public DomainEventKafkaSenderServiceImpl(final DomainEventBuilderService domainEventBuilderService,
                                             final KafkaTemplate<String, GeneratedMessage> kafkaTemplate,
                                             @ConfigProperty(name = "profilesEventTopic")
                                             final String profilesEventTopic,
                                             @ConfigProperty(name = "profilesMetadataEventTopic")
                                             final String profilesMetadataEventTopic,
                                             @ConfigProperty(name = "ewalletTransactionEventTopic")
                                             final String ewalletTransactionEventTopic) {

        this.domainEventBuilderService = domainEventBuilderService;
        this.kafkaTemplate = kafkaTemplate;
        this.profilesEventTopic = profilesEventTopic;
        this.profilesMetadataEventTopic = profilesMetadataEventTopic;
        this.ewalletTransactionEventTopic = ewalletTransactionEventTopic;
    }

    private final DomainEventBuilderService domainEventBuilderService;
    private final KafkaTemplate<String, GeneratedMessage> kafkaTemplate;
    private final String profilesEventTopic;
    private final String profilesMetadataEventTopic;
    private final String ewalletTransactionEventTopic;
}
