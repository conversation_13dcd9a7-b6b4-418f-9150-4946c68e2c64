// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: xm/protobuf/services/client_support/chat_events/v1/chat_event.proto
// Protobuf Java Version: 4.26.1

package xm.protobuf.services.client_support.chat_events.v1;

public final class ChatEventOuterClass {
  private ChatEventOuterClass() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 26,
      /* patch= */ 1,
      /* suffix= */ "",
      ChatEventOuterClass.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ChatEventOrBuilder extends
      // @@protoc_insertion_point(interface_extends:xm.protobuf.services.client_support.chat_events.v1.ChatEvent)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * &#47; Event triggered when a chat is updated.
     * </pre>
     *
     * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatUpdatedEvent chat_updated = 1;</code>
     * @return Whether the chatUpdated field is set.
     */
    boolean hasChatUpdated();
    /**
     * <pre>
     * &#47; Event triggered when a chat is updated.
     * </pre>
     *
     * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatUpdatedEvent chat_updated = 1;</code>
     * @return The chatUpdated.
     */
    xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent getChatUpdated();
    /**
     * <pre>
     * &#47; Event triggered when a chat is updated.
     * </pre>
     *
     * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatUpdatedEvent chat_updated = 1;</code>
     */
    xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEventOrBuilder getChatUpdatedOrBuilder();

    /**
     * <pre>
     * &#47; Event triggered when a chat is closed.
     * </pre>
     *
     * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatClosedEvent chat_closed = 2;</code>
     * @return Whether the chatClosed field is set.
     */
    boolean hasChatClosed();
    /**
     * <pre>
     * &#47; Event triggered when a chat is closed.
     * </pre>
     *
     * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatClosedEvent chat_closed = 2;</code>
     * @return The chatClosed.
     */
    xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent getChatClosed();
    /**
     * <pre>
     * &#47; Event triggered when a chat is closed.
     * </pre>
     *
     * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatClosedEvent chat_closed = 2;</code>
     */
    xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEventOrBuilder getChatClosedOrBuilder();

    /**
     * <pre>
     * &#47; Event triggered when a chat is deleted.
     * </pre>
     *
     * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatDeletedEvent chat_deleted = 3;</code>
     * @return Whether the chatDeleted field is set.
     */
    boolean hasChatDeleted();
    /**
     * <pre>
     * &#47; Event triggered when a chat is deleted.
     * </pre>
     *
     * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatDeletedEvent chat_deleted = 3;</code>
     * @return The chatDeleted.
     */
    xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent getChatDeleted();
    /**
     * <pre>
     * &#47; Event triggered when a chat is deleted.
     * </pre>
     *
     * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatDeletedEvent chat_deleted = 3;</code>
     */
    xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEventOrBuilder getChatDeletedOrBuilder();

    /**
     * <pre>
     * &#47; Data for tracing the event flow [optional]
     * </pre>
     *
     * <code>.xm.protobuf.common.TraceData trace = 15;</code>
     * @return Whether the trace field is set.
     */
    boolean hasTrace();
    /**
     * <pre>
     * &#47; Data for tracing the event flow [optional]
     * </pre>
     *
     * <code>.xm.protobuf.common.TraceData trace = 15;</code>
     * @return The trace.
     */
    xm.protobuf.common.TraceDataOuterClass.TraceData getTrace();
    /**
     * <pre>
     * &#47; Data for tracing the event flow [optional]
     * </pre>
     *
     * <code>.xm.protobuf.common.TraceData trace = 15;</code>
     */
    xm.protobuf.common.TraceDataOuterClass.TraceDataOrBuilder getTraceOrBuilder();

    xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatEvent.EventCase getEventCase();
  }
  /**
   * <pre>
   * &#47; Represents an event related to a chat's lifecycle.
   * </pre>
   *
   * Protobuf type {@code xm.protobuf.services.client_support.chat_events.v1.ChatEvent}
   */
  public static final class ChatEvent extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:xm.protobuf.services.client_support.chat_events.v1.ChatEvent)
      ChatEventOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 26,
        /* patch= */ 1,
        /* suffix= */ "",
        ChatEvent.class.getName());
    }
    // Use ChatEvent.newBuilder() to construct.
    private ChatEvent(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private ChatEvent() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.internal_static_xm_protobuf_services_client_support_chat_events_v1_ChatEvent_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.internal_static_xm_protobuf_services_client_support_chat_events_v1_ChatEvent_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatEvent.class, xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatEvent.Builder.class);
    }

    private int bitField0_;
    private int eventCase_ = 0;
    @SuppressWarnings("serial")
    private java.lang.Object event_;
    public enum EventCase
        implements com.google.protobuf.Internal.EnumLite,
            com.google.protobuf.AbstractMessage.InternalOneOfEnum {
      CHAT_UPDATED(1),
      CHAT_CLOSED(2),
      CHAT_DELETED(3),
      EVENT_NOT_SET(0);
      private final int value;
      private EventCase(int value) {
        this.value = value;
      }
      /**
       * @param value The number of the enum to look for.
       * @return The enum associated with the given number.
       * @deprecated Use {@link #forNumber(int)} instead.
       */
      @java.lang.Deprecated
      public static EventCase valueOf(int value) {
        return forNumber(value);
      }

      public static EventCase forNumber(int value) {
        switch (value) {
          case 1: return CHAT_UPDATED;
          case 2: return CHAT_CLOSED;
          case 3: return CHAT_DELETED;
          case 0: return EVENT_NOT_SET;
          default: return null;
        }
      }
      public int getNumber() {
        return this.value;
      }
    };

    public EventCase
    getEventCase() {
      return EventCase.forNumber(
          eventCase_);
    }

    public static final int CHAT_UPDATED_FIELD_NUMBER = 1;
    /**
     * <pre>
     * &#47; Event triggered when a chat is updated.
     * </pre>
     *
     * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatUpdatedEvent chat_updated = 1;</code>
     * @return Whether the chatUpdated field is set.
     */
    @java.lang.Override
    public boolean hasChatUpdated() {
      return eventCase_ == 1;
    }
    /**
     * <pre>
     * &#47; Event triggered when a chat is updated.
     * </pre>
     *
     * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatUpdatedEvent chat_updated = 1;</code>
     * @return The chatUpdated.
     */
    @java.lang.Override
    public xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent getChatUpdated() {
      if (eventCase_ == 1) {
         return (xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent) event_;
      }
      return xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent.getDefaultInstance();
    }
    /**
     * <pre>
     * &#47; Event triggered when a chat is updated.
     * </pre>
     *
     * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatUpdatedEvent chat_updated = 1;</code>
     */
    @java.lang.Override
    public xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEventOrBuilder getChatUpdatedOrBuilder() {
      if (eventCase_ == 1) {
         return (xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent) event_;
      }
      return xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent.getDefaultInstance();
    }

    public static final int CHAT_CLOSED_FIELD_NUMBER = 2;
    /**
     * <pre>
     * &#47; Event triggered when a chat is closed.
     * </pre>
     *
     * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatClosedEvent chat_closed = 2;</code>
     * @return Whether the chatClosed field is set.
     */
    @java.lang.Override
    public boolean hasChatClosed() {
      return eventCase_ == 2;
    }
    /**
     * <pre>
     * &#47; Event triggered when a chat is closed.
     * </pre>
     *
     * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatClosedEvent chat_closed = 2;</code>
     * @return The chatClosed.
     */
    @java.lang.Override
    public xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent getChatClosed() {
      if (eventCase_ == 2) {
         return (xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent) event_;
      }
      return xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent.getDefaultInstance();
    }
    /**
     * <pre>
     * &#47; Event triggered when a chat is closed.
     * </pre>
     *
     * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatClosedEvent chat_closed = 2;</code>
     */
    @java.lang.Override
    public xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEventOrBuilder getChatClosedOrBuilder() {
      if (eventCase_ == 2) {
         return (xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent) event_;
      }
      return xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent.getDefaultInstance();
    }

    public static final int CHAT_DELETED_FIELD_NUMBER = 3;
    /**
     * <pre>
     * &#47; Event triggered when a chat is deleted.
     * </pre>
     *
     * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatDeletedEvent chat_deleted = 3;</code>
     * @return Whether the chatDeleted field is set.
     */
    @java.lang.Override
    public boolean hasChatDeleted() {
      return eventCase_ == 3;
    }
    /**
     * <pre>
     * &#47; Event triggered when a chat is deleted.
     * </pre>
     *
     * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatDeletedEvent chat_deleted = 3;</code>
     * @return The chatDeleted.
     */
    @java.lang.Override
    public xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent getChatDeleted() {
      if (eventCase_ == 3) {
         return (xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent) event_;
      }
      return xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent.getDefaultInstance();
    }
    /**
     * <pre>
     * &#47; Event triggered when a chat is deleted.
     * </pre>
     *
     * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatDeletedEvent chat_deleted = 3;</code>
     */
    @java.lang.Override
    public xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEventOrBuilder getChatDeletedOrBuilder() {
      if (eventCase_ == 3) {
         return (xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent) event_;
      }
      return xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent.getDefaultInstance();
    }

    public static final int TRACE_FIELD_NUMBER = 15;
    private xm.protobuf.common.TraceDataOuterClass.TraceData trace_;
    /**
     * <pre>
     * &#47; Data for tracing the event flow [optional]
     * </pre>
     *
     * <code>.xm.protobuf.common.TraceData trace = 15;</code>
     * @return Whether the trace field is set.
     */
    @java.lang.Override
    public boolean hasTrace() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * &#47; Data for tracing the event flow [optional]
     * </pre>
     *
     * <code>.xm.protobuf.common.TraceData trace = 15;</code>
     * @return The trace.
     */
    @java.lang.Override
    public xm.protobuf.common.TraceDataOuterClass.TraceData getTrace() {
      return trace_ == null ? xm.protobuf.common.TraceDataOuterClass.TraceData.getDefaultInstance() : trace_;
    }
    /**
     * <pre>
     * &#47; Data for tracing the event flow [optional]
     * </pre>
     *
     * <code>.xm.protobuf.common.TraceData trace = 15;</code>
     */
    @java.lang.Override
    public xm.protobuf.common.TraceDataOuterClass.TraceDataOrBuilder getTraceOrBuilder() {
      return trace_ == null ? xm.protobuf.common.TraceDataOuterClass.TraceData.getDefaultInstance() : trace_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (eventCase_ == 1) {
        output.writeMessage(1, (xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent) event_);
      }
      if (eventCase_ == 2) {
        output.writeMessage(2, (xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent) event_);
      }
      if (eventCase_ == 3) {
        output.writeMessage(3, (xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent) event_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(15, getTrace());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (eventCase_ == 1) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, (xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent) event_);
      }
      if (eventCase_ == 2) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, (xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent) event_);
      }
      if (eventCase_ == 3) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, (xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent) event_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(15, getTrace());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatEvent)) {
        return super.equals(obj);
      }
      xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatEvent other = (xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatEvent) obj;

      if (hasTrace() != other.hasTrace()) return false;
      if (hasTrace()) {
        if (!getTrace()
            .equals(other.getTrace())) return false;
      }
      if (!getEventCase().equals(other.getEventCase())) return false;
      switch (eventCase_) {
        case 1:
          if (!getChatUpdated()
              .equals(other.getChatUpdated())) return false;
          break;
        case 2:
          if (!getChatClosed()
              .equals(other.getChatClosed())) return false;
          break;
        case 3:
          if (!getChatDeleted()
              .equals(other.getChatDeleted())) return false;
          break;
        case 0:
        default:
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasTrace()) {
        hash = (37 * hash) + TRACE_FIELD_NUMBER;
        hash = (53 * hash) + getTrace().hashCode();
      }
      switch (eventCase_) {
        case 1:
          hash = (37 * hash) + CHAT_UPDATED_FIELD_NUMBER;
          hash = (53 * hash) + getChatUpdated().hashCode();
          break;
        case 2:
          hash = (37 * hash) + CHAT_CLOSED_FIELD_NUMBER;
          hash = (53 * hash) + getChatClosed().hashCode();
          break;
        case 3:
          hash = (37 * hash) + CHAT_DELETED_FIELD_NUMBER;
          hash = (53 * hash) + getChatDeleted().hashCode();
          break;
        case 0:
        default:
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatEvent parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatEvent parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatEvent parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatEvent parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatEvent parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatEvent parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatEvent parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatEvent parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatEvent parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatEvent parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatEvent parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatEvent parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatEvent prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * &#47; Represents an event related to a chat's lifecycle.
     * </pre>
     *
     * Protobuf type {@code xm.protobuf.services.client_support.chat_events.v1.ChatEvent}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:xm.protobuf.services.client_support.chat_events.v1.ChatEvent)
        xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatEventOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.internal_static_xm_protobuf_services_client_support_chat_events_v1_ChatEvent_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.internal_static_xm_protobuf_services_client_support_chat_events_v1_ChatEvent_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatEvent.class, xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatEvent.Builder.class);
      }

      // Construct using xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatEvent.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getTraceFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        if (chatUpdatedBuilder_ != null) {
          chatUpdatedBuilder_.clear();
        }
        if (chatClosedBuilder_ != null) {
          chatClosedBuilder_.clear();
        }
        if (chatDeletedBuilder_ != null) {
          chatDeletedBuilder_.clear();
        }
        trace_ = null;
        if (traceBuilder_ != null) {
          traceBuilder_.dispose();
          traceBuilder_ = null;
        }
        eventCase_ = 0;
        event_ = null;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.internal_static_xm_protobuf_services_client_support_chat_events_v1_ChatEvent_descriptor;
      }

      @java.lang.Override
      public xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatEvent getDefaultInstanceForType() {
        return xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatEvent.getDefaultInstance();
      }

      @java.lang.Override
      public xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatEvent build() {
        xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatEvent result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatEvent buildPartial() {
        xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatEvent result = new xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatEvent(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        buildPartialOneofs(result);
        onBuilt();
        return result;
      }

      private void buildPartial0(xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatEvent result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.trace_ = traceBuilder_ == null
              ? trace_
              : traceBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      private void buildPartialOneofs(xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatEvent result) {
        result.eventCase_ = eventCase_;
        result.event_ = this.event_;
        if (eventCase_ == 1 &&
            chatUpdatedBuilder_ != null) {
          result.event_ = chatUpdatedBuilder_.build();
        }
        if (eventCase_ == 2 &&
            chatClosedBuilder_ != null) {
          result.event_ = chatClosedBuilder_.build();
        }
        if (eventCase_ == 3 &&
            chatDeletedBuilder_ != null) {
          result.event_ = chatDeletedBuilder_.build();
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatEvent) {
          return mergeFrom((xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatEvent)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatEvent other) {
        if (other == xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatEvent.getDefaultInstance()) return this;
        if (other.hasTrace()) {
          mergeTrace(other.getTrace());
        }
        switch (other.getEventCase()) {
          case CHAT_UPDATED: {
            mergeChatUpdated(other.getChatUpdated());
            break;
          }
          case CHAT_CLOSED: {
            mergeChatClosed(other.getChatClosed());
            break;
          }
          case CHAT_DELETED: {
            mergeChatDeleted(other.getChatDeleted());
            break;
          }
          case EVENT_NOT_SET: {
            break;
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getChatUpdatedFieldBuilder().getBuilder(),
                    extensionRegistry);
                eventCase_ = 1;
                break;
              } // case 10
              case 18: {
                input.readMessage(
                    getChatClosedFieldBuilder().getBuilder(),
                    extensionRegistry);
                eventCase_ = 2;
                break;
              } // case 18
              case 26: {
                input.readMessage(
                    getChatDeletedFieldBuilder().getBuilder(),
                    extensionRegistry);
                eventCase_ = 3;
                break;
              } // case 26
              case 122: {
                input.readMessage(
                    getTraceFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000008;
                break;
              } // case 122
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int eventCase_ = 0;
      private java.lang.Object event_;
      public EventCase
          getEventCase() {
        return EventCase.forNumber(
            eventCase_);
      }

      public Builder clearEvent() {
        eventCase_ = 0;
        event_ = null;
        onChanged();
        return this;
      }

      private int bitField0_;

      private com.google.protobuf.SingleFieldBuilder<
          xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent, xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent.Builder, xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEventOrBuilder> chatUpdatedBuilder_;
      /**
       * <pre>
       * &#47; Event triggered when a chat is updated.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatUpdatedEvent chat_updated = 1;</code>
       * @return Whether the chatUpdated field is set.
       */
      @java.lang.Override
      public boolean hasChatUpdated() {
        return eventCase_ == 1;
      }
      /**
       * <pre>
       * &#47; Event triggered when a chat is updated.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatUpdatedEvent chat_updated = 1;</code>
       * @return The chatUpdated.
       */
      @java.lang.Override
      public xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent getChatUpdated() {
        if (chatUpdatedBuilder_ == null) {
          if (eventCase_ == 1) {
            return (xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent) event_;
          }
          return xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent.getDefaultInstance();
        } else {
          if (eventCase_ == 1) {
            return chatUpdatedBuilder_.getMessage();
          }
          return xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent.getDefaultInstance();
        }
      }
      /**
       * <pre>
       * &#47; Event triggered when a chat is updated.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatUpdatedEvent chat_updated = 1;</code>
       */
      public Builder setChatUpdated(xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent value) {
        if (chatUpdatedBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          event_ = value;
          onChanged();
        } else {
          chatUpdatedBuilder_.setMessage(value);
        }
        eventCase_ = 1;
        return this;
      }
      /**
       * <pre>
       * &#47; Event triggered when a chat is updated.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatUpdatedEvent chat_updated = 1;</code>
       */
      public Builder setChatUpdated(
          xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent.Builder builderForValue) {
        if (chatUpdatedBuilder_ == null) {
          event_ = builderForValue.build();
          onChanged();
        } else {
          chatUpdatedBuilder_.setMessage(builderForValue.build());
        }
        eventCase_ = 1;
        return this;
      }
      /**
       * <pre>
       * &#47; Event triggered when a chat is updated.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatUpdatedEvent chat_updated = 1;</code>
       */
      public Builder mergeChatUpdated(xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent value) {
        if (chatUpdatedBuilder_ == null) {
          if (eventCase_ == 1 &&
              event_ != xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent.getDefaultInstance()) {
            event_ = xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent.newBuilder((xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent) event_)
                .mergeFrom(value).buildPartial();
          } else {
            event_ = value;
          }
          onChanged();
        } else {
          if (eventCase_ == 1) {
            chatUpdatedBuilder_.mergeFrom(value);
          } else {
            chatUpdatedBuilder_.setMessage(value);
          }
        }
        eventCase_ = 1;
        return this;
      }
      /**
       * <pre>
       * &#47; Event triggered when a chat is updated.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatUpdatedEvent chat_updated = 1;</code>
       */
      public Builder clearChatUpdated() {
        if (chatUpdatedBuilder_ == null) {
          if (eventCase_ == 1) {
            eventCase_ = 0;
            event_ = null;
            onChanged();
          }
        } else {
          if (eventCase_ == 1) {
            eventCase_ = 0;
            event_ = null;
          }
          chatUpdatedBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * &#47; Event triggered when a chat is updated.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatUpdatedEvent chat_updated = 1;</code>
       */
      public xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent.Builder getChatUpdatedBuilder() {
        return getChatUpdatedFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * &#47; Event triggered when a chat is updated.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatUpdatedEvent chat_updated = 1;</code>
       */
      @java.lang.Override
      public xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEventOrBuilder getChatUpdatedOrBuilder() {
        if ((eventCase_ == 1) && (chatUpdatedBuilder_ != null)) {
          return chatUpdatedBuilder_.getMessageOrBuilder();
        } else {
          if (eventCase_ == 1) {
            return (xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent) event_;
          }
          return xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent.getDefaultInstance();
        }
      }
      /**
       * <pre>
       * &#47; Event triggered when a chat is updated.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatUpdatedEvent chat_updated = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent, xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent.Builder, xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEventOrBuilder> 
          getChatUpdatedFieldBuilder() {
        if (chatUpdatedBuilder_ == null) {
          if (!(eventCase_ == 1)) {
            event_ = xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent.getDefaultInstance();
          }
          chatUpdatedBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent, xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent.Builder, xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEventOrBuilder>(
                  (xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent) event_,
                  getParentForChildren(),
                  isClean());
          event_ = null;
        }
        eventCase_ = 1;
        onChanged();
        return chatUpdatedBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilder<
          xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent, xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent.Builder, xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEventOrBuilder> chatClosedBuilder_;
      /**
       * <pre>
       * &#47; Event triggered when a chat is closed.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatClosedEvent chat_closed = 2;</code>
       * @return Whether the chatClosed field is set.
       */
      @java.lang.Override
      public boolean hasChatClosed() {
        return eventCase_ == 2;
      }
      /**
       * <pre>
       * &#47; Event triggered when a chat is closed.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatClosedEvent chat_closed = 2;</code>
       * @return The chatClosed.
       */
      @java.lang.Override
      public xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent getChatClosed() {
        if (chatClosedBuilder_ == null) {
          if (eventCase_ == 2) {
            return (xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent) event_;
          }
          return xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent.getDefaultInstance();
        } else {
          if (eventCase_ == 2) {
            return chatClosedBuilder_.getMessage();
          }
          return xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent.getDefaultInstance();
        }
      }
      /**
       * <pre>
       * &#47; Event triggered when a chat is closed.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatClosedEvent chat_closed = 2;</code>
       */
      public Builder setChatClosed(xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent value) {
        if (chatClosedBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          event_ = value;
          onChanged();
        } else {
          chatClosedBuilder_.setMessage(value);
        }
        eventCase_ = 2;
        return this;
      }
      /**
       * <pre>
       * &#47; Event triggered when a chat is closed.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatClosedEvent chat_closed = 2;</code>
       */
      public Builder setChatClosed(
          xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent.Builder builderForValue) {
        if (chatClosedBuilder_ == null) {
          event_ = builderForValue.build();
          onChanged();
        } else {
          chatClosedBuilder_.setMessage(builderForValue.build());
        }
        eventCase_ = 2;
        return this;
      }
      /**
       * <pre>
       * &#47; Event triggered when a chat is closed.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatClosedEvent chat_closed = 2;</code>
       */
      public Builder mergeChatClosed(xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent value) {
        if (chatClosedBuilder_ == null) {
          if (eventCase_ == 2 &&
              event_ != xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent.getDefaultInstance()) {
            event_ = xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent.newBuilder((xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent) event_)
                .mergeFrom(value).buildPartial();
          } else {
            event_ = value;
          }
          onChanged();
        } else {
          if (eventCase_ == 2) {
            chatClosedBuilder_.mergeFrom(value);
          } else {
            chatClosedBuilder_.setMessage(value);
          }
        }
        eventCase_ = 2;
        return this;
      }
      /**
       * <pre>
       * &#47; Event triggered when a chat is closed.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatClosedEvent chat_closed = 2;</code>
       */
      public Builder clearChatClosed() {
        if (chatClosedBuilder_ == null) {
          if (eventCase_ == 2) {
            eventCase_ = 0;
            event_ = null;
            onChanged();
          }
        } else {
          if (eventCase_ == 2) {
            eventCase_ = 0;
            event_ = null;
          }
          chatClosedBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * &#47; Event triggered when a chat is closed.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatClosedEvent chat_closed = 2;</code>
       */
      public xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent.Builder getChatClosedBuilder() {
        return getChatClosedFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * &#47; Event triggered when a chat is closed.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatClosedEvent chat_closed = 2;</code>
       */
      @java.lang.Override
      public xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEventOrBuilder getChatClosedOrBuilder() {
        if ((eventCase_ == 2) && (chatClosedBuilder_ != null)) {
          return chatClosedBuilder_.getMessageOrBuilder();
        } else {
          if (eventCase_ == 2) {
            return (xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent) event_;
          }
          return xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent.getDefaultInstance();
        }
      }
      /**
       * <pre>
       * &#47; Event triggered when a chat is closed.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatClosedEvent chat_closed = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent, xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent.Builder, xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEventOrBuilder> 
          getChatClosedFieldBuilder() {
        if (chatClosedBuilder_ == null) {
          if (!(eventCase_ == 2)) {
            event_ = xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent.getDefaultInstance();
          }
          chatClosedBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent, xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent.Builder, xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEventOrBuilder>(
                  (xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent) event_,
                  getParentForChildren(),
                  isClean());
          event_ = null;
        }
        eventCase_ = 2;
        onChanged();
        return chatClosedBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilder<
          xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent, xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent.Builder, xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEventOrBuilder> chatDeletedBuilder_;
      /**
       * <pre>
       * &#47; Event triggered when a chat is deleted.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatDeletedEvent chat_deleted = 3;</code>
       * @return Whether the chatDeleted field is set.
       */
      @java.lang.Override
      public boolean hasChatDeleted() {
        return eventCase_ == 3;
      }
      /**
       * <pre>
       * &#47; Event triggered when a chat is deleted.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatDeletedEvent chat_deleted = 3;</code>
       * @return The chatDeleted.
       */
      @java.lang.Override
      public xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent getChatDeleted() {
        if (chatDeletedBuilder_ == null) {
          if (eventCase_ == 3) {
            return (xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent) event_;
          }
          return xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent.getDefaultInstance();
        } else {
          if (eventCase_ == 3) {
            return chatDeletedBuilder_.getMessage();
          }
          return xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent.getDefaultInstance();
        }
      }
      /**
       * <pre>
       * &#47; Event triggered when a chat is deleted.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatDeletedEvent chat_deleted = 3;</code>
       */
      public Builder setChatDeleted(xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent value) {
        if (chatDeletedBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          event_ = value;
          onChanged();
        } else {
          chatDeletedBuilder_.setMessage(value);
        }
        eventCase_ = 3;
        return this;
      }
      /**
       * <pre>
       * &#47; Event triggered when a chat is deleted.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatDeletedEvent chat_deleted = 3;</code>
       */
      public Builder setChatDeleted(
          xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent.Builder builderForValue) {
        if (chatDeletedBuilder_ == null) {
          event_ = builderForValue.build();
          onChanged();
        } else {
          chatDeletedBuilder_.setMessage(builderForValue.build());
        }
        eventCase_ = 3;
        return this;
      }
      /**
       * <pre>
       * &#47; Event triggered when a chat is deleted.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatDeletedEvent chat_deleted = 3;</code>
       */
      public Builder mergeChatDeleted(xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent value) {
        if (chatDeletedBuilder_ == null) {
          if (eventCase_ == 3 &&
              event_ != xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent.getDefaultInstance()) {
            event_ = xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent.newBuilder((xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent) event_)
                .mergeFrom(value).buildPartial();
          } else {
            event_ = value;
          }
          onChanged();
        } else {
          if (eventCase_ == 3) {
            chatDeletedBuilder_.mergeFrom(value);
          } else {
            chatDeletedBuilder_.setMessage(value);
          }
        }
        eventCase_ = 3;
        return this;
      }
      /**
       * <pre>
       * &#47; Event triggered when a chat is deleted.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatDeletedEvent chat_deleted = 3;</code>
       */
      public Builder clearChatDeleted() {
        if (chatDeletedBuilder_ == null) {
          if (eventCase_ == 3) {
            eventCase_ = 0;
            event_ = null;
            onChanged();
          }
        } else {
          if (eventCase_ == 3) {
            eventCase_ = 0;
            event_ = null;
          }
          chatDeletedBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * &#47; Event triggered when a chat is deleted.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatDeletedEvent chat_deleted = 3;</code>
       */
      public xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent.Builder getChatDeletedBuilder() {
        return getChatDeletedFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * &#47; Event triggered when a chat is deleted.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatDeletedEvent chat_deleted = 3;</code>
       */
      @java.lang.Override
      public xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEventOrBuilder getChatDeletedOrBuilder() {
        if ((eventCase_ == 3) && (chatDeletedBuilder_ != null)) {
          return chatDeletedBuilder_.getMessageOrBuilder();
        } else {
          if (eventCase_ == 3) {
            return (xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent) event_;
          }
          return xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent.getDefaultInstance();
        }
      }
      /**
       * <pre>
       * &#47; Event triggered when a chat is deleted.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.ChatDeletedEvent chat_deleted = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent, xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent.Builder, xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEventOrBuilder> 
          getChatDeletedFieldBuilder() {
        if (chatDeletedBuilder_ == null) {
          if (!(eventCase_ == 3)) {
            event_ = xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent.getDefaultInstance();
          }
          chatDeletedBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent, xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent.Builder, xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEventOrBuilder>(
                  (xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent) event_,
                  getParentForChildren(),
                  isClean());
          event_ = null;
        }
        eventCase_ = 3;
        onChanged();
        return chatDeletedBuilder_;
      }

      private xm.protobuf.common.TraceDataOuterClass.TraceData trace_;
      private com.google.protobuf.SingleFieldBuilder<
          xm.protobuf.common.TraceDataOuterClass.TraceData, xm.protobuf.common.TraceDataOuterClass.TraceData.Builder, xm.protobuf.common.TraceDataOuterClass.TraceDataOrBuilder> traceBuilder_;
      /**
       * <pre>
       * &#47; Data for tracing the event flow [optional]
       * </pre>
       *
       * <code>.xm.protobuf.common.TraceData trace = 15;</code>
       * @return Whether the trace field is set.
       */
      public boolean hasTrace() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * &#47; Data for tracing the event flow [optional]
       * </pre>
       *
       * <code>.xm.protobuf.common.TraceData trace = 15;</code>
       * @return The trace.
       */
      public xm.protobuf.common.TraceDataOuterClass.TraceData getTrace() {
        if (traceBuilder_ == null) {
          return trace_ == null ? xm.protobuf.common.TraceDataOuterClass.TraceData.getDefaultInstance() : trace_;
        } else {
          return traceBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * &#47; Data for tracing the event flow [optional]
       * </pre>
       *
       * <code>.xm.protobuf.common.TraceData trace = 15;</code>
       */
      public Builder setTrace(xm.protobuf.common.TraceDataOuterClass.TraceData value) {
        if (traceBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          trace_ = value;
        } else {
          traceBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * &#47; Data for tracing the event flow [optional]
       * </pre>
       *
       * <code>.xm.protobuf.common.TraceData trace = 15;</code>
       */
      public Builder setTrace(
          xm.protobuf.common.TraceDataOuterClass.TraceData.Builder builderForValue) {
        if (traceBuilder_ == null) {
          trace_ = builderForValue.build();
        } else {
          traceBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * &#47; Data for tracing the event flow [optional]
       * </pre>
       *
       * <code>.xm.protobuf.common.TraceData trace = 15;</code>
       */
      public Builder mergeTrace(xm.protobuf.common.TraceDataOuterClass.TraceData value) {
        if (traceBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0) &&
            trace_ != null &&
            trace_ != xm.protobuf.common.TraceDataOuterClass.TraceData.getDefaultInstance()) {
            getTraceBuilder().mergeFrom(value);
          } else {
            trace_ = value;
          }
        } else {
          traceBuilder_.mergeFrom(value);
        }
        if (trace_ != null) {
          bitField0_ |= 0x00000008;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * &#47; Data for tracing the event flow [optional]
       * </pre>
       *
       * <code>.xm.protobuf.common.TraceData trace = 15;</code>
       */
      public Builder clearTrace() {
        bitField0_ = (bitField0_ & ~0x00000008);
        trace_ = null;
        if (traceBuilder_ != null) {
          traceBuilder_.dispose();
          traceBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * &#47; Data for tracing the event flow [optional]
       * </pre>
       *
       * <code>.xm.protobuf.common.TraceData trace = 15;</code>
       */
      public xm.protobuf.common.TraceDataOuterClass.TraceData.Builder getTraceBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getTraceFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * &#47; Data for tracing the event flow [optional]
       * </pre>
       *
       * <code>.xm.protobuf.common.TraceData trace = 15;</code>
       */
      public xm.protobuf.common.TraceDataOuterClass.TraceDataOrBuilder getTraceOrBuilder() {
        if (traceBuilder_ != null) {
          return traceBuilder_.getMessageOrBuilder();
        } else {
          return trace_ == null ?
              xm.protobuf.common.TraceDataOuterClass.TraceData.getDefaultInstance() : trace_;
        }
      }
      /**
       * <pre>
       * &#47; Data for tracing the event flow [optional]
       * </pre>
       *
       * <code>.xm.protobuf.common.TraceData trace = 15;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          xm.protobuf.common.TraceDataOuterClass.TraceData, xm.protobuf.common.TraceDataOuterClass.TraceData.Builder, xm.protobuf.common.TraceDataOuterClass.TraceDataOrBuilder> 
          getTraceFieldBuilder() {
        if (traceBuilder_ == null) {
          traceBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              xm.protobuf.common.TraceDataOuterClass.TraceData, xm.protobuf.common.TraceDataOuterClass.TraceData.Builder, xm.protobuf.common.TraceDataOuterClass.TraceDataOrBuilder>(
                  getTrace(),
                  getParentForChildren(),
                  isClean());
          trace_ = null;
        }
        return traceBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:xm.protobuf.services.client_support.chat_events.v1.ChatEvent)
    }

    // @@protoc_insertion_point(class_scope:xm.protobuf.services.client_support.chat_events.v1.ChatEvent)
    private static final xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatEvent DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatEvent();
    }

    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatEvent getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ChatEvent>
        PARSER = new com.google.protobuf.AbstractParser<ChatEvent>() {
      @java.lang.Override
      public ChatEvent parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ChatEvent> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ChatEvent> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatEvent getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ChatUpdatedEventOrBuilder extends
      // @@protoc_insertion_point(interface_extends:xm.protobuf.services.client_support.chat_events.v1.ChatUpdatedEvent)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * &#47; The chat that was updated.
     * </pre>
     *
     * <code>.xm.protobuf.services.client_support.chat_events.v1.Chat chat = 1;</code>
     * @return Whether the chat field is set.
     */
    boolean hasChat();
    /**
     * <pre>
     * &#47; The chat that was updated.
     * </pre>
     *
     * <code>.xm.protobuf.services.client_support.chat_events.v1.Chat chat = 1;</code>
     * @return The chat.
     */
    xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.Chat getChat();
    /**
     * <pre>
     * &#47; The chat that was updated.
     * </pre>
     *
     * <code>.xm.protobuf.services.client_support.chat_events.v1.Chat chat = 1;</code>
     */
    xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.ChatOrBuilder getChatOrBuilder();
  }
  /**
   * <pre>
   * &#47; Contains information about an updated chat.
   * </pre>
   *
   * Protobuf type {@code xm.protobuf.services.client_support.chat_events.v1.ChatUpdatedEvent}
   */
  public static final class ChatUpdatedEvent extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:xm.protobuf.services.client_support.chat_events.v1.ChatUpdatedEvent)
      ChatUpdatedEventOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 26,
        /* patch= */ 1,
        /* suffix= */ "",
        ChatUpdatedEvent.class.getName());
    }
    // Use ChatUpdatedEvent.newBuilder() to construct.
    private ChatUpdatedEvent(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private ChatUpdatedEvent() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.internal_static_xm_protobuf_services_client_support_chat_events_v1_ChatUpdatedEvent_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.internal_static_xm_protobuf_services_client_support_chat_events_v1_ChatUpdatedEvent_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent.class, xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent.Builder.class);
    }

    private int bitField0_;
    public static final int CHAT_FIELD_NUMBER = 1;
    private xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.Chat chat_;
    /**
     * <pre>
     * &#47; The chat that was updated.
     * </pre>
     *
     * <code>.xm.protobuf.services.client_support.chat_events.v1.Chat chat = 1;</code>
     * @return Whether the chat field is set.
     */
    @java.lang.Override
    public boolean hasChat() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * &#47; The chat that was updated.
     * </pre>
     *
     * <code>.xm.protobuf.services.client_support.chat_events.v1.Chat chat = 1;</code>
     * @return The chat.
     */
    @java.lang.Override
    public xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.Chat getChat() {
      return chat_ == null ? xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.Chat.getDefaultInstance() : chat_;
    }
    /**
     * <pre>
     * &#47; The chat that was updated.
     * </pre>
     *
     * <code>.xm.protobuf.services.client_support.chat_events.v1.Chat chat = 1;</code>
     */
    @java.lang.Override
    public xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.ChatOrBuilder getChatOrBuilder() {
      return chat_ == null ? xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.Chat.getDefaultInstance() : chat_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getChat());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getChat());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent)) {
        return super.equals(obj);
      }
      xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent other = (xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent) obj;

      if (hasChat() != other.hasChat()) return false;
      if (hasChat()) {
        if (!getChat()
            .equals(other.getChat())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasChat()) {
        hash = (37 * hash) + CHAT_FIELD_NUMBER;
        hash = (53 * hash) + getChat().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * &#47; Contains information about an updated chat.
     * </pre>
     *
     * Protobuf type {@code xm.protobuf.services.client_support.chat_events.v1.ChatUpdatedEvent}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:xm.protobuf.services.client_support.chat_events.v1.ChatUpdatedEvent)
        xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEventOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.internal_static_xm_protobuf_services_client_support_chat_events_v1_ChatUpdatedEvent_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.internal_static_xm_protobuf_services_client_support_chat_events_v1_ChatUpdatedEvent_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent.class, xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent.Builder.class);
      }

      // Construct using xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getChatFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        chat_ = null;
        if (chatBuilder_ != null) {
          chatBuilder_.dispose();
          chatBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.internal_static_xm_protobuf_services_client_support_chat_events_v1_ChatUpdatedEvent_descriptor;
      }

      @java.lang.Override
      public xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent getDefaultInstanceForType() {
        return xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent.getDefaultInstance();
      }

      @java.lang.Override
      public xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent build() {
        xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent buildPartial() {
        xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent result = new xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.chat_ = chatBuilder_ == null
              ? chat_
              : chatBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent) {
          return mergeFrom((xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent other) {
        if (other == xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent.getDefaultInstance()) return this;
        if (other.hasChat()) {
          mergeChat(other.getChat());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getChatFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.Chat chat_;
      private com.google.protobuf.SingleFieldBuilder<
          xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.Chat, xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.Chat.Builder, xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.ChatOrBuilder> chatBuilder_;
      /**
       * <pre>
       * &#47; The chat that was updated.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.Chat chat = 1;</code>
       * @return Whether the chat field is set.
       */
      public boolean hasChat() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * &#47; The chat that was updated.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.Chat chat = 1;</code>
       * @return The chat.
       */
      public xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.Chat getChat() {
        if (chatBuilder_ == null) {
          return chat_ == null ? xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.Chat.getDefaultInstance() : chat_;
        } else {
          return chatBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * &#47; The chat that was updated.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.Chat chat = 1;</code>
       */
      public Builder setChat(xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.Chat value) {
        if (chatBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          chat_ = value;
        } else {
          chatBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * &#47; The chat that was updated.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.Chat chat = 1;</code>
       */
      public Builder setChat(
          xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.Chat.Builder builderForValue) {
        if (chatBuilder_ == null) {
          chat_ = builderForValue.build();
        } else {
          chatBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * &#47; The chat that was updated.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.Chat chat = 1;</code>
       */
      public Builder mergeChat(xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.Chat value) {
        if (chatBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            chat_ != null &&
            chat_ != xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.Chat.getDefaultInstance()) {
            getChatBuilder().mergeFrom(value);
          } else {
            chat_ = value;
          }
        } else {
          chatBuilder_.mergeFrom(value);
        }
        if (chat_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * &#47; The chat that was updated.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.Chat chat = 1;</code>
       */
      public Builder clearChat() {
        bitField0_ = (bitField0_ & ~0x00000001);
        chat_ = null;
        if (chatBuilder_ != null) {
          chatBuilder_.dispose();
          chatBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * &#47; The chat that was updated.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.Chat chat = 1;</code>
       */
      public xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.Chat.Builder getChatBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getChatFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * &#47; The chat that was updated.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.Chat chat = 1;</code>
       */
      public xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.ChatOrBuilder getChatOrBuilder() {
        if (chatBuilder_ != null) {
          return chatBuilder_.getMessageOrBuilder();
        } else {
          return chat_ == null ?
              xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.Chat.getDefaultInstance() : chat_;
        }
      }
      /**
       * <pre>
       * &#47; The chat that was updated.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.Chat chat = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.Chat, xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.Chat.Builder, xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.ChatOrBuilder> 
          getChatFieldBuilder() {
        if (chatBuilder_ == null) {
          chatBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.Chat, xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.Chat.Builder, xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.ChatOrBuilder>(
                  getChat(),
                  getParentForChildren(),
                  isClean());
          chat_ = null;
        }
        return chatBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:xm.protobuf.services.client_support.chat_events.v1.ChatUpdatedEvent)
    }

    // @@protoc_insertion_point(class_scope:xm.protobuf.services.client_support.chat_events.v1.ChatUpdatedEvent)
    private static final xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent();
    }

    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ChatUpdatedEvent>
        PARSER = new com.google.protobuf.AbstractParser<ChatUpdatedEvent>() {
      @java.lang.Override
      public ChatUpdatedEvent parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ChatUpdatedEvent> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ChatUpdatedEvent> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatUpdatedEvent getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ChatClosedEventOrBuilder extends
      // @@protoc_insertion_point(interface_extends:xm.protobuf.services.client_support.chat_events.v1.ChatClosedEvent)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * &#47; The chat that was closed.
     * </pre>
     *
     * <code>.xm.protobuf.services.client_support.chat_events.v1.Chat chat = 1;</code>
     * @return Whether the chat field is set.
     */
    boolean hasChat();
    /**
     * <pre>
     * &#47; The chat that was closed.
     * </pre>
     *
     * <code>.xm.protobuf.services.client_support.chat_events.v1.Chat chat = 1;</code>
     * @return The chat.
     */
    xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.Chat getChat();
    /**
     * <pre>
     * &#47; The chat that was closed.
     * </pre>
     *
     * <code>.xm.protobuf.services.client_support.chat_events.v1.Chat chat = 1;</code>
     */
    xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.ChatOrBuilder getChatOrBuilder();
  }
  /**
   * <pre>
   * &#47; Contains information about a closed chat.
   * </pre>
   *
   * Protobuf type {@code xm.protobuf.services.client_support.chat_events.v1.ChatClosedEvent}
   */
  public static final class ChatClosedEvent extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:xm.protobuf.services.client_support.chat_events.v1.ChatClosedEvent)
      ChatClosedEventOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 26,
        /* patch= */ 1,
        /* suffix= */ "",
        ChatClosedEvent.class.getName());
    }
    // Use ChatClosedEvent.newBuilder() to construct.
    private ChatClosedEvent(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private ChatClosedEvent() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.internal_static_xm_protobuf_services_client_support_chat_events_v1_ChatClosedEvent_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.internal_static_xm_protobuf_services_client_support_chat_events_v1_ChatClosedEvent_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent.class, xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent.Builder.class);
    }

    private int bitField0_;
    public static final int CHAT_FIELD_NUMBER = 1;
    private xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.Chat chat_;
    /**
     * <pre>
     * &#47; The chat that was closed.
     * </pre>
     *
     * <code>.xm.protobuf.services.client_support.chat_events.v1.Chat chat = 1;</code>
     * @return Whether the chat field is set.
     */
    @java.lang.Override
    public boolean hasChat() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * &#47; The chat that was closed.
     * </pre>
     *
     * <code>.xm.protobuf.services.client_support.chat_events.v1.Chat chat = 1;</code>
     * @return The chat.
     */
    @java.lang.Override
    public xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.Chat getChat() {
      return chat_ == null ? xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.Chat.getDefaultInstance() : chat_;
    }
    /**
     * <pre>
     * &#47; The chat that was closed.
     * </pre>
     *
     * <code>.xm.protobuf.services.client_support.chat_events.v1.Chat chat = 1;</code>
     */
    @java.lang.Override
    public xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.ChatOrBuilder getChatOrBuilder() {
      return chat_ == null ? xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.Chat.getDefaultInstance() : chat_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getChat());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getChat());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent)) {
        return super.equals(obj);
      }
      xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent other = (xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent) obj;

      if (hasChat() != other.hasChat()) return false;
      if (hasChat()) {
        if (!getChat()
            .equals(other.getChat())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasChat()) {
        hash = (37 * hash) + CHAT_FIELD_NUMBER;
        hash = (53 * hash) + getChat().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * &#47; Contains information about a closed chat.
     * </pre>
     *
     * Protobuf type {@code xm.protobuf.services.client_support.chat_events.v1.ChatClosedEvent}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:xm.protobuf.services.client_support.chat_events.v1.ChatClosedEvent)
        xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEventOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.internal_static_xm_protobuf_services_client_support_chat_events_v1_ChatClosedEvent_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.internal_static_xm_protobuf_services_client_support_chat_events_v1_ChatClosedEvent_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent.class, xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent.Builder.class);
      }

      // Construct using xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getChatFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        chat_ = null;
        if (chatBuilder_ != null) {
          chatBuilder_.dispose();
          chatBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.internal_static_xm_protobuf_services_client_support_chat_events_v1_ChatClosedEvent_descriptor;
      }

      @java.lang.Override
      public xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent getDefaultInstanceForType() {
        return xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent.getDefaultInstance();
      }

      @java.lang.Override
      public xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent build() {
        xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent buildPartial() {
        xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent result = new xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.chat_ = chatBuilder_ == null
              ? chat_
              : chatBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent) {
          return mergeFrom((xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent other) {
        if (other == xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent.getDefaultInstance()) return this;
        if (other.hasChat()) {
          mergeChat(other.getChat());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getChatFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.Chat chat_;
      private com.google.protobuf.SingleFieldBuilder<
          xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.Chat, xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.Chat.Builder, xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.ChatOrBuilder> chatBuilder_;
      /**
       * <pre>
       * &#47; The chat that was closed.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.Chat chat = 1;</code>
       * @return Whether the chat field is set.
       */
      public boolean hasChat() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * &#47; The chat that was closed.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.Chat chat = 1;</code>
       * @return The chat.
       */
      public xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.Chat getChat() {
        if (chatBuilder_ == null) {
          return chat_ == null ? xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.Chat.getDefaultInstance() : chat_;
        } else {
          return chatBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * &#47; The chat that was closed.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.Chat chat = 1;</code>
       */
      public Builder setChat(xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.Chat value) {
        if (chatBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          chat_ = value;
        } else {
          chatBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * &#47; The chat that was closed.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.Chat chat = 1;</code>
       */
      public Builder setChat(
          xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.Chat.Builder builderForValue) {
        if (chatBuilder_ == null) {
          chat_ = builderForValue.build();
        } else {
          chatBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * &#47; The chat that was closed.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.Chat chat = 1;</code>
       */
      public Builder mergeChat(xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.Chat value) {
        if (chatBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            chat_ != null &&
            chat_ != xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.Chat.getDefaultInstance()) {
            getChatBuilder().mergeFrom(value);
          } else {
            chat_ = value;
          }
        } else {
          chatBuilder_.mergeFrom(value);
        }
        if (chat_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * &#47; The chat that was closed.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.Chat chat = 1;</code>
       */
      public Builder clearChat() {
        bitField0_ = (bitField0_ & ~0x00000001);
        chat_ = null;
        if (chatBuilder_ != null) {
          chatBuilder_.dispose();
          chatBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * &#47; The chat that was closed.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.Chat chat = 1;</code>
       */
      public xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.Chat.Builder getChatBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getChatFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * &#47; The chat that was closed.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.Chat chat = 1;</code>
       */
      public xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.ChatOrBuilder getChatOrBuilder() {
        if (chatBuilder_ != null) {
          return chatBuilder_.getMessageOrBuilder();
        } else {
          return chat_ == null ?
              xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.Chat.getDefaultInstance() : chat_;
        }
      }
      /**
       * <pre>
       * &#47; The chat that was closed.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.Chat chat = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.Chat, xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.Chat.Builder, xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.ChatOrBuilder> 
          getChatFieldBuilder() {
        if (chatBuilder_ == null) {
          chatBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.Chat, xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.Chat.Builder, xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.ChatOrBuilder>(
                  getChat(),
                  getParentForChildren(),
                  isClean());
          chat_ = null;
        }
        return chatBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:xm.protobuf.services.client_support.chat_events.v1.ChatClosedEvent)
    }

    // @@protoc_insertion_point(class_scope:xm.protobuf.services.client_support.chat_events.v1.ChatClosedEvent)
    private static final xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent();
    }

    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ChatClosedEvent>
        PARSER = new com.google.protobuf.AbstractParser<ChatClosedEvent>() {
      @java.lang.Override
      public ChatClosedEvent parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ChatClosedEvent> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ChatClosedEvent> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatClosedEvent getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ChatDeletedEventOrBuilder extends
      // @@protoc_insertion_point(interface_extends:xm.protobuf.services.client_support.chat_events.v1.ChatDeletedEvent)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * &#47; Unique identifier of the deleted chat.
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return The id.
     */
    java.lang.String getId();
    /**
     * <pre>
     * &#47; Unique identifier of the deleted chat.
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return The bytes for id.
     */
    com.google.protobuf.ByteString
        getIdBytes();

    /**
     * <pre>
     * &#47; Chat service provider type.
     * </pre>
     *
     * <code>.xm.protobuf.services.client_support.chat_events.v1.ProviderType provider = 2;</code>
     * @return The enum numeric value on the wire for provider.
     */
    int getProviderValue();
    /**
     * <pre>
     * &#47; Chat service provider type.
     * </pre>
     *
     * <code>.xm.protobuf.services.client_support.chat_events.v1.ProviderType provider = 2;</code>
     * @return The provider.
     */
    xm.protobuf.services.client_support.chat_events.v1.ProviderTypeOuterClass.ProviderType getProvider();
  }
  /**
   * <pre>
   * &#47; Contains information about a deleted chat.
   * </pre>
   *
   * Protobuf type {@code xm.protobuf.services.client_support.chat_events.v1.ChatDeletedEvent}
   */
  public static final class ChatDeletedEvent extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:xm.protobuf.services.client_support.chat_events.v1.ChatDeletedEvent)
      ChatDeletedEventOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 26,
        /* patch= */ 1,
        /* suffix= */ "",
        ChatDeletedEvent.class.getName());
    }
    // Use ChatDeletedEvent.newBuilder() to construct.
    private ChatDeletedEvent(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private ChatDeletedEvent() {
      id_ = "";
      provider_ = 0;
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.internal_static_xm_protobuf_services_client_support_chat_events_v1_ChatDeletedEvent_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.internal_static_xm_protobuf_services_client_support_chat_events_v1_ChatDeletedEvent_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent.class, xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent.Builder.class);
    }

    public static final int ID_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile java.lang.Object id_ = "";
    /**
     * <pre>
     * &#47; Unique identifier of the deleted chat.
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public java.lang.String getId() {
      java.lang.Object ref = id_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        id_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * &#47; Unique identifier of the deleted chat.
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return The bytes for id.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIdBytes() {
      java.lang.Object ref = id_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        id_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PROVIDER_FIELD_NUMBER = 2;
    private int provider_ = 0;
    /**
     * <pre>
     * &#47; Chat service provider type.
     * </pre>
     *
     * <code>.xm.protobuf.services.client_support.chat_events.v1.ProviderType provider = 2;</code>
     * @return The enum numeric value on the wire for provider.
     */
    @java.lang.Override public int getProviderValue() {
      return provider_;
    }
    /**
     * <pre>
     * &#47; Chat service provider type.
     * </pre>
     *
     * <code>.xm.protobuf.services.client_support.chat_events.v1.ProviderType provider = 2;</code>
     * @return The provider.
     */
    @java.lang.Override public xm.protobuf.services.client_support.chat_events.v1.ProviderTypeOuterClass.ProviderType getProvider() {
      xm.protobuf.services.client_support.chat_events.v1.ProviderTypeOuterClass.ProviderType result = xm.protobuf.services.client_support.chat_events.v1.ProviderTypeOuterClass.ProviderType.forNumber(provider_);
      return result == null ? xm.protobuf.services.client_support.chat_events.v1.ProviderTypeOuterClass.ProviderType.UNRECOGNIZED : result;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(id_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 1, id_);
      }
      if (provider_ != xm.protobuf.services.client_support.chat_events.v1.ProviderTypeOuterClass.ProviderType.PROVIDER_TYPE_UNSPECIFIED.getNumber()) {
        output.writeEnum(2, provider_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(id_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(1, id_);
      }
      if (provider_ != xm.protobuf.services.client_support.chat_events.v1.ProviderTypeOuterClass.ProviderType.PROVIDER_TYPE_UNSPECIFIED.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(2, provider_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent)) {
        return super.equals(obj);
      }
      xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent other = (xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent) obj;

      if (!getId()
          .equals(other.getId())) return false;
      if (provider_ != other.provider_) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId().hashCode();
      hash = (37 * hash) + PROVIDER_FIELD_NUMBER;
      hash = (53 * hash) + provider_;
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * &#47; Contains information about a deleted chat.
     * </pre>
     *
     * Protobuf type {@code xm.protobuf.services.client_support.chat_events.v1.ChatDeletedEvent}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:xm.protobuf.services.client_support.chat_events.v1.ChatDeletedEvent)
        xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEventOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.internal_static_xm_protobuf_services_client_support_chat_events_v1_ChatDeletedEvent_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.internal_static_xm_protobuf_services_client_support_chat_events_v1_ChatDeletedEvent_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent.class, xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent.Builder.class);
      }

      // Construct using xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        id_ = "";
        provider_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.internal_static_xm_protobuf_services_client_support_chat_events_v1_ChatDeletedEvent_descriptor;
      }

      @java.lang.Override
      public xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent getDefaultInstanceForType() {
        return xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent.getDefaultInstance();
      }

      @java.lang.Override
      public xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent build() {
        xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent buildPartial() {
        xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent result = new xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.id_ = id_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.provider_ = provider_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent) {
          return mergeFrom((xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent other) {
        if (other == xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent.getDefaultInstance()) return this;
        if (!other.getId().isEmpty()) {
          id_ = other.id_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        if (other.provider_ != 0) {
          setProviderValue(other.getProviderValue());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                id_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 16: {
                provider_ = input.readEnum();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object id_ = "";
      /**
       * <pre>
       * &#47; Unique identifier of the deleted chat.
       * </pre>
       *
       * <code>string id = 1;</code>
       * @return The id.
       */
      public java.lang.String getId() {
        java.lang.Object ref = id_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          id_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * &#47; Unique identifier of the deleted chat.
       * </pre>
       *
       * <code>string id = 1;</code>
       * @return The bytes for id.
       */
      public com.google.protobuf.ByteString
          getIdBytes() {
        java.lang.Object ref = id_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          id_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * &#47; Unique identifier of the deleted chat.
       * </pre>
       *
       * <code>string id = 1;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        id_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * &#47; Unique identifier of the deleted chat.
       * </pre>
       *
       * <code>string id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        id_ = getDefaultInstance().getId();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * &#47; Unique identifier of the deleted chat.
       * </pre>
       *
       * <code>string id = 1;</code>
       * @param value The bytes for id to set.
       * @return This builder for chaining.
       */
      public Builder setIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        id_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      private int provider_ = 0;
      /**
       * <pre>
       * &#47; Chat service provider type.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.ProviderType provider = 2;</code>
       * @return The enum numeric value on the wire for provider.
       */
      @java.lang.Override public int getProviderValue() {
        return provider_;
      }
      /**
       * <pre>
       * &#47; Chat service provider type.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.ProviderType provider = 2;</code>
       * @param value The enum numeric value on the wire for provider to set.
       * @return This builder for chaining.
       */
      public Builder setProviderValue(int value) {
        provider_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * &#47; Chat service provider type.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.ProviderType provider = 2;</code>
       * @return The provider.
       */
      @java.lang.Override
      public xm.protobuf.services.client_support.chat_events.v1.ProviderTypeOuterClass.ProviderType getProvider() {
        xm.protobuf.services.client_support.chat_events.v1.ProviderTypeOuterClass.ProviderType result = xm.protobuf.services.client_support.chat_events.v1.ProviderTypeOuterClass.ProviderType.forNumber(provider_);
        return result == null ? xm.protobuf.services.client_support.chat_events.v1.ProviderTypeOuterClass.ProviderType.UNRECOGNIZED : result;
      }
      /**
       * <pre>
       * &#47; Chat service provider type.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.ProviderType provider = 2;</code>
       * @param value The provider to set.
       * @return This builder for chaining.
       */
      public Builder setProvider(xm.protobuf.services.client_support.chat_events.v1.ProviderTypeOuterClass.ProviderType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000002;
        provider_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * &#47; Chat service provider type.
       * </pre>
       *
       * <code>.xm.protobuf.services.client_support.chat_events.v1.ProviderType provider = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearProvider() {
        bitField0_ = (bitField0_ & ~0x00000002);
        provider_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:xm.protobuf.services.client_support.chat_events.v1.ChatDeletedEvent)
    }

    // @@protoc_insertion_point(class_scope:xm.protobuf.services.client_support.chat_events.v1.ChatDeletedEvent)
    private static final xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent();
    }

    public static xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ChatDeletedEvent>
        PARSER = new com.google.protobuf.AbstractParser<ChatDeletedEvent>() {
      @java.lang.Override
      public ChatDeletedEvent parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ChatDeletedEvent> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ChatDeletedEvent> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public xm.protobuf.services.client_support.chat_events.v1.ChatEventOuterClass.ChatDeletedEvent getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_xm_protobuf_services_client_support_chat_events_v1_ChatEvent_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_xm_protobuf_services_client_support_chat_events_v1_ChatEvent_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_xm_protobuf_services_client_support_chat_events_v1_ChatUpdatedEvent_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_xm_protobuf_services_client_support_chat_events_v1_ChatUpdatedEvent_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_xm_protobuf_services_client_support_chat_events_v1_ChatClosedEvent_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_xm_protobuf_services_client_support_chat_events_v1_ChatClosedEvent_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_xm_protobuf_services_client_support_chat_events_v1_ChatDeletedEvent_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_xm_protobuf_services_client_support_chat_events_v1_ChatDeletedEvent_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\nCxm/protobuf/services/client_support/ch" +
      "at_events/v1/chat_event.proto\0222xm.protob" +
      "uf.services.client_support.chat_events.v" +
      "1\032#xm/protobuf/common/trace_data.proto\032=" +
      "xm/protobuf/services/client_support/chat" +
      "_events/v1/chat.proto\032Fxm/protobuf/servi" +
      "ces/client_support/chat_events/v1/provid" +
      "er_type.proto\"\332\002\n\tChatEvent\022\\\n\014chat_upda" +
      "ted\030\001 \001(\0132D.xm.protobuf.services.client_" +
      "support.chat_events.v1.ChatUpdatedEventH" +
      "\000\022Z\n\013chat_closed\030\002 \001(\0132C.xm.protobuf.ser" +
      "vices.client_support.chat_events.v1.Chat" +
      "ClosedEventH\000\022\\\n\014chat_deleted\030\003 \001(\0132D.xm" +
      ".protobuf.services.client_support.chat_e" +
      "vents.v1.ChatDeletedEventH\000\022,\n\005trace\030\017 \001" +
      "(\0132\035.xm.protobuf.common.TraceDataB\007\n\005eve" +
      "nt\"Z\n\020ChatUpdatedEvent\022F\n\004chat\030\001 \001(\01328.x" +
      "m.protobuf.services.client_support.chat_" +
      "events.v1.Chat\"Y\n\017ChatClosedEvent\022F\n\004cha" +
      "t\030\001 \001(\01328.xm.protobuf.services.client_su" +
      "pport.chat_events.v1.Chat\"r\n\020ChatDeleted" +
      "Event\022\n\n\002id\030\001 \001(\t\022R\n\010provider\030\002 \001(\0162@.xm" +
      ".protobuf.services.client_support.chat_e" +
      "vents.v1.ProviderTypeB\232\001Z\227\001gitlab.xm.com" +
      "/xlDevs/proto_contracts/generated/go/xm." +
      "protobuf.services.client_support.chat_ev" +
      "ents.v1/xm/protobuf/services/client_supp" +
      "ort/chat_events/v1b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          xm.protobuf.common.TraceDataOuterClass.getDescriptor(),
          xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.getDescriptor(),
          xm.protobuf.services.client_support.chat_events.v1.ProviderTypeOuterClass.getDescriptor(),
        });
    internal_static_xm_protobuf_services_client_support_chat_events_v1_ChatEvent_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_xm_protobuf_services_client_support_chat_events_v1_ChatEvent_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_xm_protobuf_services_client_support_chat_events_v1_ChatEvent_descriptor,
        new java.lang.String[] { "ChatUpdated", "ChatClosed", "ChatDeleted", "Trace", "Event", });
    internal_static_xm_protobuf_services_client_support_chat_events_v1_ChatUpdatedEvent_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_xm_protobuf_services_client_support_chat_events_v1_ChatUpdatedEvent_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_xm_protobuf_services_client_support_chat_events_v1_ChatUpdatedEvent_descriptor,
        new java.lang.String[] { "Chat", });
    internal_static_xm_protobuf_services_client_support_chat_events_v1_ChatClosedEvent_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_xm_protobuf_services_client_support_chat_events_v1_ChatClosedEvent_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_xm_protobuf_services_client_support_chat_events_v1_ChatClosedEvent_descriptor,
        new java.lang.String[] { "Chat", });
    internal_static_xm_protobuf_services_client_support_chat_events_v1_ChatDeletedEvent_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_xm_protobuf_services_client_support_chat_events_v1_ChatDeletedEvent_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_xm_protobuf_services_client_support_chat_events_v1_ChatDeletedEvent_descriptor,
        new java.lang.String[] { "Id", "Provider", });
    descriptor.resolveAllFeaturesImmutable();
    xm.protobuf.common.TraceDataOuterClass.getDescriptor();
    xm.protobuf.services.client_support.chat_events.v1.ChatOuterClass.getDescriptor();
    xm.protobuf.services.client_support.chat_events.v1.ProviderTypeOuterClass.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
