package com.xm.dal.integrationtest.utils.data;

import com.xm.dal.domain.AddressType;
import com.xm.dal.domain.Brand;
import com.xm.dal.domain.CallbackRequestStatus;
import com.xm.dal.domain.Country;
import com.xm.dal.domain.Language;
import com.xm.dal.domain.LeadPhone;
import com.xm.dal.domain.Name;
import com.xm.dal.domain.Profile;
import com.xm.dal.domain.ProfileStatus;
import com.xm.dal.domain.RegistrationStatus;
import com.xm.dal.domain.account.AccountManager;
import com.xm.dal.domain.account.AccountStatus;
import com.xm.dal.domain.account.AccountType;
import com.xm.dal.domain.account.DemoAccount;
import com.xm.dal.domain.account.trading.LoginActivity;
import com.xm.dal.domain.account.trading.PlatformType;
import com.xm.dal.domain.account.trading.ServerInfo;
import com.xm.dal.domain.account.trading.TradingAccount;
import com.xm.dal.repository.elastic.entity.Address;
import com.xm.dal.repository.elastic.entity.AddressDetails;
import com.xm.dal.repository.elastic.entity.Affiliate;
import com.xm.dal.repository.elastic.entity.Author;
import com.xm.dal.repository.elastic.entity.AutoRebate;
import com.xm.dal.repository.elastic.entity.CallbackRequest;
import com.xm.dal.repository.elastic.entity.Comment;
import com.xm.dal.repository.elastic.entity.Confirmation;
import com.xm.dal.repository.elastic.entity.EducationAccount;
import com.xm.dal.repository.elastic.entity.FinancialInfo;
import com.xm.dal.repository.elastic.entity.FirstStep;
import com.xm.dal.repository.elastic.entity.Label;
import com.xm.dal.repository.elastic.entity.LatestLeadInfo;
import com.xm.dal.repository.elastic.entity.LeadData;
import com.xm.dal.repository.elastic.entity.Phone;
import com.xm.dal.repository.elastic.entity.PhoneCallsInfo;
import com.xm.dal.repository.elastic.entity.PhoneType;
import com.xm.dal.repository.elastic.entity.ProfileData;
import com.xm.dal.repository.elastic.entity.ProfileDocument;
import com.xm.dal.repository.elastic.entity.RafInfo;
import com.xm.dal.repository.elastic.entity.Rating;
import com.xm.dal.repository.elastic.entity.SecondStep;
import com.xm.dal.repository.elastic.entity.SimpleName;
import com.xm.dal.repository.elastic.entity.StreetType;
import com.xm.dal.repository.elastic.entity.ValidationStatus;
import com.xm.dal.repository.elastic.entity.WalletAccount;
import com.xm.dal.repository.elastic.entity.WebinarAccount;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-02T10:20:17+0300",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.6 (Amazon.com Inc.)"
)
@Component
public class ProfileMapperImpl implements ProfileMapper {

    @Override
    public ProfileDocument convert(Profile profile) {
        if ( profile == null ) {
            return null;
        }

        ProfileDocument.ProfileDocumentBuilder profileDocument = ProfileDocument.builder();

        profileDocument.email( profile.email() );
        profileDocument.brand( brandToBrand( profile.brand() ) );
        profileDocument.profileId( profile.profileId() );
        profileDocument.brandAccountId( profile.brandAccountId() );
        profileDocument.leadId( profile.leadId() );
        profileDocument.profileStatus( profileStatusToProfileStatus( profile.profileStatus() ) );
        profileDocument.profileData( profileDataToProfileData( profile.profileData() ) );
        profileDocument.tradingAccounts( tradingAccountListToTradingAccountList( profile.tradingAccounts() ) );
        profileDocument.demoAccounts( demoAccountListToDemoAccountList( profile.demoAccounts() ) );
        profileDocument.educationAccounts( educationAccountListToEducationAccountList( profile.educationAccounts() ) );
        profileDocument.webinarAccounts( webinarAccountListToWebinarAccountList( profile.webinarAccounts() ) );
        profileDocument.walletAccounts( walletAccountListToWalletAccountList( profile.walletAccounts() ) );
        profileDocument.firstSteps( firstStepListToFirstStepList( profile.firstSteps() ) );
        profileDocument.secondSteps( secondStepListToSecondStepList( profile.secondSteps() ) );
        profileDocument.validationStatus( validationStatusToValidationStatus( profile.validationStatus() ) );
        profileDocument.phoneCallsInfo( phoneCallsInfoToPhoneCallsInfo( profile.phoneCallsInfo() ) );
        profileDocument.financialInfo( financialInfoToFinancialInfo( profile.financialInfo() ) );
        profileDocument.rafInfo( rafInfoToRafInfo( profile.rafInfo() ) );
        profileDocument.labels( labelListToLabelList( profile.labels() ) );
        profileDocument.ratings( ratingListToRatingList( profile.ratings() ) );
        profileDocument.comments( commentListToCommentList( profile.comments() ) );
        profileDocument.sat( profile.sat() );
        profileDocument.isProfessionalClient( profile.isProfessionalClient() );
        profileDocument.hasActiveTradingAccounts( profile.hasActiveTradingAccounts() );
        profileDocument.latestEmailDate( profile.latestEmailDate() );
        profileDocument.latestTradeDate( profile.latestTradeDate() );
        profileDocument.confirmations( confirmationToConfirmation( profile.confirmations() ) );
        profileDocument.accountManager( accountManagerToAccountManager( profile.accountManager() ) );
        profileDocument.affiliate( affiliateToAffiliate( profile.affiliate() ) );
        profileDocument.affiliateManager( accountManagerToAccountManager( profile.affiliateManager() ) );
        profileDocument.leadData( leadDataToLeadData( profile.leadData() ) );
        profileDocument.callbackRequests( callbackRequestListToCallbackRequestList( profile.callbackRequests() ) );

        return profileDocument.build();
    }

    protected com.xm.dal.repository.elastic.entity.Brand brandToBrand(Brand brand) {
        if ( brand == null ) {
            return null;
        }

        com.xm.dal.repository.elastic.entity.Brand.BrandBuilder brand1 = com.xm.dal.repository.elastic.entity.Brand.builder();

        brand1.brandId( brand.brandId() );
        brand1.brandName( brand.brandName() );

        return brand1.build();
    }

    protected com.xm.dal.repository.elastic.entity.ProfileStatus profileStatusToProfileStatus(ProfileStatus profileStatus) {
        if ( profileStatus == null ) {
            return null;
        }

        com.xm.dal.repository.elastic.entity.ProfileStatus profileStatus1;

        switch ( profileStatus ) {
            case BRAND_ACCOUNT: profileStatus1 = com.xm.dal.repository.elastic.entity.ProfileStatus.BRAND_ACCOUNT;
            break;
            case LEAD: profileStatus1 = com.xm.dal.repository.elastic.entity.ProfileStatus.LEAD;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + profileStatus );
        }

        return profileStatus1;
    }

    protected com.xm.dal.repository.elastic.entity.RegistrationStatus registrationStatusToRegistrationStatus(RegistrationStatus registrationStatus) {
        if ( registrationStatus == null ) {
            return null;
        }

        com.xm.dal.repository.elastic.entity.RegistrationStatus registrationStatus1;

        switch ( registrationStatus ) {
            case PENDING: registrationStatus1 = com.xm.dal.repository.elastic.entity.RegistrationStatus.PENDING;
            break;
            case COMPLETED: registrationStatus1 = com.xm.dal.repository.elastic.entity.RegistrationStatus.COMPLETED;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + registrationStatus );
        }

        return registrationStatus1;
    }

    protected com.xm.dal.repository.elastic.entity.Country countryToCountry(Country country) {
        if ( country == null ) {
            return null;
        }

        com.xm.dal.repository.elastic.entity.Country.CountryBuilder country1 = com.xm.dal.repository.elastic.entity.Country.builder();

        country1.code( country.code() );
        country1.name( country.name() );

        return country1.build();
    }

    protected com.xm.dal.repository.elastic.entity.Language languageToLanguage(Language language) {
        if ( language == null ) {
            return null;
        }

        com.xm.dal.repository.elastic.entity.Language.LanguageBuilder language1 = com.xm.dal.repository.elastic.entity.Language.builder();

        language1.code( language.code() );
        language1.name( language.name() );

        return language1.build();
    }

    protected com.xm.dal.repository.elastic.entity.Name nameToName(Name name) {
        if ( name == null ) {
            return null;
        }

        com.xm.dal.repository.elastic.entity.Name.NameBuilder name1 = com.xm.dal.repository.elastic.entity.Name.builder();

        name1.firstName( name.firstName() );
        name1.lastName( name.lastName() );
        name1.middleName( name.middleName() );
        name1.title( name.title() );
        name1.nativeFirstName( name.nativeFirstName() );
        name1.nativeLastName( name.nativeLastName() );

        return name1.build();
    }

    protected com.xm.dal.repository.elastic.entity.AddressType addressTypeToAddressType(AddressType addressType) {
        if ( addressType == null ) {
            return null;
        }

        com.xm.dal.repository.elastic.entity.AddressType.AddressTypeBuilder addressType1 = com.xm.dal.repository.elastic.entity.AddressType.builder();

        addressType1.id( addressType.id() );
        addressType1.description( addressType.description() );

        return addressType1.build();
    }

    protected StreetType streetTypeToStreetType(com.xm.dal.domain.StreetType streetType) {
        if ( streetType == null ) {
            return null;
        }

        StreetType.StreetTypeBuilder streetType1 = StreetType.builder();

        streetType1.type( streetType.type() );
        streetType1.abbreviatedType( streetType.abbreviatedType() );

        return streetType1.build();
    }

    protected AddressDetails addressDetailsToAddressDetails(com.xm.dal.domain.AddressDetails addressDetails) {
        if ( addressDetails == null ) {
            return null;
        }

        AddressDetails.AddressDetailsBuilder addressDetails1 = AddressDetails.builder();

        addressDetails1.zipCode( addressDetails.zipCode() );
        addressDetails1.state( addressDetails.state() );
        addressDetails1.province( addressDetails.province() );
        addressDetails1.poBox( addressDetails.poBox() );
        addressDetails1.streetType( streetTypeToStreetType( addressDetails.streetType() ) );
        addressDetails1.streetName( addressDetails.streetName() );
        addressDetails1.streetNumber( addressDetails.streetNumber() );
        addressDetails1.buildingName( addressDetails.buildingName() );
        addressDetails1.buildingNumber( addressDetails.buildingNumber() );

        return addressDetails1.build();
    }

    protected Address addressToAddress(com.xm.dal.domain.Address address) {
        if ( address == null ) {
            return null;
        }

        Address.AddressBuilder address1 = Address.builder();

        address1.addressType( addressTypeToAddressType( address.addressType() ) );
        address1.addressFull( address.addressFull() );
        address1.isVerified( address.isVerified() );
        address1.city( address.city() );
        address1.country( countryToCountry( address.country() ) );
        address1.details( addressDetailsToAddressDetails( address.details() ) );

        return address1.build();
    }

    protected PhoneType phoneTypeToPhoneType(com.xm.dal.domain.PhoneType phoneType) {
        if ( phoneType == null ) {
            return null;
        }

        PhoneType.PhoneTypeBuilder phoneType1 = PhoneType.builder();

        phoneType1.id( phoneType.id() );
        phoneType1.description( phoneType.description() );

        return phoneType1.build();
    }

    protected Phone phoneToPhone(com.xm.dal.domain.Phone phone) {
        if ( phone == null ) {
            return null;
        }

        Phone.PhoneBuilder phone1 = Phone.builder();

        phone1.phoneId( phone.phoneId() );
        phone1.phoneNumber( phone.phoneNumber() );
        phone1.phoneType( phoneTypeToPhoneType( phone.phoneType() ) );
        phone1.priorityOrder( phone.priorityOrder() );
        phone1.isVerified( phone.isVerified() );
        phone1.regionCode( phone.regionCode() );
        phone1.prefix( phone.prefix() );
        phone1.local( phone.local() );
        phone1.international( phone.international() );
        phone1.processingType( phone.processingType() );

        return phone1.build();
    }

    protected List<Phone> phoneListToPhoneList(List<com.xm.dal.domain.Phone> list) {
        if ( list == null ) {
            return null;
        }

        List<Phone> list1 = new ArrayList<Phone>( list.size() );
        for ( com.xm.dal.domain.Phone phone : list ) {
            list1.add( phoneToPhone( phone ) );
        }

        return list1;
    }

    protected ProfileData profileDataToProfileData(com.xm.dal.domain.ProfileData profileData) {
        if ( profileData == null ) {
            return null;
        }

        ProfileData.ProfileDataBuilder profileData1 = ProfileData.builder();

        profileData1.registrationDate( profileData.registrationDate() );
        profileData1.registrationStatus( registrationStatusToRegistrationStatus( profileData.registrationStatus() ) );
        profileData1.country( countryToCountry( profileData.country() ) );
        profileData1.preferredLanguage( languageToLanguage( profileData.preferredLanguage() ) );
        profileData1.dateOfBirth( profileData.dateOfBirth() );
        profileData1.name( nameToName( profileData.name() ) );
        profileData1.address( addressToAddress( profileData.address() ) );
        profileData1.phones( phoneListToPhoneList( profileData.phones() ) );
        profileData1.lastUpdateDate( profileData.lastUpdateDate() );

        return profileData1.build();
    }

    protected com.xm.dal.repository.elastic.entity.AccountStatus accountStatusToAccountStatus(AccountStatus accountStatus) {
        if ( accountStatus == null ) {
            return null;
        }

        com.xm.dal.repository.elastic.entity.AccountStatus accountStatus1;

        switch ( accountStatus ) {
            case ACTIVE: accountStatus1 = com.xm.dal.repository.elastic.entity.AccountStatus.ACTIVE;
            break;
            case ARCHIVED: accountStatus1 = com.xm.dal.repository.elastic.entity.AccountStatus.ARCHIVED;
            break;
            case CLOSED: accountStatus1 = com.xm.dal.repository.elastic.entity.AccountStatus.CLOSED;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + accountStatus );
        }

        return accountStatus1;
    }

    protected com.xm.dal.repository.elastic.entity.PlatformType platformTypeToPlatformType(PlatformType platformType) {
        if ( platformType == null ) {
            return null;
        }

        com.xm.dal.repository.elastic.entity.PlatformType platformType1;

        switch ( platformType ) {
            case MT4: platformType1 = com.xm.dal.repository.elastic.entity.PlatformType.MT4;
            break;
            case MT5: platformType1 = com.xm.dal.repository.elastic.entity.PlatformType.MT5;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + platformType );
        }

        return platformType1;
    }

    protected com.xm.dal.repository.elastic.entity.ServerInfo serverInfoToServerInfo(ServerInfo serverInfo) {
        if ( serverInfo == null ) {
            return null;
        }

        com.xm.dal.repository.elastic.entity.ServerInfo.ServerInfoBuilder serverInfo1 = com.xm.dal.repository.elastic.entity.ServerInfo.builder();

        serverInfo1.serverId( serverInfo.serverId() );
        serverInfo1.serverName( serverInfo.serverName() );
        serverInfo1.serverType( serverInfo.serverType() );
        serverInfo1.platformType( platformTypeToPlatformType( serverInfo.platformType() ) );
        serverInfo1.serverLoginName( serverInfo.serverLoginName() );

        return serverInfo1.build();
    }

    protected com.xm.dal.repository.elastic.entity.AccountType accountTypeToAccountType(AccountType accountType) {
        if ( accountType == null ) {
            return null;
        }

        com.xm.dal.repository.elastic.entity.AccountType.AccountTypeBuilder accountType1 = com.xm.dal.repository.elastic.entity.AccountType.builder();

        accountType1.id( accountType.id() );
        accountType1.name( accountType.name() );

        return accountType1.build();
    }

    protected com.xm.dal.repository.elastic.entity.AccountManager accountManagerToAccountManager(AccountManager accountManager) {
        if ( accountManager == null ) {
            return null;
        }

        com.xm.dal.repository.elastic.entity.AccountManager.AccountManagerBuilder accountManager1 = com.xm.dal.repository.elastic.entity.AccountManager.builder();

        accountManager1.id( accountManager.id() );
        accountManager1.firstName( accountManager.firstName() );
        accountManager1.lastName( accountManager.lastName() );
        accountManager1.email( accountManager.email() );
        accountManager1.office( accountManager.office() );

        return accountManager1.build();
    }

    protected Affiliate affiliateToAffiliate(com.xm.dal.domain.account.trading.Affiliate affiliate) {
        if ( affiliate == null ) {
            return null;
        }

        Affiliate.AffiliateBuilder affiliate1 = Affiliate.builder();

        affiliate1.id( affiliate.id() );
        affiliate1.firstName( affiliate.firstName() );
        affiliate1.lastName( affiliate.lastName() );

        return affiliate1.build();
    }

    protected AutoRebate autoRebateToAutoRebate(com.xm.dal.domain.account.trading.AutoRebate autoRebate) {
        if ( autoRebate == null ) {
            return null;
        }

        AutoRebate.AutoRebateBuilder autoRebate1 = AutoRebate.builder();

        autoRebate1.isEligible( autoRebate.isEligible() );
        autoRebate1.hasAccepted( autoRebate.hasAccepted() );
        autoRebate1.acceptedDate( autoRebate.acceptedDate() );

        return autoRebate1.build();
    }

    protected com.xm.dal.repository.elastic.entity.LoginActivity loginActivityToLoginActivity(LoginActivity loginActivity) {
        if ( loginActivity == null ) {
            return null;
        }

        com.xm.dal.repository.elastic.entity.LoginActivity.LoginActivityBuilder loginActivity1 = com.xm.dal.repository.elastic.entity.LoginActivity.builder();

        loginActivity1.membersAreaLastLogin( loginActivity.membersAreaLastLogin() );
        loginActivity1.mobileLastLogin( loginActivity.mobileLastLogin() );
        loginActivity1.mqTerminalLastLogin( loginActivity.mqTerminalLastLogin() );
        loginActivity1.webTraderLastLogin( loginActivity.webTraderLastLogin() );

        return loginActivity1.build();
    }

    protected com.xm.dal.repository.elastic.entity.TradingAccount tradingAccountToTradingAccount(TradingAccount tradingAccount) {
        if ( tradingAccount == null ) {
            return null;
        }

        com.xm.dal.repository.elastic.entity.TradingAccount.TradingAccountBuilder<?, ?> tradingAccount1 = com.xm.dal.repository.elastic.entity.TradingAccount.builder();

        tradingAccount1.login( tradingAccount.login() );
        tradingAccount1.currency( tradingAccount.currency() );
        tradingAccount1.registrationDate( tradingAccount.registrationDate() );
        tradingAccount1.registrationIp( tradingAccount.registrationIp() );
        tradingAccount1.registrationIpCountry( countryToCountry( tradingAccount.registrationIpCountry() ) );
        tradingAccount1.accountStatus( accountStatusToAccountStatus( tradingAccount.accountStatus() ) );
        tradingAccount1.isEnabled( tradingAccount.isEnabled() );
        tradingAccount1.isReadOnly( tradingAccount.isReadOnly() );
        tradingAccount1.sendReports( tradingAccount.sendReports() );
        tradingAccount1.archivedDate( tradingAccount.archivedDate() );
        tradingAccount1.closedDate( tradingAccount.closedDate() );
        tradingAccount1.gid( tradingAccount.gid() );
        tradingAccount1.source( tradingAccount.source() );
        tradingAccount1.gwClickDate( tradingAccount.gwClickDate() );
        tradingAccount1.serverInfo( serverInfoToServerInfo( tradingAccount.serverInfo() ) );
        tradingAccount1.accountType( accountTypeToAccountType( tradingAccount.accountType() ) );
        tradingAccount1.accountManager( accountManagerToAccountManager( tradingAccount.accountManager() ) );
        tradingAccount1.affiliate( affiliateToAffiliate( tradingAccount.affiliate() ) );
        tradingAccount1.affiliateManager( accountManagerToAccountManager( tradingAccount.affiliateManager() ) );
        tradingAccount1.comment( tradingAccount.comment() );
        tradingAccount1.leverage( tradingAccount.leverage() );
        tradingAccount1.socialTradingStatus( tradingAccount.socialTradingStatus() );
        tradingAccount1.groupName( tradingAccount.groupName() );
        tradingAccount1.accountName( tradingAccount.accountName() );
        tradingAccount1.bonusTermsAgreed( tradingAccount.bonusTermsAgreed() );
        tradingAccount1.autoRebate( autoRebateToAutoRebate( tradingAccount.autoRebate() ) );
        tradingAccount1.loginActivity( loginActivityToLoginActivity( tradingAccount.loginActivity() ) );
        tradingAccount1.isIslamic( tradingAccount.isIslamic() );

        return tradingAccount1.build();
    }

    protected List<com.xm.dal.repository.elastic.entity.TradingAccount> tradingAccountListToTradingAccountList(List<TradingAccount> list) {
        if ( list == null ) {
            return null;
        }

        List<com.xm.dal.repository.elastic.entity.TradingAccount> list1 = new ArrayList<com.xm.dal.repository.elastic.entity.TradingAccount>( list.size() );
        for ( TradingAccount tradingAccount : list ) {
            list1.add( tradingAccountToTradingAccount( tradingAccount ) );
        }

        return list1;
    }

    protected SimpleName simpleNameToSimpleName(com.xm.dal.domain.SimpleName simpleName) {
        if ( simpleName == null ) {
            return null;
        }

        SimpleName.SimpleNameBuilder simpleName1 = SimpleName.builder();

        simpleName1.firstName( simpleName.firstName() );
        simpleName1.lastName( simpleName.lastName() );
        simpleName1.middleName( simpleName.middleName() );

        return simpleName1.build();
    }

    protected com.xm.dal.repository.elastic.entity.LeadPhone leadPhoneToLeadPhone(LeadPhone leadPhone) {
        if ( leadPhone == null ) {
            return null;
        }

        com.xm.dal.repository.elastic.entity.LeadPhone.LeadPhoneBuilder leadPhone1 = com.xm.dal.repository.elastic.entity.LeadPhone.builder();

        leadPhone1.phoneNumber( leadPhone.phoneNumber() );
        leadPhone1.regionCode( leadPhone.regionCode() );
        leadPhone1.prefix( leadPhone.prefix() );
        leadPhone1.local( leadPhone.local() );
        leadPhone1.international( leadPhone.international() );
        leadPhone1.processingType( leadPhone.processingType() );

        return leadPhone1.build();
    }

    protected com.xm.dal.repository.elastic.entity.DemoAccount demoAccountToDemoAccount(DemoAccount demoAccount) {
        if ( demoAccount == null ) {
            return null;
        }

        com.xm.dal.repository.elastic.entity.DemoAccount.DemoAccountBuilder<?, ?> demoAccount1 = com.xm.dal.repository.elastic.entity.DemoAccount.builder();

        demoAccount1.login( demoAccount.login() );
        demoAccount1.currency( demoAccount.currency() );
        demoAccount1.registrationDate( demoAccount.registrationDate() );
        demoAccount1.registrationIp( demoAccount.registrationIp() );
        demoAccount1.registrationIpCountry( countryToCountry( demoAccount.registrationIpCountry() ) );
        demoAccount1.accountStatus( accountStatusToAccountStatus( demoAccount.accountStatus() ) );
        demoAccount1.isEnabled( demoAccount.isEnabled() );
        demoAccount1.isReadOnly( demoAccount.isReadOnly() );
        demoAccount1.sendReports( demoAccount.sendReports() );
        demoAccount1.archivedDate( demoAccount.archivedDate() );
        demoAccount1.closedDate( demoAccount.closedDate() );
        demoAccount1.gid( demoAccount.gid() );
        demoAccount1.source( demoAccount.source() );
        demoAccount1.gwClickDate( demoAccount.gwClickDate() );
        demoAccount1.serverInfo( serverInfoToServerInfo( demoAccount.serverInfo() ) );
        demoAccount1.accountType( accountTypeToAccountType( demoAccount.accountType() ) );
        demoAccount1.accountManager( accountManagerToAccountManager( demoAccount.accountManager() ) );
        demoAccount1.affiliate( affiliateToAffiliate( demoAccount.affiliate() ) );
        demoAccount1.affiliateManager( accountManagerToAccountManager( demoAccount.affiliateManager() ) );
        demoAccount1.comment( demoAccount.comment() );
        demoAccount1.leverage( demoAccount.leverage() );
        demoAccount1.socialTradingStatus( demoAccount.socialTradingStatus() );
        demoAccount1.groupName( demoAccount.groupName() );
        demoAccount1.accountName( demoAccount.accountName() );
        demoAccount1.simCountry( countryToCountry( demoAccount.simCountry() ) );
        demoAccount1.name( simpleNameToSimpleName( demoAccount.name() ) );
        demoAccount1.phone( leadPhoneToLeadPhone( demoAccount.phone() ) );
        demoAccount1.country( countryToCountry( demoAccount.country() ) );
        demoAccount1.preferredLanguage( languageToLanguage( demoAccount.preferredLanguage() ) );

        return demoAccount1.build();
    }

    protected List<com.xm.dal.repository.elastic.entity.DemoAccount> demoAccountListToDemoAccountList(List<DemoAccount> list) {
        if ( list == null ) {
            return null;
        }

        List<com.xm.dal.repository.elastic.entity.DemoAccount> list1 = new ArrayList<com.xm.dal.repository.elastic.entity.DemoAccount>( list.size() );
        for ( DemoAccount demoAccount : list ) {
            list1.add( demoAccountToDemoAccount( demoAccount ) );
        }

        return list1;
    }

    protected EducationAccount educationAccountToEducationAccount(com.xm.dal.domain.account.EducationAccount educationAccount) {
        if ( educationAccount == null ) {
            return null;
        }

        EducationAccount.EducationAccountBuilder educationAccount1 = EducationAccount.builder();

        educationAccount1.id( educationAccount.id() );
        educationAccount1.name( simpleNameToSimpleName( educationAccount.name() ) );
        educationAccount1.phone( leadPhoneToLeadPhone( educationAccount.phone() ) );
        educationAccount1.country( countryToCountry( educationAccount.country() ) );
        educationAccount1.accountManager( accountManagerToAccountManager( educationAccount.accountManager() ) );
        educationAccount1.registrationDate( educationAccount.registrationDate() );
        educationAccount1.acceptedContact( educationAccount.acceptedContact() );

        return educationAccount1.build();
    }

    protected List<EducationAccount> educationAccountListToEducationAccountList(List<com.xm.dal.domain.account.EducationAccount> list) {
        if ( list == null ) {
            return null;
        }

        List<EducationAccount> list1 = new ArrayList<EducationAccount>( list.size() );
        for ( com.xm.dal.domain.account.EducationAccount educationAccount : list ) {
            list1.add( educationAccountToEducationAccount( educationAccount ) );
        }

        return list1;
    }

    protected WebinarAccount webinarAccountToWebinarAccount(com.xm.dal.domain.account.WebinarAccount webinarAccount) {
        if ( webinarAccount == null ) {
            return null;
        }

        WebinarAccount.WebinarAccountBuilder webinarAccount1 = WebinarAccount.builder();

        webinarAccount1.id( webinarAccount.id() );
        webinarAccount1.name( simpleNameToSimpleName( webinarAccount.name() ) );
        webinarAccount1.phone( leadPhoneToLeadPhone( webinarAccount.phone() ) );
        webinarAccount1.country( countryToCountry( webinarAccount.country() ) );
        webinarAccount1.city( webinarAccount.city() );
        webinarAccount1.accountManager( accountManagerToAccountManager( webinarAccount.accountManager() ) );
        webinarAccount1.affiliate( affiliateToAffiliate( webinarAccount.affiliate() ) );
        webinarAccount1.affiliateManager( accountManagerToAccountManager( webinarAccount.affiliateManager() ) );
        webinarAccount1.registrationDate( webinarAccount.registrationDate() );

        return webinarAccount1.build();
    }

    protected List<WebinarAccount> webinarAccountListToWebinarAccountList(List<com.xm.dal.domain.account.WebinarAccount> list) {
        if ( list == null ) {
            return null;
        }

        List<WebinarAccount> list1 = new ArrayList<WebinarAccount>( list.size() );
        for ( com.xm.dal.domain.account.WebinarAccount webinarAccount : list ) {
            list1.add( webinarAccountToWebinarAccount( webinarAccount ) );
        }

        return list1;
    }

    protected WalletAccount walletAccountToWalletAccount(com.xm.dal.domain.account.WalletAccount walletAccount) {
        if ( walletAccount == null ) {
            return null;
        }

        WalletAccount.WalletAccountBuilder walletAccount1 = WalletAccount.builder();

        walletAccount1.login( walletAccount.login() );
        walletAccount1.brand( brandToBrand( walletAccount.brand() ) );
        walletAccount1.currency( walletAccount.currency() );
        walletAccount1.registrationDate( walletAccount.registrationDate() );
        walletAccount1.registrationIp( walletAccount.registrationIp() );
        walletAccount1.registrationIpCountry( countryToCountry( walletAccount.registrationIpCountry() ) );
        walletAccount1.accountStatus( accountStatusToAccountStatus( walletAccount.accountStatus() ) );
        walletAccount1.isEnabled( walletAccount.isEnabled() );
        walletAccount1.isReadOnly( walletAccount.isReadOnly() );
        walletAccount1.sendReports( walletAccount.sendReports() );
        walletAccount1.promoAgreed( walletAccount.promoAgreed() );
        walletAccount1.archivedDate( walletAccount.archivedDate() );
        walletAccount1.source( walletAccount.source() );
        walletAccount1.serverInfo( serverInfoToServerInfo( walletAccount.serverInfo() ) );
        walletAccount1.accountManager( accountManagerToAccountManager( walletAccount.accountManager() ) );
        walletAccount1.affiliateId( walletAccount.affiliateId() );
        walletAccount1.comment( walletAccount.comment() );
        walletAccount1.groupName( walletAccount.groupName() );

        return walletAccount1.build();
    }

    protected List<WalletAccount> walletAccountListToWalletAccountList(List<com.xm.dal.domain.account.WalletAccount> list) {
        if ( list == null ) {
            return null;
        }

        List<WalletAccount> list1 = new ArrayList<WalletAccount>( list.size() );
        for ( com.xm.dal.domain.account.WalletAccount walletAccount : list ) {
            list1.add( walletAccountToWalletAccount( walletAccount ) );
        }

        return list1;
    }

    protected FirstStep firstStepToFirstStep(com.xm.dal.domain.account.FirstStep firstStep) {
        if ( firstStep == null ) {
            return null;
        }

        FirstStep.FirstStepBuilder firstStep1 = FirstStep.builder();

        firstStep1.id( firstStep.id() );
        firstStep1.name( simpleNameToSimpleName( firstStep.name() ) );
        firstStep1.phone( leadPhoneToLeadPhone( firstStep.phone() ) );
        firstStep1.country( countryToCountry( firstStep.country() ) );
        firstStep1.preferredLanguage( languageToLanguage( firstStep.preferredLanguage() ) );
        firstStep1.accountManager( accountManagerToAccountManager( firstStep.accountManager() ) );
        firstStep1.affiliate( affiliateToAffiliate( firstStep.affiliate() ) );
        firstStep1.affiliateManager( accountManagerToAccountManager( firstStep.affiliateManager() ) );
        firstStep1.registrationDate( firstStep.registrationDate() );

        return firstStep1.build();
    }

    protected List<FirstStep> firstStepListToFirstStepList(List<com.xm.dal.domain.account.FirstStep> list) {
        if ( list == null ) {
            return null;
        }

        List<FirstStep> list1 = new ArrayList<FirstStep>( list.size() );
        for ( com.xm.dal.domain.account.FirstStep firstStep : list ) {
            list1.add( firstStepToFirstStep( firstStep ) );
        }

        return list1;
    }

    protected SecondStep secondStepToSecondStep(com.xm.dal.domain.account.SecondStep secondStep) {
        if ( secondStep == null ) {
            return null;
        }

        SecondStep.SecondStepBuilder secondStep1 = SecondStep.builder();

        secondStep1.id( secondStep.id() );
        secondStep1.name( simpleNameToSimpleName( secondStep.name() ) );
        secondStep1.phone( leadPhoneToLeadPhone( secondStep.phone() ) );
        secondStep1.country( countryToCountry( secondStep.country() ) );
        secondStep1.city( secondStep.city() );
        secondStep1.dateOfBirth( secondStep.dateOfBirth() );
        secondStep1.preferredLanguage( languageToLanguage( secondStep.preferredLanguage() ) );
        secondStep1.accountManager( accountManagerToAccountManager( secondStep.accountManager() ) );
        secondStep1.affiliate( affiliateToAffiliate( secondStep.affiliate() ) );
        secondStep1.affiliateManager( accountManagerToAccountManager( secondStep.affiliateManager() ) );
        secondStep1.registrationDate( secondStep.registrationDate() );

        return secondStep1.build();
    }

    protected List<SecondStep> secondStepListToSecondStepList(List<com.xm.dal.domain.account.SecondStep> list) {
        if ( list == null ) {
            return null;
        }

        List<SecondStep> list1 = new ArrayList<SecondStep>( list.size() );
        for ( com.xm.dal.domain.account.SecondStep secondStep : list ) {
            list1.add( secondStepToSecondStep( secondStep ) );
        }

        return list1;
    }

    protected ValidationStatus validationStatusToValidationStatus(com.xm.dal.domain.ValidationStatus validationStatus) {
        if ( validationStatus == null ) {
            return null;
        }

        ValidationStatus.ValidationStatusBuilder validationStatus1 = ValidationStatus.builder();

        validationStatus1.documentExpirationDate( validationStatus.documentExpirationDate() );
        validationStatus1.isValidated( validationStatus.isValidated() );
        validationStatus1.isRevalidationRequested( validationStatus.isRevalidationRequested() );
        validationStatus1.validationDate( validationStatus.validationDate() );

        return validationStatus1.build();
    }

    protected PhoneCallsInfo phoneCallsInfoToPhoneCallsInfo(com.xm.dal.domain.PhoneCallsInfo phoneCallsInfo) {
        if ( phoneCallsInfo == null ) {
            return null;
        }

        PhoneCallsInfo.PhoneCallsInfoBuilder phoneCallsInfo1 = PhoneCallsInfo.builder();

        phoneCallsInfo1.latestValidPhoneCallDate( phoneCallsInfo.latestValidPhoneCallDate() );
        phoneCallsInfo1.latestInvalidPhoneCallDate( phoneCallsInfo.latestInvalidPhoneCallDate() );
        phoneCallsInfo1.latestPhoneCallAttemptDate( phoneCallsInfo.latestPhoneCallAttemptDate() );
        phoneCallsInfo1.totalPhoneCallAttempts( phoneCallsInfo.totalPhoneCallAttempts() );
        phoneCallsInfo1.totalInvalidPhoneCalls( phoneCallsInfo.totalInvalidPhoneCalls() );
        phoneCallsInfo1.totalValidBeforeClientActivation( phoneCallsInfo.totalValidBeforeClientActivation() );
        phoneCallsInfo1.totalInvalidBeforeClientActivation( phoneCallsInfo.totalInvalidBeforeClientActivation() );
        phoneCallsInfo1.totalAttemptsBeforeClientActivation( phoneCallsInfo.totalAttemptsBeforeClientActivation() );
        phoneCallsInfo1.totalOtherCommunicatedPhoneCalls( phoneCallsInfo.totalOtherCommunicatedPhoneCalls() );

        return phoneCallsInfo1.build();
    }

    protected FinancialInfo financialInfoToFinancialInfo(com.xm.dal.domain.FinancialInfo financialInfo) {
        if ( financialInfo == null ) {
            return null;
        }

        FinancialInfo.FinancialInfoBuilder financialInfo1 = FinancialInfo.builder();

        financialInfo1.depositCount( financialInfo.depositCount() );
        financialInfo1.firstDepositDate( financialInfo.firstDepositDate() );
        financialInfo1.latestDepositDate( financialInfo.latestDepositDate() );
        financialInfo1.latestMonthPnlEur( financialInfo.latestMonthPnlEur() );
        financialInfo1.totalBalanceEur( financialInfo.totalBalanceEur() );
        financialInfo1.totalCreditEur( financialInfo.totalCreditEur() );
        financialInfo1.totalDepositsEur( financialInfo.totalDepositsEur() );
        financialInfo1.totalEquityEur( financialInfo.totalEquityEur() );
        financialInfo1.totalPnlEur( financialInfo.totalPnlEur() );
        financialInfo1.totalWithdrawalsEur( financialInfo.totalWithdrawalsEur() );

        return financialInfo1.build();
    }

    protected RafInfo rafInfoToRafInfo(com.xm.dal.domain.RafInfo rafInfo) {
        if ( rafInfo == null ) {
            return null;
        }

        RafInfo.RafInfoBuilder rafInfo1 = RafInfo.builder();

        rafInfo1.isReferrer( rafInfo.isReferrer() );
        rafInfo1.isReferredClient( rafInfo.isReferredClient() );
        rafInfo1.isRafQualified( rafInfo.isRafQualified() );
        rafInfo1.connectionStatusId( rafInfo.connectionStatusId() );
        rafInfo1.connectionStatusDesc( rafInfo.connectionStatusDesc() );
        rafInfo1.referrerToken( rafInfo.referrerToken() );

        return rafInfo1.build();
    }

    protected Label labelToLabel(com.xm.dal.domain.Label label) {
        if ( label == null ) {
            return null;
        }

        Label.LabelBuilder label1 = Label.builder();

        label1.id( label.id() );
        label1.name( label.name() );
        label1.categoryColor( label.categoryColor() );
        label1.isActive( label.isActive() );

        return label1.build();
    }

    protected List<Label> labelListToLabelList(List<com.xm.dal.domain.Label> list) {
        if ( list == null ) {
            return null;
        }

        List<Label> list1 = new ArrayList<Label>( list.size() );
        for ( com.xm.dal.domain.Label label : list ) {
            list1.add( labelToLabel( label ) );
        }

        return list1;
    }

    protected Author authorToAuthor(com.xm.dal.domain.Author author) {
        if ( author == null ) {
            return null;
        }

        Author.AuthorBuilder author1 = Author.builder();

        author1.ldapId( author.ldapId() );
        author1.firstName( author.firstName() );
        author1.lastName( author.lastName() );

        return author1.build();
    }

    protected Rating ratingToRating(com.xm.dal.domain.Rating rating) {
        if ( rating == null ) {
            return null;
        }

        Rating.RatingBuilder rating1 = Rating.builder();

        rating1.id( rating.id() );
        rating1.description( rating.description() );
        rating1.dateAdded( rating.dateAdded() );
        rating1.author( authorToAuthor( rating.author() ) );

        return rating1.build();
    }

    protected List<Rating> ratingListToRatingList(List<com.xm.dal.domain.Rating> list) {
        if ( list == null ) {
            return null;
        }

        List<Rating> list1 = new ArrayList<Rating>( list.size() );
        for ( com.xm.dal.domain.Rating rating : list ) {
            list1.add( ratingToRating( rating ) );
        }

        return list1;
    }

    protected Comment commentToComment(com.xm.dal.domain.Comment comment) {
        if ( comment == null ) {
            return null;
        }

        Comment.CommentBuilder comment1 = Comment.builder();

        comment1.id( comment.id() );
        comment1.text( comment.text() );
        comment1.dateAdded( comment.dateAdded() );
        comment1.relatedLogin( comment.relatedLogin() );
        comment1.author( authorToAuthor( comment.author() ) );

        return comment1.build();
    }

    protected List<Comment> commentListToCommentList(List<com.xm.dal.domain.Comment> list) {
        if ( list == null ) {
            return null;
        }

        List<Comment> list1 = new ArrayList<Comment>( list.size() );
        for ( com.xm.dal.domain.Comment comment : list ) {
            list1.add( commentToComment( comment ) );
        }

        return list1;
    }

    protected Confirmation confirmationToConfirmation(com.xm.dal.domain.Confirmation confirmation) {
        if ( confirmation == null ) {
            return null;
        }

        Confirmation.ConfirmationBuilder confirmation1 = Confirmation.builder();

        confirmation1.promoAgreed( confirmation.promoAgreed() );

        return confirmation1.build();
    }

    protected LatestLeadInfo latestLeadInfoToLatestLeadInfo(com.xm.dal.domain.LatestLeadInfo latestLeadInfo) {
        if ( latestLeadInfo == null ) {
            return null;
        }

        LatestLeadInfo.LatestLeadInfoBuilder latestLeadInfo1 = LatestLeadInfo.builder();

        latestLeadInfo1.name( simpleNameToSimpleName( latestLeadInfo.name() ) );
        latestLeadInfo1.phone( leadPhoneToLeadPhone( latestLeadInfo.phone() ) );
        latestLeadInfo1.country( countryToCountry( latestLeadInfo.country() ) );
        latestLeadInfo1.preferredLanguage( languageToLanguage( latestLeadInfo.preferredLanguage() ) );
        latestLeadInfo1.registrationDate( latestLeadInfo.registrationDate() );
        latestLeadInfo1.comment( commentToComment( latestLeadInfo.comment() ) );
        latestLeadInfo1.ratings( ratingListToRatingList( latestLeadInfo.ratings() ) );

        return latestLeadInfo1.build();
    }

    protected LeadData leadDataToLeadData(com.xm.dal.domain.LeadData leadData) {
        if ( leadData == null ) {
            return null;
        }

        LeadData.LeadDataBuilder leadData1 = LeadData.builder();

        leadData1.latestInfo( latestLeadInfoToLatestLeadInfo( leadData.latestInfo() ) );

        return leadData1.build();
    }

    protected com.xm.dal.repository.elastic.entity.CallbackRequestStatus callbackRequestStatusToCallbackRequestStatus(CallbackRequestStatus callbackRequestStatus) {
        if ( callbackRequestStatus == null ) {
            return null;
        }

        com.xm.dal.repository.elastic.entity.CallbackRequestStatus callbackRequestStatus1;

        switch ( callbackRequestStatus ) {
            case CREATED: callbackRequestStatus1 = com.xm.dal.repository.elastic.entity.CallbackRequestStatus.CREATED;
            break;
            case COMPLETED: callbackRequestStatus1 = com.xm.dal.repository.elastic.entity.CallbackRequestStatus.COMPLETED;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + callbackRequestStatus );
        }

        return callbackRequestStatus1;
    }

    protected CallbackRequest callbackRequestToCallbackRequest(com.xm.dal.domain.CallbackRequest callbackRequest) {
        if ( callbackRequest == null ) {
            return null;
        }

        CallbackRequest.CallbackRequestBuilder callbackRequest1 = CallbackRequest.builder();

        callbackRequest1.id( callbackRequest.id() );
        callbackRequest1.conversationTopic( callbackRequest.conversationTopic() );
        callbackRequest1.status( callbackRequestStatusToCallbackRequestStatus( callbackRequest.status() ) );
        callbackRequest1.creationDate( callbackRequest.creationDate() );
        callbackRequest1.completionDate( callbackRequest.completionDate() );

        return callbackRequest1.build();
    }

    protected List<CallbackRequest> callbackRequestListToCallbackRequestList(List<com.xm.dal.domain.CallbackRequest> list) {
        if ( list == null ) {
            return null;
        }

        List<CallbackRequest> list1 = new ArrayList<CallbackRequest>( list.size() );
        for ( com.xm.dal.domain.CallbackRequest callbackRequest : list ) {
            list1.add( callbackRequestToCallbackRequest( callbackRequest ) );
        }

        return list1;
    }
}
