package com.xm.dal.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.model.Generation;
import org.springframework.ai.chat.messages.AssistantMessage;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Unit tests for NaturalLanguageQueryService.
 */
@ExtendWith(MockitoExtension.class)
class EsNlqServiceTest {

    @Mock
    private ChatModel chatModel;

    @Mock
    private ChatResponse chatResponse;

    @Mock
    private Generation generation;

    @Mock
    private AssistantMessage assistantMessage;

    private EsNlqService esNlqService;

    @BeforeEach
    void setUp() {
        esNlqService = new EsNlqService(chatModel);
    }

    @Test
    void serviceCanBeCreated() {
        // Test that the service can be instantiated
        assertNotNull(esNlqService);
    }

    @Test
    void getExampleQueries_ShouldReturnExamples() {
        // When
        var examples = esNlqService.getExampleQueries();

        // Then
        assertNotNull(examples);
        assertFalse(examples.isEmpty());
        assertTrue(examples.containsKey("Find all active users"));
    }

    @Test
    void translateToElasticsearchQuery_ShouldHandleValidResponse() {
        // Given
        String naturalQuery = "Find all active users";
        String expectedEsQuery = "{\"term\": {\"status\": \"active\"}}";

        when(chatModel.call(any())).thenReturn(chatResponse);
        when(chatResponse.getResult()).thenReturn(generation);
        when(generation.getOutput()).thenReturn(assistantMessage);
        when(assistantMessage.getText()).thenReturn(expectedEsQuery);

        // When
        String result = esNlqService.translateToElasticsearchQuery(naturalQuery);

        // Then
        assertNotNull(result);
        assertEquals(expectedEsQuery, result);
        verify(chatModel).call(any());
    }

    @Test
    void translateToElasticsearchQuery_ShouldHandleMarkdownResponse() {
        // Given
        String naturalQuery = "Find users named John";
        String markdownResponse = "```json\n{\"match\": {\"name\": \"John\"}}\n```";
        String expectedCleanedQuery = "{\"match\": {\"name\": \"John\"}}";

        when(chatModel.call(any())).thenReturn(chatResponse);
        when(chatResponse.getResult()).thenReturn(generation);
        when(generation.getOutput()).thenReturn(assistantMessage);
        when(assistantMessage.getText()).thenReturn(markdownResponse);

        // When
        String result = esNlqService.translateToElasticsearchQuery(naturalQuery);

        // Then
        assertNotNull(result);
        assertEquals(expectedCleanedQuery, result);
    }

    @Test
    void translateToElasticsearchQuery_ShouldHandleInvalidResponse() {
        // Given
        String naturalQuery = "Find users";
        String invalidResponse = "This is not JSON";

        when(chatModel.call(any())).thenReturn(chatResponse);
        when(chatResponse.getResult()).thenReturn(generation);
        when(generation.getOutput()).thenReturn(assistantMessage);
        when(assistantMessage.getText()).thenReturn(invalidResponse);

        // When
        String result = esNlqService.translateToElasticsearchQuery(naturalQuery);

        // Then
        assertNotNull(result);
        assertEquals("{}", result); // Should return empty query for invalid response
    }
}
