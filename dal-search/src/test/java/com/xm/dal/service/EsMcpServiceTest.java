package com.xm.dal.service;

import com.xm.dal.config.McpClientConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import io.modelcontextprotocol.client.McpSyncClient;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for ElasticsearchMcpService.
 */
@ExtendWith(MockitoExtension.class)
class EsMcpServiceTest {

    @Mock
    private McpClientConfig mcpClientConfig;

    @Mock
    private McpSyncClient elasticsearchMcpSyncClient;

    private EsMcpService esMcpService;

    @BeforeEach
    void setUp() {
        esMcpService = new EsMcpService(mcpClientConfig);
        when(mcpClientConfig.getElasticsearchMcpSyncClient()).thenReturn(elasticsearchMcpSyncClient);
    }

    @Test
    void serviceCanBeCreated() {
        // Test that the service can be instantiated
        assertNotNull(esMcpService);
    }

    @Test
    void getClientThrowsExceptionWhenNoClientsConfigured() {
        // Given
        when(mcpClientConfig.getElasticsearchMcpSyncClient()).thenThrow(new IllegalStateException("No MCP sync clients configured"));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class,
                () -> esMcpService.listAvailableTools());
        assertEquals("Failed to list MCP tools", exception.getMessage());
    }
}
