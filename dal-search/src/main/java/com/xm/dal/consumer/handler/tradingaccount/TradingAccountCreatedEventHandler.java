package com.xm.dal.consumer.handler.tradingaccount;

import static com.xm.dal.consumer.handler.EventType.TRADING_ACCOUNT_CREATED;
import static java.util.Objects.nonNull;
import static java.util.Objects.requireNonNull;

import com.xm.dal.config.AppProperties;
import com.xm.dal.consumer.handler.EventHandler;
import com.xm.dal.consumer.handler.EventType;
import com.xm.dal.consumer.repository.ProfileRepository;
import com.xm.dal.repository.elastic.entity.Country;
import com.xm.dal.repository.elastic.entity.Language;
import com.xm.dal.repository.elastic.entity.LatestLeadInfo;
import com.xm.dal.repository.elastic.entity.ProfileData;
import com.xm.dal.repository.elastic.entity.ProfileDocument;
import com.xm.dal.repository.elastic.entity.ProfileStatus;
import com.xm.dal.repository.elastic.entity.TradingAccount;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.core.convert.ConversionService;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.stereotype.Component;
import xm.protobuf.services.jthales.common.v1.XmProfileIdentifiersOuterClass;
import xm.protobuf.services.jthales.profile.v1.ProfileEventOuterClass.ProfileEvent;
import xm.protobuf.services.jthales.profile.v1.TradingAccountEventOuterClass.TradingAccountCreated;

@Component
@RequiredArgsConstructor
public class TradingAccountCreatedEventHandler implements EventHandler<ProfileEvent> {

    private final ProfileRepository profileRepository;
    private final ConversionService conversionService;
    private final AppProperties appProperties;

    @Override
    public void handle(final ProfileEvent profileEvent) {

        final TradingAccountCreated event = profileEvent.getTradingAccountEvent().getTradingAccountCreated();
        final TradingAccount tradingAccount = requireNonNull(conversionService.convert(event, TradingAccount.class));

        final XmProfileIdentifiersOuterClass.XmProfileIdentifiers xmProfileIds = event.getXmProfileIdentifiers();
        final String xmProfileId = String.valueOf(xmProfileIds.getXmProfileId());
        final IndexCoordinates indexCoordinates = appProperties.realTimeIndexCoordinates();
        final ProfileDocument profileDocument = profileRepository.findById(xmProfileId, indexCoordinates);

        final Long brandAccountId = xmProfileIds.hasBrandAccountId() ? xmProfileIds.getBrandAccountId() : null;
        profileDocument.setBrandAccountId(brandAccountId);

        final List<TradingAccount> tradingAccounts = nonNull(profileDocument.getTradingAccounts())
            ? profileDocument.getTradingAccounts() : new ArrayList<>();
        final Map<Long, Integer> indexByLogin = profileDocument.collectTradingAccountIndexByLogin();

        final Integer tradingAccountIndex = indexByLogin.get(tradingAccount.getLogin());
        if (nonNull(tradingAccountIndex)) {
            tradingAccounts.set(tradingAccountIndex, tradingAccount);
        } else {
            tradingAccounts.add(tradingAccount);
            profileDocument.setTradingAccounts(tradingAccounts);
        }

        if (profileDocument.getProfileStatus().equals(ProfileStatus.LEAD)) {
            final LatestLeadInfo updatedLatestLeadInfo =
                createLatestLeadInfo(tradingAccount, profileDocument.getProfileData());

            profileDocument.updateLeadData(updatedLatestLeadInfo);
        }

        profileRepository.upsert(profileDocument, indexCoordinates);
    }

    @Override
    public EventType getEventType() {
        return TRADING_ACCOUNT_CREATED;
    }

    private LatestLeadInfo createLatestLeadInfo(final TradingAccount tradingAccount,
                                                final ProfileData profileData) {
        final OffsetDateTime registrationDate = tradingAccount.getRegistrationDate();
        final Country country = profileData.getCountry();
        final Language preferredLanguage = profileData.getPreferredLanguage();
        return LatestLeadInfo.builder()
            .registrationDate(registrationDate)
            .country(country)
            .preferredLanguage(preferredLanguage)
            .build();
    }

}
