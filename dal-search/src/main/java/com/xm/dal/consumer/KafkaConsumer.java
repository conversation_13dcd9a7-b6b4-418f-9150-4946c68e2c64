package com.xm.dal.consumer;

import static xm.protobuf.services.loyalty_program.callback.v1.CallBackRequestEventOuterClass.CallBackRequestEvent;

import com.google.protobuf.GeneratedMessage;
import com.xm.dal.consumer.handler.EventType;
import com.xm.dal.consumer.service.EventSinkService;
import com.xm.dal.consumer.util.ProtobufUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import xm.protobuf.services.jthales.profile.v1.ProfileEventOuterClass.ProfileEvent;
import xm.protobuf.services.jthales.profilemetadata.v1.ProfileMetadataEventOuterClass.ProfileMetadataEvent;
import xm.protobuf.services.kyc.validation.v1.ValidationDocumentEventOuterClass.ValidationDocumentEvent;
import xm.protobuf.services.kyc.validation.v1.ValidationStatusEventOuterClass.ValidationStatusEvent;
import xm.protobuf.services.trading_mesh.server_operation.v1.ServerOperationService.ServerOperation;

@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "consumer", name = "enabled")
public class KafkaConsumer {

    private final EventSinkService sinkService;

    @KafkaListener(
        topics = "${consumer.kafka.profile.topic}",
        groupId = "${consumer.kafka.profile.group-id}",
        autoStartup = "${consumer.kafka.profile.auto-startup}")
    public void listenProfile(final ConsumerRecord<String, ProfileEvent> consumerRecord) {
        consume(consumerRecord);
    }

    @KafkaListener(
        topics = "${consumer.kafka.profile-metadata.topic}",
        groupId = "${consumer.kafka.profile-metadata.group-id}",
        autoStartup = "${consumer.kafka.profile-metadata.auto-startup}")
    public void listenProfileMetadata(final ConsumerRecord<String, ProfileMetadataEvent> consumerRecord) {
        consume(consumerRecord);
    }

    @KafkaListener(
        topics = "${consumer.kafka.validation-document.topic}",
        groupId = "${consumer.kafka.validation-document.group-id}",
        autoStartup = "${consumer.kafka.validation-document.auto-startup}"
    )
    public void listenValidationDocumentEvent(final ConsumerRecord<String, ValidationDocumentEvent> consumerRecord) {
        consume(consumerRecord);
    }

    @KafkaListener(
        topics = "${consumer.kafka.validation-status.topic}",
        groupId = "${consumer.kafka.validation-status.group-id}",
        autoStartup = "${consumer.kafka.validation-status.auto-startup}"
    )
    public void listenValidationStatusEvent(final ConsumerRecord<String, ValidationStatusEvent> consumerRecord) {
        consume(consumerRecord);
    }

    @KafkaListener(
        topics = "${consumer.kafka.server-operation.topic}",
        groupId = "${consumer.kafka.server-operation.group-id}",
        autoStartup = "${consumer.kafka.server-operation.auto-startup}")
    public void listenServerOperation(final ConsumerRecord<String, ServerOperation> consumerRecord) {
        consume(consumerRecord);
    }

    @KafkaListener(
        topics = "${consumer.kafka.callback-request.topic}",
        groupId = "${consumer.kafka.callback-request.group-id}",
        autoStartup = "${consumer.kafka.callback-request.auto-startup}",
        properties = {
            "auto.offset.reset=${consumer.kafka.callback-request.properties.auto.offset.reset}"
        })
    public void listenCallbackRequest(final ConsumerRecord<String, CallBackRequestEvent> consumerRecord) {
        consume(consumerRecord);
    }

    private <T extends GeneratedMessage> void consume(final ConsumerRecord<String, T> consumerRecord) {
        final T event = consumerRecord.value();
        final String key = consumerRecord.key();
        final String topic = consumerRecord.topic();
        final EventType eventType = ProtobufUtils.getEventType(event);

        log.info("Received message: topic={}, key={}, eventType={}, offset={}, partition={}",
            topic, key, eventType, consumerRecord.offset(), consumerRecord.partition());

        if (eventType != EventType.UNSUPPORTED) {
            sinkService.save(event);

            log.info("Processed message: topic={}, key={}, eventType={}, offset={}, partition={}",
                topic, key, eventType, consumerRecord.offset(), consumerRecord.partition());
        }
    }
}
