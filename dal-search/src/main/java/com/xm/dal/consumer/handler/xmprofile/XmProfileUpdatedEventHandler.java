package com.xm.dal.consumer.handler.xmprofile;

import static com.xm.dal.consumer.handler.EventType.XM_PROFILE_UPDATED;
import static java.util.Objects.requireNonNull;

import com.xm.dal.config.AppProperties;
import com.xm.dal.consumer.handler.EventHandler;
import com.xm.dal.consumer.handler.EventType;
import com.xm.dal.consumer.repository.ProfileRepository;
import com.xm.dal.repository.elastic.entity.Country;
import com.xm.dal.repository.elastic.entity.Language;
import com.xm.dal.repository.elastic.entity.LatestLeadInfo;
import com.xm.dal.repository.elastic.entity.ProfileData;
import com.xm.dal.repository.elastic.entity.ProfileDocument;
import com.xm.dal.repository.elastic.entity.ProfileStatus;
import lombok.RequiredArgsConstructor;
import org.springframework.core.convert.ConversionService;
import org.springframework.stereotype.Component;
import xm.protobuf.services.jthales.profile.v1.ProfileEventOuterClass.ProfileEvent;
import xm.protobuf.services.jthales.profile.v1.XmProfileEventOuterClass.XmProfileUpdated;

@Component
@RequiredArgsConstructor
public class XmProfileUpdatedEventHandler implements EventHandler<ProfileEvent> {

    private final ProfileRepository profileRepository;
    private final ConversionService conversionService;
    private final AppProperties appProperties;

    @Override
    public void handle(final ProfileEvent profileEvent) {

        final XmProfileUpdated event = profileEvent.getXmProfileEvent().getXmProfileUpdated();
        final ProfileDocument profileDocument = requireNonNull(conversionService.convert(event, ProfileDocument.class));

        if (profileDocument.getProfileStatus().equals(ProfileStatus.LEAD)) {
            final LatestLeadInfo updatedLatestLeadInfo = createLatestLeadInfo(profileDocument.getProfileData());

            profileDocument.updateLeadData(updatedLatestLeadInfo);
        }

        profileRepository.update(profileDocument, appProperties.realTimeIndexCoordinates());
    }

    @Override
    public EventType getEventType() {
        return XM_PROFILE_UPDATED;
    }

    private LatestLeadInfo createLatestLeadInfo(final ProfileData profileData) {
        final Country country = profileData.getCountry();
        final Language preferredLanguage = profileData.getPreferredLanguage();
        return LatestLeadInfo.builder()
            .country(country)
            .preferredLanguage(preferredLanguage)
            .build();
    }
}
