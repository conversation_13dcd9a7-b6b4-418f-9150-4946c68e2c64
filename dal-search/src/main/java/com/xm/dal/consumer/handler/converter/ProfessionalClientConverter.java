package com.xm.dal.consumer.handler.converter;

import static xm.protobuf.services.jthales.profile.v1.ProfileDataOuterClass.ProfileData;

import com.xm.dal.domain.ProfessionalClient;
import lombok.RequiredArgsConstructor;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;
import xm.protobuf.services.jthales.profile.v1.ProfessionalClientOuterClass;

@Component
@RequiredArgsConstructor
public class ProfessionalClientConverter implements Converter<ProfileData, ProfessionalClient> {

    @Override
    public ProfessionalClient convert(final ProfileData profileData) {
        if (!profileData.hasProfessionalClient()) {
            return null;
        }

        final ProfessionalClientOuterClass.ProfessionalClient pc = profileData.getProfessionalClient();
        return ProfessionalClient.builder()
            .isProfessionalClient(pc.hasIsProfessionalClient() ? pc.getIsProfessionalClient() : null)
            .build();
    }
}
