package com.xm.dal.consumer.handler.validationstatus;

import static com.xm.dal.consumer.handler.EventType.VALIDATION_DOCUMENT_EVENT;
import static com.xm.dal.consumer.util.ProtobufUtils.toOffsetDateTime;

import com.xm.dal.config.AppProperties;
import com.xm.dal.consumer.handler.EventHandler;
import com.xm.dal.consumer.handler.EventType;
import com.xm.dal.consumer.repository.ProfileRepository;
import com.xm.dal.repository.elastic.entity.ProfileDocument;
import com.xm.dal.repository.elastic.entity.ValidationStatus;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import xm.protobuf.services.kyc.validation.v1.ValidationDocumentEventOuterClass.ValidationDocumentEvent;
import xm.protobuf.services.kyc.validation.v1.ValidationDocumentStatusOuterClass;
import xm.protobuf.services.kyc.validation.v1.ValidationDocumentTypeOuterClass.ValidationDocumentType;

@Component
@RequiredArgsConstructor
public class ValidationDocumentEventHandler
    implements EventHandler<ValidationDocumentEvent> {

    private final ProfileRepository profileRepository;
    private final AppProperties appProperties;

    @Override
    public void handle(final ValidationDocumentEvent event) {
        if (!isApprovedIdentityDocument(event)) {
            return;
        }

        final long profileId = event.getProfileId();

        final ProfileDocument profile =
            profileRepository.findById(String.valueOf(profileId), appProperties.realTimeIndexCoordinates());

        if (profile == null) {
            // If the profile hasn't been created already, we create one.
            final ValidationStatus updatedValidationStatus = ValidationStatus.builder()
                .documentExpirationDate(toOffsetDateTime(event.getExpiryDate()))
                .build();

            final ProfileDocument profileDocument = ProfileDocument.builder()
                .id(String.valueOf(profileId))
                .profileId(profileId)
                .validationStatus(updatedValidationStatus)
                .build();

            profileRepository.upsert(profileDocument, appProperties.realTimeIndexCoordinates());
            return;
        }

        final ValidationStatus updatedValidationStatus = profile.getValidationStatus().toBuilder()
            .documentExpirationDate(toOffsetDateTime(event.getExpiryDate()))
            .build();

        profile.setValidationStatus(updatedValidationStatus);

        profileRepository.update(profile, appProperties.realTimeIndexCoordinates());
    }

    @Override
    public EventType getEventType() {
        return VALIDATION_DOCUMENT_EVENT;
    }

    protected boolean isApprovedIdentityDocument(final ValidationDocumentEvent event) {
        return event.getDocumentType() == ValidationDocumentType.VALIDATION_DOCUMENT_TYPE_IDENTITY && event.getStatus()
            .equals(ValidationDocumentStatusOuterClass.ValidationDocumentStatus.VALIDATION_DOCUMENT_STATUS_APPROVED);
    }
}
