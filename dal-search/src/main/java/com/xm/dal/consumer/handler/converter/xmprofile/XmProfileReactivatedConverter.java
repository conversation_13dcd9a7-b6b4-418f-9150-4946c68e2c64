package com.xm.dal.consumer.handler.converter.xmprofile;

import static com.xm.dal.consumer.util.ProtobufUtils.toBrand;
import static com.xm.dal.consumer.util.ProtobufUtils.toLocalDate;
import static com.xm.dal.consumer.util.ProtobufUtils.toOffsetDateTime;
import static com.xm.dal.consumer.util.ProtobufUtils.toProfileStatus;
import static com.xm.dal.consumer.util.ProtobufUtils.toRegistrationStatus;
import static com.xm.dal.util.Nullable.applyIfPresent;
import static xm.protobuf.services.jthales.profile.v1.XmProfileEventOuterClass.XmProfileReactivated;

import com.xm.dal.consumer.exception.EventConsumerException;
import com.xm.dal.consumer.refdata.service.RefDataService;
import com.xm.dal.domain.ProfessionalClient;
import com.xm.dal.repository.elastic.entity.Address;
import com.xm.dal.repository.elastic.entity.Confirmation;
import com.xm.dal.repository.elastic.entity.Country;
import com.xm.dal.repository.elastic.entity.Language;
import com.xm.dal.repository.elastic.entity.Name;
import com.xm.dal.repository.elastic.entity.Phone;
import com.xm.dal.repository.elastic.entity.ProfileData;
import com.xm.dal.repository.elastic.entity.ProfileDocument;
import com.xm.dal.repository.elastic.entity.RegistrationStatus;
import com.xm.dal.repository.elastic.entity.ValidationStatus;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.convert.ConversionService;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;
import xm.protobuf.common.BrandOuterClass.Brand;
import xm.protobuf.services.jthales.common.v1.XmProfileIdentifiersOuterClass.XmProfileIdentifiers;
import xm.protobuf.services.jthales.profile.v1.ProfileDataOuterClass;

@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class XmProfileReactivatedConverter implements Converter<XmProfileReactivated, ProfileDocument> {

    private final ConversionService conversionService;
    private final RefDataService refDataService;

    @Override
    public ProfileDocument convert(final XmProfileReactivated event) {
        final XmProfileIdentifiers xmProfileIds = event.getXmProfileIdentifiers();

        if (!xmProfileIds.hasXmProfileId()) {
            throw new EventConsumerException("XmProfileId is required");
        }

        final Brand eventBrand = xmProfileIds.getBrand();
        final long profileId = xmProfileIds.getXmProfileId();
        final Long leadId = xmProfileIds.hasLeadId() ? xmProfileIds.getLeadId() : null;
        final Long brandAccountId = xmProfileIds.hasBrandAccountId() ? xmProfileIds.getBrandAccountId() : null;

        return ProfileDocument.builder()
            .id(String.valueOf(profileId))
            .email(xmProfileIds.getEmail())
            .brand(toBrand(eventBrand))
            .profileId(profileId)
            .leadId(leadId)
            .brandAccountId(brandAccountId)
            .profileStatus(toProfileStatus(event.getProfileStatus()).orElse(null))
            .profileData(map(event))
            .confirmations(conversionService.convert(event.getProfileData(), Confirmation.class))
            .validationStatus(ValidationStatus.builder()
                .isRevalidationRequested(event.getIsRevalidationRequested()
                ).build())
            .isProfessionalClient(applyIfPresent(
                conversionService.convert(event.getProfileData(), ProfessionalClient.class),
                ProfessionalClient::isProfessionalClient))
            .build();
    }

    private ProfileData map(final XmProfileReactivated event) {
        final ProfileDataOuterClass.ProfileData pd = event.getProfileData();

        return ProfileData.builder()
            .registrationStatus(toRegistrationStatus(event.getRegistrationStatus())
                .map(Enum::name)
                .map(RegistrationStatus::valueOf)
                .orElse(null))
            .country(conversionService.convert(
                refDataService.findCountryByIsoCode(event.getCountry()), Country.class))
            .preferredLanguage(conversionService.convert(
                refDataService.findLanguageByIsoCode(event.getPreferredLanguage()), Language.class))
            .dateOfBirth(toLocalDate(pd.getDateOfBirth()).orElse(null))
            .name(pd.hasName() ? conversionService.convert(pd.getName(), Name.class) : null)
            .address(pd.hasAddress() ? conversionService.convert(pd.getAddress(), Address.class) : null)
            .phones(pd.getPhonesList().stream().map(it -> conversionService.convert(it, Phone.class)).toList())
            .lastUpdateDate(toOffsetDateTime(pd.getLastUpdateDate()))
            .build();
    }
}
