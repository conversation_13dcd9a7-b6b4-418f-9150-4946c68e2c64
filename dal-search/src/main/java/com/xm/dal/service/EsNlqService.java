package com.xm.dal.service;

import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class EsNlqService {

    private final ChatModel chatModel;

    private static final String ELASTICSEARCH_QUERY_PROMPT = """
        You are an expert in Elasticsearch queries. Convert the following natural language request into a valid Elasticsearch query JSON.
        
        Rules:
        1. Return ONLY the JSON query object, no explanations or markdown formatting
        2. Use appropriate Elasticsearch query types (match, term, range, bool, etc.)
        3. For text searches, prefer 'match' or 'multi_match' queries
        4. For exact matches, use 'term' queries
        5. For date ranges, use 'range' queries
        6. Combine multiple conditions with 'bool' queries
        7. The response must be valid JSON that can be parsed
        
        Index schema context:
        - This is a profiles index containing user profile data
        - Common fields: name, email, age, status, created_date, last_login
        - Text fields are analyzed, use 'match' for text search
        - Status field uses exact values, use 'term' for status
        
        Natural language request: {query}
        
        Elasticsearch Query JSON:
        """;

    public String translateToElasticsearchQuery(String naturalLanguageQuery) {
        log.debug("Translating natural language query: {}", naturalLanguageQuery);

        try {
            PromptTemplate promptTemplate = new PromptTemplate(ELASTICSEARCH_QUERY_PROMPT);
            Prompt prompt = promptTemplate.create(Map.of("query", naturalLanguageQuery));

            String response = chatModel.call(prompt).getResult().getOutput().getText();

            // Clean up the response to ensure it's valid JSON
            String cleanedResponse = cleanJsonResponse(response);

            log.info("Translated query '{}' to Elasticsearch query: {}", naturalLanguageQuery, cleanedResponse);
            return cleanedResponse;

        } catch (Exception e) {
            log.error("Error translating natural language query: {}", naturalLanguageQuery, e);
            throw new RuntimeException("Failed to translate natural language query", e);
        }
    }

    public Object searchWithNaturalLanguage(String naturalLanguageQuery, String index,
                                            EsMcpService esMcpService) {
        log.info("Processing natural language search: '{}' on index: {}", naturalLanguageQuery, index);

        try {
            // Step 1: Translate natural language to Elasticsearch query
            String elasticsearchQuery = translateToElasticsearchQuery(naturalLanguageQuery);

            // Step 2: Execute the query via MCP
            Object results = esMcpService.executeSearch(index, elasticsearchQuery);

            log.info("Natural language search completed successfully");
            return results;

        } catch (Exception e) {
            log.error("Error processing natural language search: {}", naturalLanguageQuery, e);
            throw new RuntimeException("Failed to process natural language search", e);
        }
    }

    private String cleanJsonResponse(String response) {
        if (response == null) {
            return "{}";
        }

        // Remove markdown code blocks if present
        String cleaned = response.trim();
        if (cleaned.startsWith("```json")) {
            cleaned = cleaned.substring(7);
        }
        if (cleaned.startsWith("```")) {
            cleaned = cleaned.substring(3);
        }
        if (cleaned.endsWith("```")) {
            cleaned = cleaned.substring(0, cleaned.length() - 3);
        }

        // Trim whitespace
        cleaned = cleaned.trim();

        // If empty or invalid, return empty query
        if (cleaned.isEmpty() || !cleaned.startsWith("{")) {
            log.warn("Invalid JSON response from LLM, using empty query: {}", response);
            return "{}";
        }

        return cleaned;
    }

    public Map<String, String> getExampleQueries() {
        return Map.of(
            "Find all active users", "Search for users with active status",
            "Show users created in the last month", "Find recently created user profiles",
            "Find users with gmail email addresses", "Search for users with Gmail email domains",
            "Show users older than 30", "Find users with age greater than 30",
            "Find users named John", "Search for users with the name John",
            "Show inactive users who haven't logged in recently", "Complex query combining status and login date"
        );
    }
}
