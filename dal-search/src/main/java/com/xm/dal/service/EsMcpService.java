package com.xm.dal.service;

import io.modelcontextprotocol.client.McpSyncClient;
import io.modelcontextprotocol.spec.McpSchema;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class EsMcpService {

    private final McpSyncClient elasticsearchMcpSyncClient;

    public List<McpSchema.Tool> listAvailableTools() {
        log.debug("Listing available tools from Elasticsearch MCP server");
        try {
            var response = elasticsearchMcpSyncClient.listTools();
            log.info("Found {} tools available from Elasticsearch MCP server", response.tools().size());
            return response.tools();
        } catch (Exception e) {
            log.error("Error listing tools from Elasticsearch MCP server", e);
            throw new RuntimeException("Failed to list MCP tools", e);
        }
    }

    public Object executeSearch(String index, String query) {
        log.debug("Executing search on index: {} with query: {}", index, query);
        try {
            var toolCall = new McpSchema.CallToolRequest(
                "elasticsearch_search",
                Map.of(
                    "index", index,
                    "query", query
                )
            );

            var response = elasticsearchMcpSyncClient.callTool(toolCall);
            log.info("Search executed successfully on index: {}", index);
            return response.content();
        } catch (Exception e) {
            log.error("Error executing search on index: {} with query: {}", index, query, e);
            throw new RuntimeException("Failed to execute search via MCP", e);
        }
    }

    public Object getIndexInfo(String index) {
        log.debug("Getting info for index: {}", index);
        try {
            var toolCall = new McpSchema.CallToolRequest(
                "elasticsearch_index_info",
                Map.of("index", index)
            );

            var response = elasticsearchMcpSyncClient.callTool(toolCall);
            log.info("Retrieved info for index: {}", index);
            return response.content();
        } catch (Exception e) {
            log.error("Error getting info for index: {}", index, e);
            throw new RuntimeException("Failed to get index info via MCP", e);
        }
    }

    public Object listIndices() {
        log.debug("Listing all Elasticsearch indices");
        try {
            var toolCall = new McpSchema.CallToolRequest(
                "elasticsearch_list_indices",
                Map.of()
            );

            var response = elasticsearchMcpSyncClient.callTool(toolCall);
            log.info("Successfully listed Elasticsearch indices");
            return response.content();
        } catch (Exception e) {
            log.error("Error listing Elasticsearch indices", e);
            throw new RuntimeException("Failed to list indices via MCP", e);
        }
    }

    public Object executeCustomOperation(String toolName, Map<String, Object> arguments) {
        log.debug("Executing custom operation: {} with arguments: {}", toolName, arguments);
        try {
            var toolCall = new McpSchema.CallToolRequest(
                toolName,
                arguments
            );

            var response = elasticsearchMcpSyncClient.callTool(toolCall);
            log.info("Successfully executed custom operation: {}", toolName);
            return response.content();
        } catch (Exception e) {
            log.error("Error executing custom operation: {} with arguments: {}", toolName, arguments, e);
            throw new RuntimeException("Failed to execute custom operation via MCP", e);
        }
    }
}
