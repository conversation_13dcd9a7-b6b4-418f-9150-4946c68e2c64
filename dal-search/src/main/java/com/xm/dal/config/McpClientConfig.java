package com.xm.dal.config;

import io.modelcontextprotocol.client.McpSyncClient;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
public class McpClientConfig {

    @Bean
    public McpSyncClient elasticsearchMcpSyncClient(List<McpSyncClient> mcpSyncClients) {
        if (mcpSyncClients.isEmpty()) {
            throw new IllegalStateException("No MCP sync clients configured");
        }
        log.info("Using MCP sync client: {}", mcpSyncClients.get(0));
        return mcpSyncClients.get(0);
    }
}
