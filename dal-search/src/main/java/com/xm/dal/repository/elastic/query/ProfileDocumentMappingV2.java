package com.xm.dal.repository.elastic.query;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.data.elasticsearch.annotations.FieldType;
import org.springframework.data.elasticsearch.core.query.Field;
import org.springframework.data.elasticsearch.core.query.SimpleField;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
class ProfileDocumentMappingV2 {
    static final String TRADING_ACCOUNTS_NESTED_PATH_V2 = "tradingAccounts";
    static final String DEMO_ACCOUNTS_NESTED_PATH_V2 = "demoAccounts";
    static final String WEBINAR_ACCOUNTS_NESTED_PATH_V2 = "webinarAccounts";
    static final String FIRST_STEPS_NESTED_PATH_V2 = "firstSteps";
    static final String SECOND_STEPS_NESTED_PATH_V2 = "secondSteps";
    static final String EDUCATION_ACCOUNTS_NESTED_PATH_V2 = "educationAccounts";
    static final String WALLET_ACCOUNTS_NESTED_PATH_V2 = "walletAccounts";
    static final String CALLBACK_REQUESTS_NESTED_PATH_V2 = "callbackRequests";

    static final Field BRAND_ACCOUNT_ID_V2 = field("brandAccountId", FieldType.Long);
    static final Field BRAND_ID_V2 = field("brand.brandId", FieldType.Byte);
    static final Field LEAD_ID_V2 = field("leadId", FieldType.Long);

    static final Field BRAND_NAME_V2 = field("brand.brandName", FieldType.Keyword);
    static final Field EMAIL_V2 = field("email", FieldType.Keyword);
    static final Field PROFILE_ID_V2 = field("profileId", FieldType.Long);
    static final Field PROFILE_STATUS_V2 = field("profileStatus", FieldType.Keyword);


    // Profile Data
    static final Field CITY_V2 = field("profileData.address.city", FieldType.Keyword);
    static final Field COUNTRY_CODE_V2 = field("profileData.country.code", FieldType.Keyword);
    static final Field COUNTRY_NAME_V2 = field("profileData.country.name", FieldType.Keyword);
    static final Field DATE_OF_BIRTH_V2 = field("profileData.dateOfBirth", FieldType.Date);
    static final Field FIRST_NAME_V2 = field("profileData.name.firstName", FieldType.Keyword);
    static final Field INTERNATIONAL_PHONE_NUMBER_V2 = field("profileData.phones.international", FieldType.Keyword);
    static final Field IS_PROFESSIONAL_CLIENT_V2 = field("isProfessionalClient", FieldType.Boolean);
    static final Field LAST_NAME_V2 = field("profileData.name.lastName", FieldType.Keyword);
    static final Field PHONE_NUMBER_V2 = field("profileData.phones.phoneNumber", FieldType.Keyword);
    static final Field PREFERRED_LANGUAGE_CODE_V2 = field("profileData.preferredLanguage.code", FieldType.Keyword);
    static final Field REGISTRATION_DATE_V2 = field("profileData.registrationDate", FieldType.Date);
    static final Field REGISTRATION_STATUS_V2 = field("profileData.registrationStatus", FieldType.Keyword);

    //Lead Data
    static final Field LATEST_LEAD_REGISTRATION_DATE_V2 = field("leadData.latestInfo.registrationDate", FieldType.Date);
    static final Field LATEST_COMMENT_ID_V2 = field("leadData.latestInfo.comment.id", FieldType.Long);

    // Validation Status
    static final Field IS_VALIDATED_V2 = field("validationStatus.isValidated", FieldType.Boolean);
    static final Field DOCUMENT_EXPIRATION_DATE_V2 = field("validationStatus.documentExpirationDate", FieldType.Date);
    static final Field IS_REVALIDATION_REQUESTED_V2 =
        field("validationStatus.isRevalidationRequested", FieldType.Boolean);
    static final Field VALIDATION_DATE_V2 = field("validationStatus.validationDate", FieldType.Date);

    // Ratings
    static final Field RATING_ID_V2 = field("ratings.id", FieldType.Integer);

    // Comments
    static final Field COMMENT_DATE_ADDED_V2 = field("comments.dateAdded", FieldType.Date);

    // Labels
    static final Field LABEL_ID_V2 = field("labels.id", FieldType.Long);

    // Trading Accounts
    static final Field ACCOUNT_MANAGER_FIRST_NAME_V2 =
        nested("tradingAccounts.accountManager.firstName", FieldType.Keyword);
    static final Field ACCOUNT_MANAGER_ID_V2 = nested("tradingAccounts.accountManager.id", FieldType.Keyword);
    static final Field ACCOUNT_MANAGER_LAST_NAME_V2 =
        nested("tradingAccounts.accountManager.lastName", FieldType.Keyword);
    static final Field ACCOUNT_STATUS_V2 = nested("tradingAccounts.accountStatus", FieldType.Keyword);
    static final Field LOGIN_V2 = nested("tradingAccounts.login", FieldType.Long);
    static final Field ACCOUNT_TYPE_ID_V2 = nested("tradingAccounts.accountType.id", FieldType.Integer);
    static final Field AFFILIATE_FIRST_NAME_V2 = nested("tradingAccounts.affiliate.firstName", FieldType.Keyword);
    static final Field IS_AFFILIATED_V2 = nested("tradingAccounts.affiliate.id", FieldType.Boolean);

    static final Field AFFILIATE_ID_V2 = nested("tradingAccounts.affiliate.id", FieldType.Long);
    static final Field AFFILIATE_LAST_NAME_V2 = nested("tradingAccounts.affiliate.lastName", FieldType.Keyword);
    static final Field AFFILIATE_MANAGER_FIRST_NAME_V2 =
        nested("tradingAccounts.affiliateManager.firstName", FieldType.Keyword);
    static final Field AFFILIATE_MANAGER_ID_V2 = nested("tradingAccounts.affiliateManager.id", FieldType.Keyword);
    static final Field AFFILIATE_MANAGER_LAST_NAME_V2 =
        nested("tradingAccounts.affiliateManager.lastName", FieldType.Keyword);
    static final Field COMMENT_V2 = nested("tradingAccounts.comment", FieldType.Keyword);
    static final Field CURRENCY_V2 = nested("tradingAccounts.currency", FieldType.Keyword);
    static final Field GROUP_NAME_V2 = nested("tradingAccounts.groupName", FieldType.Keyword);
    static final Field IS_ISLAMIC_V2 = nested("tradingAccounts.isIslamic", FieldType.Boolean);
    static final Field LEVERAGE_V2 = nested("tradingAccounts.leverage", FieldType.Integer);
    static final Field MEMBERS_AREA_LAST_LOGIN_V2 =
        nested("tradingAccounts.loginActivity.membersAreaLastLogin", FieldType.Date);
    static final Field MOBILE_LAST_LOGIN_V2 = nested("tradingAccounts.loginActivity.mobileLastLogin", FieldType.Date);
    static final Field MQ_TERMINAL_LAST_LOGIN_V2 =
        nested("tradingAccounts.loginActivity.mqTerminalLastLogin", FieldType.Date);
    static final Field PLATFORM_TYPE_V2 = nested("tradingAccounts.serverInfo.platformType", FieldType.Keyword);
    static final Field SOCIAL_TRADING_STATUS_V2 = nested("tradingAccounts.socialTradingStatus", FieldType.Keyword);
    static final Field WEB_TRADER_LAST_LOGIN_V2 =
        nested("tradingAccounts.loginActivity.webTraderLastLogin", FieldType.Date);
    static final Field GID_V2 = nested("tradingAccounts.gid", FieldType.Integer);
    static final Field ACCOUNT_REGISTRATION_DATE_V2 = nested("tradingAccounts.registrationDate", FieldType.Date);
    static final Field HAS_ACTIVE_TRADING_ACCOUNTS_V2 = field("hasActiveTradingAccounts", FieldType.Boolean);
    static final Field LATEST_TRADE_DATE_V2 = field("latestTradeDate", FieldType.Date);

    // Financial Info
    static final Field DEPOSIT_COUNT_V2 = field("financialInfo.depositCount", FieldType.Integer);
    static final Field FIRST_DEPOSIT_DATE_V2 = field("financialInfo.firstDepositDate", FieldType.Date);
    static final Field LATEST_DEPOSIT_DATE_V2 = field("financialInfo.latestDepositDate", FieldType.Date);
    static final Field LATEST_MONTH_PNL_EUR_V2 = field("financialInfo.latestMonthPnlEur", FieldType.Float);
    static final Field TOTAL_BALANCE_EUR_V2 = field("financialInfo.totalBalanceEur", FieldType.Float);
    static final Field TOTAL_CREDIT_EUR_V2 = field("financialInfo.totalCreditEur", FieldType.Float);
    static final Field TOTAL_DEPOSITS_EUR_V2 = field("financialInfo.totalDepositsEur", FieldType.Float);
    static final Field TOTAL_EQUITY_EUR_V2 = field("financialInfo.totalEquityEur", FieldType.Float);
    static final Field TOTAL_PNL_EUR_V2 = field("financialInfo.totalPnlEur", FieldType.Float);
    static final Field TOTAL_WITHDRAWALS_EUR_V2 = field("financialInfo.totalWithdrawalsEur", FieldType.Float);

    // Raf Info
    static final Field IS_RAF_QUALIFIED_V2 = field("rafInfo.isRafQualified", FieldType.Boolean);
    static final Field IS_REFERRER_V2 = field("rafInfo.isReferrer", FieldType.Boolean);
    static final Field IS_REFERRED_CLIENT_V2 = field("rafInfo.isReferredClient", FieldType.Boolean);
    static final Field RAF_CONNECTION_STATUS_ID_V2 = field("rafInfo.connectionStatusId", FieldType.Byte);
    static final Field REFERRER_TOKEN_V2 = field("rafInfo.referrerToken", FieldType.Keyword);

    // Phone Calls
    static final Field LATEST_INVALID_PHONE_CALL_DATE_V2 =
        field("phoneCallsInfo.latestInvalidPhoneCallDate", FieldType.Date);
    static final Field LATEST_PHONE_CALL_ATTEMPT_DATE_V2 =
        field("phoneCallsInfo.latestPhoneCallAttemptDate", FieldType.Date);
    static final Field LATEST_VALID_PHONE_CALL_DATE_V2 =
        field("phoneCallsInfo.latestValidPhoneCallDate", FieldType.Date);
    static final Field TOTAL_ATTEMPTS_BEFORE_ACTIVATION_V2 =
        field("phoneCallsInfo.totalAttemptsBeforeClientActivation", FieldType.Integer);
    static final Field TOTAL_INVALID_PHONE_CALLS_V2 = field("phoneCallsInfo.totalInvalidPhoneCalls", FieldType.Integer);
    static final Field TOTAL_INVALID_PHONE_CALLS_BEFORE_ACTIVATION_V2 =
        field("phoneCallsInfo.totalInvalidBeforeClientActivation", FieldType.Integer);
    static final Field TOTAL_OTHER_PHONE_CALLS_V2 =
        field("phoneCallsInfo.totalOtherCommunicatedPhoneCalls", FieldType.Integer);
    static final Field TOTAL_PHONE_CALL_ATTEMPTS_V2 = field("phoneCallsInfo.totalPhoneCallAttempts", FieldType.Integer);
    static final Field TOTAL_VALID_PHONE_CALLS_BEFORE_ACTIVATION_V2 =
        field("phoneCallsInfo.totalValidBeforeClientActivation", FieldType.Integer);

    // Confirmations
    static final Field PROMO_AGREED_V2 = field("confirmations.promoAgreed", FieldType.Boolean);

    // Callback Requests
    static final Field CALLBACK_REQUEST_STATUS_V2 =
        field("callbackRequests.status", FieldType.Keyword, CALLBACK_REQUESTS_NESTED_PATH_V2);

    // Others
    static final Field SAT_V2 = field("sat", FieldType.Boolean);
    static final Field LATEST_EMAIL_DATE_V2 = field("latestEmailDate", FieldType.Date);

    //Demos

    //Demo Accounts
    static final Field HAS_DEMOS_V2 = field("demoAccounts.login", FieldType.Boolean, DEMO_ACCOUNTS_NESTED_PATH_V2);
    static final Field DEMO_LOGIN_V2 = field("demoAccounts.login", FieldType.Long, DEMO_ACCOUNTS_NESTED_PATH_V2);

    //Demo - Server Info

    //Demo - Name
    static final Field DEMO_FIRST_NAME_V2 =
        field("demoAccounts.name.firstName", FieldType.Keyword, DEMO_ACCOUNTS_NESTED_PATH_V2);
    static final Field DEMO_LAST_NAME_V2 =
        field("demoAccounts.name.lastName", FieldType.Keyword, DEMO_ACCOUNTS_NESTED_PATH_V2);

    //Demo  - Phone
    static final Field DEMO_PHONE_NUMBER_V2 =
        field("demoAccounts.phone.phoneNumber", FieldType.Keyword, DEMO_ACCOUNTS_NESTED_PATH_V2);

    static final Field DEMO_COUNTRY_CODE_V2 =
        field("demoAccounts.country.code", FieldType.Keyword, DEMO_ACCOUNTS_NESTED_PATH_V2);
    static final Field DEMO_COUNTRY_NAME_V2 =
        field("demoAccounts.country.name", FieldType.Keyword, DEMO_ACCOUNTS_NESTED_PATH_V2);

    //Demo - Account Manager
    static final Field DEMO_ACCOUNT_MANAGER_ID_V2 =
        field("demoAccounts.accountManager.id", FieldType.Keyword, DEMO_ACCOUNTS_NESTED_PATH_V2);
    static final Field DEMO_ACCOUNT_MANAGER_FIRST_NAME_V2 =
        field("demoAccounts.accountManager.firstName", FieldType.Keyword, DEMO_ACCOUNTS_NESTED_PATH_V2);
    static final Field DEMO_ACCOUNT_MANAGER_LAST_NAME_V2 =
        field("demoAccounts.accountManager.lastName", FieldType.Keyword, DEMO_ACCOUNTS_NESTED_PATH_V2);

    //Demo - Affiliate
    static final Field DEMO_AFFILIATE_ID_V2 =
        field("demoAccounts.affiliate.id", FieldType.Long, DEMO_ACCOUNTS_NESTED_PATH_V2);
    static final Field DEMO_AFFILIATE_FIRST_NAME_V2 =
        field("demoAccounts.affiliate.firstName", FieldType.Keyword, DEMO_ACCOUNTS_NESTED_PATH_V2);
    static final Field DEMO_AFFILIATE_LAST_NAME_V2 =
        field("demoAccounts.affiliate.lastName", FieldType.Keyword, DEMO_ACCOUNTS_NESTED_PATH_V2);

    //Demo - Affiliate Manager
    static final Field DEMO_AFFILIATE_MANAGER_ID_V2 =
        field("demoAccounts.affiliateManager.id", FieldType.Keyword, DEMO_ACCOUNTS_NESTED_PATH_V2);
    static final Field DEMO_AFFILIATE_MANAGER_FIRST_NAME_V2 =
        field("demoAccounts.affiliateManager.firstName", FieldType.Keyword, DEMO_ACCOUNTS_NESTED_PATH_V2);
    static final Field DEMO_AFFILIATE_MANAGER_LAST_NAME_V2 =
        field("demoAccounts.affiliateManager.lastName", FieldType.Keyword, DEMO_ACCOUNTS_NESTED_PATH_V2);

    static final Field DEMO_REGISTRATION_DATE_V2 =
        field("demoAccounts.registrationDate", FieldType.Date, DEMO_ACCOUNTS_NESTED_PATH_V2);


    //Webinar Accounts
    static final Field WEBINAR_ID_V2 = field("webinarAccounts.id", FieldType.Long, WEBINAR_ACCOUNTS_NESTED_PATH_V2);
    static final Field HAS_WEBINARS_V2 =
        field("webinarAccounts.id", FieldType.Boolean, WEBINAR_ACCOUNTS_NESTED_PATH_V2);


    //Webinar - Name
    static final Field WEBINAR_FIRST_NAME_V2 =
        field("webinarAccounts.name.firstName", FieldType.Keyword, WEBINAR_ACCOUNTS_NESTED_PATH_V2);
    static final Field WEBINAR_LAST_NAME_V2 =
        field("webinarAccounts.name.lastName", FieldType.Keyword, WEBINAR_ACCOUNTS_NESTED_PATH_V2);

    //Webinar  - Phone
    static final Field WEBINAR_PHONE_NUMBER_V2 =
        field("webinarAccounts.phone.phoneNumber", FieldType.Keyword, WEBINAR_ACCOUNTS_NESTED_PATH_V2);

    static final Field WEBINAR_COUNTRY_CODE_V2 =
        field("webinarAccounts.country.code", FieldType.Keyword, WEBINAR_ACCOUNTS_NESTED_PATH_V2);
    static final Field WEBINAR_COUNTRY_NAME_V2 =
        field("webinarAccounts.country.name", FieldType.Keyword, WEBINAR_ACCOUNTS_NESTED_PATH_V2);
    static final Field WEBINAR_CITY_V2 =
        field("webinarAccounts.city", FieldType.Keyword, WEBINAR_ACCOUNTS_NESTED_PATH_V2);

    //Webinar - Account Manager
    static final Field WEBINAR_ACCOUNT_MANAGER_ID_V2 =
        field("webinarAccounts.accountManager.id", FieldType.Keyword, WEBINAR_ACCOUNTS_NESTED_PATH_V2);
    static final Field WEBINAR_ACCOUNT_MANAGER_FIRST_NAME_V2 =
        field("webinarAccounts.accountManager.firstName", FieldType.Keyword, WEBINAR_ACCOUNTS_NESTED_PATH_V2);
    static final Field WEBINAR_ACCOUNT_MANAGER_LAST_NAME_V2 =
        field("webinarAccounts.accountManager.lastName", FieldType.Keyword, WEBINAR_ACCOUNTS_NESTED_PATH_V2);

    //Webinar - Affiliate
    static final Field WEBINAR_AFFILIATE_ID_V2 =
        field("webinarAccounts.affiliate.id", FieldType.Long, WEBINAR_ACCOUNTS_NESTED_PATH_V2);
    static final Field WEBINAR_AFFILIATE_FIRST_NAME_V2 =
        field("webinarAccounts.affiliate.firstName", FieldType.Keyword, WEBINAR_ACCOUNTS_NESTED_PATH_V2);
    static final Field WEBINAR_AFFILIATE_LAST_NAME_V2 =
        field("webinarAccounts.affiliate.lastName", FieldType.Keyword, WEBINAR_ACCOUNTS_NESTED_PATH_V2);

    //Webinar - Affiliate Manager
    static final Field WEBINAR_AFFILIATE_MANAGER_ID_V2 =
        field("webinarAccounts.affiliateManager.id", FieldType.Keyword, WEBINAR_ACCOUNTS_NESTED_PATH_V2);
    static final Field WEBINAR_AFFILIATE_MANAGER_FIRST_NAME_V2 =
        field("webinarAccounts.affiliateManager.firstName", FieldType.Keyword, WEBINAR_ACCOUNTS_NESTED_PATH_V2);
    static final Field WEBINAR_AFFILIATE_MANAGER_LAST_NAME_V2 =
        field("webinarAccounts.affiliateManager.lastName", FieldType.Keyword, WEBINAR_ACCOUNTS_NESTED_PATH_V2);

    static final Field WEBINAR_REGISTRATION_DATE_V2 =
        field("webinarAccounts.registrationDate", FieldType.Date, WEBINAR_ACCOUNTS_NESTED_PATH_V2);

    //Education Accounts
    static final Field EDUCATION_ACCOUNT_ID_V2 =
        field("educationAccounts.id", FieldType.Long, EDUCATION_ACCOUNTS_NESTED_PATH_V2);
    static final Field HAS_EDUCATION_ACCOUNTS_V2 =
        field("educationAccounts.id", FieldType.Boolean, EDUCATION_ACCOUNTS_NESTED_PATH_V2);


    //Education Account - Name
    static final Field EDUCATION_ACCOUNT_FIRST_NAME_V2 =
        field("educationAccounts.name.firstName", FieldType.Keyword, EDUCATION_ACCOUNTS_NESTED_PATH_V2);
    static final Field EDUCATION_ACCOUNT_LAST_NAME_V2 =
        field("educationAccounts.name.lastName", FieldType.Keyword, EDUCATION_ACCOUNTS_NESTED_PATH_V2);

    //Education Account  - Phone
    static final Field EDUCATION_ACCOUNT_PHONE_NUMBER_V2 =
        field("educationAccounts.phone.phoneNumber", FieldType.Keyword, EDUCATION_ACCOUNTS_NESTED_PATH_V2);

    static final Field EDUCATION_ACCOUNT_COUNTRY_CODE_V2 =
        field("educationAccounts.country.code", FieldType.Keyword, EDUCATION_ACCOUNTS_NESTED_PATH_V2);
    static final Field EDUCATION_ACCOUNT_COUNTRY_NAME_V2 =
        field("educationAccounts.country.name", FieldType.Keyword, EDUCATION_ACCOUNTS_NESTED_PATH_V2);

    //Education Account - Account Manager
    static final Field EDUCATION_ACCOUNT_MANAGER_ID_V2 =
        field("educationAccounts.accountManager.id", FieldType.Keyword, EDUCATION_ACCOUNTS_NESTED_PATH_V2);
    static final Field EDUCATION_ACCOUNT_MANAGER_FIRST_NAME_V2 =
        field("educationAccounts.accountManager.firstName", FieldType.Keyword, EDUCATION_ACCOUNTS_NESTED_PATH_V2);
    static final Field EDUCATION_ACCOUNT_MANAGER_LAST_NAME_V2 =
        field("educationAccounts.accountManager.lastName", FieldType.Keyword, EDUCATION_ACCOUNTS_NESTED_PATH_V2);

    static final Field EDUCATION_ACCOUNT_REGISTRATION_DATE_V2 =
        field("educationAccounts.registrationDate", FieldType.Date, EDUCATION_ACCOUNTS_NESTED_PATH_V2);
    static final Field EDUCATION_ACCOUNT_ACCEPTED_CONTACT_V2 =
        field("educationAccounts.acceptedContact", FieldType.Boolean, EDUCATION_ACCOUNTS_NESTED_PATH_V2);


    //Wallet Accounts
    static final Field WALLET_ACCOUNT_LOGIN_V2 =
        field("walletAccounts.login", FieldType.Long, WALLET_ACCOUNTS_NESTED_PATH_V2);
    static final Field WALLET_ACCOUNT_AFFILIATE_ID_V2 =
        field("walletAccounts.affiliateId", FieldType.Long, WALLET_ACCOUNTS_NESTED_PATH_V2);
    static final Field WALLET_ACCOUNT_REGISTRATION_DATE_V2 =
        field("walletAccounts.registrationDate", FieldType.Date, WALLET_ACCOUNTS_NESTED_PATH_V2);
    static final Field HAS_WALLET_ACCOUNTS_V2 =
        field("walletAccounts.login", FieldType.Boolean, WALLET_ACCOUNTS_NESTED_PATH_V2);

    //Wallet Account - Account Manager
    static final Field WALLET_ACCOUNT_MANAGER_ID_V2 =
        field("walletAccounts.accountManager.id", FieldType.Keyword, WALLET_ACCOUNTS_NESTED_PATH_V2);
    static final Field WALLET_ACCOUNT_MANAGER_FIRST_NAME_V2 =
        field("walletAccounts.accountManager.firstName", FieldType.Keyword, WALLET_ACCOUNTS_NESTED_PATH_V2);
    static final Field WALLET_ACCOUNT_MANAGER_LAST_NAME_V2 =
        field("walletAccounts.accountManager.lastName", FieldType.Keyword, WALLET_ACCOUNTS_NESTED_PATH_V2);


    //First Steps
    static final Field FIRST_STEP_ID_V2 = field("firstSteps.id", FieldType.Long, FIRST_STEPS_NESTED_PATH_V2);
    static final Field HAS_FIRST_STEPS_V2 = field("firstSteps.id", FieldType.Boolean, FIRST_STEPS_NESTED_PATH_V2);


    //First Step - Name
    static final Field FIRST_STEP_FIRST_NAME_V2 =
        field("firstSteps.name.firstName", FieldType.Keyword, FIRST_STEPS_NESTED_PATH_V2);
    static final Field FIRST_STEP_LAST_NAME_V2 =
        field("firstSteps.name.lastName", FieldType.Keyword, FIRST_STEPS_NESTED_PATH_V2);

    //First Step  - Phone
    static final Field FIRST_STEP_PHONE_NUMBER_V2 =
        field("firstSteps.phone.phoneNumber", FieldType.Keyword, FIRST_STEPS_NESTED_PATH_V2);

    static final Field FIRST_STEP_COUNTRY_CODE_V2 =
        field("firstSteps.country.code", FieldType.Keyword, FIRST_STEPS_NESTED_PATH_V2);
    static final Field FIRST_STEP_COUNTRY_NAME_V2 =
        field("firstSteps.country.name", FieldType.Keyword, FIRST_STEPS_NESTED_PATH_V2);

    //First Step - Account Manager
    static final Field FIRST_STEP_ACCOUNT_MANAGER_ID_V2 =
        field("firstSteps.accountManager.id", FieldType.Keyword, FIRST_STEPS_NESTED_PATH_V2);
    static final Field FIRST_STEP_ACCOUNT_MANAGER_FIRST_NAME_V2 =
        field("firstSteps.accountManager.firstName", FieldType.Keyword, FIRST_STEPS_NESTED_PATH_V2);
    static final Field FIRST_STEP_ACCOUNT_MANAGER_LAST_NAME_V2 =
        field("firstSteps.accountManager.lastName", FieldType.Keyword, FIRST_STEPS_NESTED_PATH_V2);

    //First Step - Affiliate
    static final Field FIRST_STEP_AFFILIATE_ID_V2 =
        field("firstSteps.affiliate.id", FieldType.Long, FIRST_STEPS_NESTED_PATH_V2);
    static final Field FIRST_STEP_AFFILIATE_FIRST_NAME_V2 =
        field("firstSteps.affiliate.firstName", FieldType.Keyword, FIRST_STEPS_NESTED_PATH_V2);
    static final Field FIRST_STEP_AFFILIATE_LAST_NAME_V2 =
        field("firstSteps.affiliate.lastName", FieldType.Keyword, FIRST_STEPS_NESTED_PATH_V2);

    //First Step - Affiliate Manager
    static final Field FIRST_STEP_AFFILIATE_MANAGER_ID_V2 =
        field("firstSteps.affiliateManager.id", FieldType.Keyword, FIRST_STEPS_NESTED_PATH_V2);
    static final Field FIRST_STEP_AFFILIATE_MANAGER_FIRST_NAME_V2 =
        field("firstSteps.affiliateManager.firstName", FieldType.Keyword, FIRST_STEPS_NESTED_PATH_V2);
    static final Field FIRST_STEP_AFFILIATE_MANAGER_LAST_NAME_V2 =
        field("firstSteps.affiliateManager.lastName", FieldType.Keyword, FIRST_STEPS_NESTED_PATH_V2);

    static final Field FIRST_STEP_REGISTRATION_DATE_V2 =
        field("firstSteps.registrationDate", FieldType.Date, FIRST_STEPS_NESTED_PATH_V2);

    //Second Steps
    static final Field SECOND_STEP_ID_V2 = field("secondSteps.id", FieldType.Long, SECOND_STEPS_NESTED_PATH_V2);
    static final Field HAS_SECOND_STEPS_V2 = field("secondSteps.id",
        FieldType.Boolean, SECOND_STEPS_NESTED_PATH_V2);


    //Second Step - Name
    static final Field SECOND_STEP_FIRST_NAME_V2 =
        field("secondSteps.name.firstName", FieldType.Keyword, SECOND_STEPS_NESTED_PATH_V2);
    static final Field SECOND_STEP_LAST_NAME_V2 =
        field("secondSteps.name.lastName", FieldType.Keyword, SECOND_STEPS_NESTED_PATH_V2);

    //Second Step  - Phone
    static final Field SECOND_STEP_PHONE_NUMBER_V2 =
        field("secondSteps.phone.phoneNumber", FieldType.Keyword, SECOND_STEPS_NESTED_PATH_V2);

    static final Field SECOND_STEP_COUNTRY_CODE_V2 =
        field("secondSteps.country.code", FieldType.Keyword, SECOND_STEPS_NESTED_PATH_V2);
    static final Field SECOND_STEP_COUNTRY_NAME_V2 =
        field("secondSteps.country.name", FieldType.Keyword, SECOND_STEPS_NESTED_PATH_V2);
    static final Field SECOND_STEP_CITY_V2 = field("secondSteps.city",
        FieldType.Keyword, SECOND_STEPS_NESTED_PATH_V2);
    static final Field SECOND_STEP_DATE_OF_BIRTH_V2 = field("secondSteps.dateOfBirth",
        FieldType.Date, SECOND_STEPS_NESTED_PATH_V2);

    //Second Step - Account Manager
    static final Field SECOND_STEP_ACCOUNT_MANAGER_ID_V2 =
        field("secondSteps.accountManager.id", FieldType.Keyword, SECOND_STEPS_NESTED_PATH_V2);
    static final Field SECOND_STEP_ACCOUNT_MANAGER_FIRST_NAME_V2 =
        field("secondSteps.accountManager.firstName", FieldType.Keyword, SECOND_STEPS_NESTED_PATH_V2);
    static final Field SECOND_STEP_ACCOUNT_MANAGER_LAST_NAME_V2 =
        field("secondSteps.accountManager.lastName", FieldType.Keyword, SECOND_STEPS_NESTED_PATH_V2);

    //Second Step - Affiliate
    static final Field SECOND_STEP_AFFILIATE_ID_V2 =
        field("secondSteps.affiliate.id", FieldType.Long, SECOND_STEPS_NESTED_PATH_V2);
    static final Field SECOND_STEP_AFFILIATE_FIRST_NAME_V2 =
        field("secondSteps.affiliate.firstName", FieldType.Keyword, SECOND_STEPS_NESTED_PATH_V2);
    static final Field SECOND_STEP_AFFILIATE_LAST_NAME_V2 =
        field("secondSteps.affiliate.lastName", FieldType.Keyword, SECOND_STEPS_NESTED_PATH_V2);

    //Second Step - Affiliate Manager
    static final Field SECOND_STEP_AFFILIATE_MANAGER_ID_V2 =
        field("secondSteps.affiliateManager.id", FieldType.Keyword, SECOND_STEPS_NESTED_PATH_V2);
    static final Field SECOND_STEP_AFFILIATE_MANAGER_FIRST_NAME_V2 =
        field("secondSteps.affiliateManager.firstName", FieldType.Keyword, SECOND_STEPS_NESTED_PATH_V2);
    static final Field SECOND_STEP_AFFILIATE_MANAGER_LAST_NAME_V2 =
        field("secondSteps.affiliateManager.lastName", FieldType.Keyword, SECOND_STEPS_NESTED_PATH_V2);

    static final Field SECOND_STEP_REGISTRATION_DATE_V2 =
        field("secondSteps.registrationDate", FieldType.Date, SECOND_STEPS_NESTED_PATH_V2);

    private static Field nested(final String name, final FieldType type) {
        return field(name, type, TRADING_ACCOUNTS_NESTED_PATH_V2);
    }

    private static Field field(final String name, final FieldType type) {
        return field(name, type, null);
    }

    private static Field field(final String name, final FieldType type, final String path) {
        SimpleField field = new SimpleField(name);
        field.setFieldType(type);
        field.setPath(path);
        return field;
    }
}
