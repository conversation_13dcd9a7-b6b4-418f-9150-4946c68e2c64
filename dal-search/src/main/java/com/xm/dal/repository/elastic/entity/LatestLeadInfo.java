package com.xm.dal.repository.elastic.entity;

import static java.util.Objects.nonNull;
import static java.util.Objects.requireNonNull;

import java.time.OffsetDateTime;
import java.util.List;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Data;
import lombok.Setter;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.lang.NonNull;

@Builder(toBuilder = true)
@Setter(AccessLevel.PRIVATE)
@Data
public class LatestLeadInfo {
    @Field(storeNullValue = true) private SimpleName name;
    @Field(storeNullValue = true) private LeadPhone phone;
    @Field(storeNullValue = true) private Country country;
    @Field(storeNullValue = true) private Language preferredLanguage;
    private OffsetDateTime registrationDate;
    private Comment comment;
    private List<Rating> ratings;

    public void mergeWith(@NonNull final LatestLeadInfo latestInfo) {
        requireNonNull(latestInfo);

        this.name = nonNull(latestInfo.getName()) ? latestInfo.getName() : this.getName();
        this.phone = nonNull(latestInfo.getPhone()) ? latestInfo.getPhone() : this.getPhone();
        this.country = nonNull(latestInfo.getCountry()) ? latestInfo.getCountry() : this.getCountry();
        this.preferredLanguage = nonNull(latestInfo.getPreferredLanguage())
            ? latestInfo.getPreferredLanguage() : this.getPreferredLanguage();
        this.registrationDate = nonNull(latestInfo.getRegistrationDate())
            ? latestInfo.getRegistrationDate() : this.getRegistrationDate();
        this.comment = nonNull(latestInfo.getComment()) ? latestInfo.getComment() : this.getComment();
        this.ratings = nonNull(latestInfo.getRatings()) ? latestInfo.getRatings() : this.getRatings();
    }
}
