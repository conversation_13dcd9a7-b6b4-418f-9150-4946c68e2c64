package com.xm.dal.repository.elastic.query;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.data.elasticsearch.annotations.FieldType;
import org.springframework.data.elasticsearch.core.query.Field;
import org.springframework.data.elasticsearch.core.query.SimpleField;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
class ProfileDocumentMapping {
    static final Field BRAND_ACCOUNT_ID = field("brandAccountId", FieldType.Long);
    static final Field BRAND_ID = field("brand.brandId", FieldType.Byte);
    static final Field BRAND_NAME = field("brand.brandName", FieldType.Text);
    static final Field EMAIL = field("email", FieldType.Text);
    static final Field PROFILE_ID = field("profileId", FieldType.Long);

    // Profile Data
    static final Field CITY = field("profileData.address.city", FieldType.Text);
    static final Field COUNTRY_CODE = field("profileData.country.code", FieldType.Text);
    static final Field COUNTRY_NAME = field("profileData.country.name", FieldType.Text);
    static final Field DATE_OF_BIRTH = field("profileData.dateOfBirth", FieldType.Date);
    static final Field FIRST_NAME = field("profileData.name.firstName", FieldType.Text);
    static final Field INTERNATIONAL_PHONE_NUMBER = field("profileData.phones.international", FieldType.Text);
    static final Field IS_PROFESSIONAL_CLIENT = field("isProfessionalClient", FieldType.Boolean);
    static final Field LAST_NAME = field("profileData.name.lastName", FieldType.Text);
    static final Field PHONE_NUMBER = field("profileData.phones.phoneNumber", FieldType.Text);
    static final Field PREFERRED_LANGUAGE_CODE = field("profileData.preferredLanguage.code", FieldType.Text);
    static final Field REGISTRATION_DATE = field("profileData.registrationDate", FieldType.Date);
    static final Field REGISTRATION_STATUS = field("profileData.registrationStatus", FieldType.Text);

    // Validation Status
    static final Field IS_VALIDATED = field("validationStatus.isValidated", FieldType.Boolean);
    static final Field IS_REVALIDATION_REQUESTED = field("validationStatus.isRevalidationRequested", FieldType.Boolean);
    static final Field DOCUMENT_EXPIRATION_DATE = field("validationStatus.documentExpirationDate", FieldType.Date);
    static final Field VALIDATION_DATE = field("validationStatus.validationDate", FieldType.Date);

    // Ratings
    static final Field RATING_ID = field("ratings.id", FieldType.Integer);

    // Comments
    static final Field COMMENT_DATE_ADDED = field("comments.dateAdded", FieldType.Date);

    // Labels
    static final Field LABEL_ID = field("labels.id", FieldType.Long);

    // Trading Accounts
    static final Field ACCOUNT_MANAGER_FIRST_NAME = field("tradingAccounts.accountManager.firstName", FieldType.Text);
    static final Field ACCOUNT_MANAGER_ID = field("tradingAccounts.accountManager.id", FieldType.Text);
    static final Field ACCOUNT_MANAGER_LAST_NAME = field("tradingAccounts.accountManager.lastName", FieldType.Text);
    static final Field ACCOUNT_STATUS = field("tradingAccounts.accountStatus", FieldType.Keyword);
    static final Field LOGIN = field("tradingAccounts.login", FieldType.Long);
    static final Field ACCOUNT_TYPE_ID = field("tradingAccounts.accountType.id", FieldType.Integer);
    static final Field AFFILIATE_FIRST_NAME = field("tradingAccounts.affiliate.firstName", FieldType.Text);
    static final Field AFFILIATE_ID = field("tradingAccounts.affiliate.id", FieldType.Long);
    static final Field AFFILIATE_LAST_NAME = field("tradingAccounts.affiliate.lastName", FieldType.Text);
    static final Field AFFILIATE_MANAGER_FIRST_NAME =
        field("tradingAccounts.affiliateManager.firstName", FieldType.Text);
    static final Field AFFILIATE_MANAGER_ID = field("tradingAccounts.affiliateManager.id", FieldType.Text);
    static final Field AFFILIATE_MANAGER_LAST_NAME = field("tradingAccounts.affiliateManager.lastName", FieldType.Text);
    static final Field COMMENT = field("tradingAccounts.comment", FieldType.Text);
    static final Field CURRENCY = field("tradingAccounts.currency", FieldType.Keyword);
    static final Field GROUP_NAME = field("tradingAccounts.groupName", FieldType.Text);
    static final Field HAS_ACTIVE_TRADING_ACCOUNTS = field("hasActiveTradingAccounts", FieldType.Boolean);
    static final Field IS_ISLAMIC = field("tradingAccounts.isIslamic", FieldType.Boolean);
    static final Field LATEST_TRADE_DATE = field("latestTradeDate", FieldType.Date);
    static final Field LEVERAGE = field("tradingAccounts.leverage", FieldType.Integer);
    static final Field MEMBERS_AREA_LAST_LOGIN =
        field("tradingAccounts.loginActivity.membersAreaLastLogin", FieldType.Date);
    static final Field MOBILE_LAST_LOGIN = field("tradingAccounts.loginActivity.mobileLastLogin", FieldType.Date);
    static final Field MQ_TERMINAL_LAST_LOGIN =
        field("tradingAccounts.loginActivity.mqTerminalLastLogin", FieldType.Date);
    static final Field PLATFORM_TYPE = field("tradingAccounts.serverInfo.platformType", FieldType.Keyword);
    static final Field SOCIAL_TRADING_STATUS = field("tradingAccounts.socialTradingStatus", FieldType.Text);
    static final Field WEB_TRADER_LAST_LOGIN =
        field("tradingAccounts.loginActivity.webTraderLastLogin", FieldType.Date);
    static final Field GID = field("tradingAccounts.gid", FieldType.Integer);
    static final Field ACCOUNT_REGISTRATION_DATE = field("tradingAccounts.registrationDate", FieldType.Date);

    // Financial Info
    static final Field DEPOSIT_COUNT = field("financialInfo.depositCount", FieldType.Integer);
    static final Field FIRST_DEPOSIT_DATE = field("financialInfo.firstDepositDate", FieldType.Date);
    static final Field LATEST_DEPOSIT_DATE = field("financialInfo.latestDepositDate", FieldType.Date);
    static final Field LATEST_MONTH_PNL_EUR = field("financialInfo.latestMonthPnlEur", FieldType.Float);
    static final Field TOTAL_BALANCE_EUR = field("financialInfo.totalBalanceEur", FieldType.Float);
    static final Field TOTAL_CREDIT_EUR = field("financialInfo.totalCreditEur", FieldType.Float);
    static final Field TOTAL_DEPOSITS_EUR = field("financialInfo.totalDepositsEur", FieldType.Float);
    static final Field TOTAL_EQUITY_EUR = field("financialInfo.totalEquityEur", FieldType.Float);
    static final Field TOTAL_PNL_EUR = field("financialInfo.totalPnlEur", FieldType.Float);
    static final Field TOTAL_WITHDRAWALS_EUR = field("financialInfo.totalWithdrawalsEur", FieldType.Float);

    // Raf Info
    static final Field IS_RAF_QUALIFIED = field("rafInfo.isRafQualified", FieldType.Boolean);
    static final Field IS_REFERRER = field("rafInfo.isReferrer", FieldType.Boolean);
    static final Field IS_REFERRED_CLIENT = field("rafInfo.isReferredClient", FieldType.Boolean);
    static final Field RAF_CONNECTION_STATUS_ID = field("rafInfo.connectionStatusId", FieldType.Byte);
    static final Field REFERRER_TOKEN = field("rafInfo.referrerToken", FieldType.Keyword);

    // Phone Calls
    static final Field LATEST_INVALID_PHONE_CALL_DATE =
        field("phoneCallsInfo.latestInvalidPhoneCallDate", FieldType.Date);
    static final Field LATEST_PHONE_CALL_ATTEMPT_DATE =
        field("phoneCallsInfo.latestPhoneCallAttemptDate", FieldType.Date);
    static final Field LATEST_VALID_PHONE_CALL_DATE =
        field("phoneCallsInfo.latestValidPhoneCallDate", FieldType.Date);
    static final Field TOTAL_ATTEMPTS_BEFORE_ACTIVATION =
        field("phoneCallsInfo.totalAttemptsBeforeClientActivation", FieldType.Integer);
    static final Field TOTAL_INVALID_PHONE_CALLS =
        field("phoneCallsInfo.totalInvalidPhoneCalls", FieldType.Integer);
    static final Field TOTAL_INVALID_PHONE_CALLS_BEFORE_ACTIVATION =
        field("phoneCallsInfo.totalInvalidBeforeClientActivation", FieldType.Integer);
    static final Field TOTAL_OTHER_PHONE_CALLS =
        field("phoneCallsInfo.totalOtherCommunicatedPhoneCalls", FieldType.Integer);
    static final Field TOTAL_PHONE_CALL_ATTEMPTS =
        field("phoneCallsInfo.totalPhoneCallAttempts", FieldType.Integer);
    static final Field TOTAL_VALID_PHONE_CALLS_BEFORE_ACTIVATION =
        field("phoneCallsInfo.totalValidBeforeClientActivation", FieldType.Integer);

    // Confirmations
    static final Field PROMO_AGREED = field("confirmations.promoAgreed", FieldType.Boolean);

    // Callback Requests
    static final Field CALLBACK_REQUEST_STATUS = field("callbackRequests.status", FieldType.Keyword);

    // Others
    static final Field SAT = field("sat", FieldType.Boolean);
    static final Field LATEST_EMAIL_DATE = field("latestEmailDate", FieldType.Date);

    private static Field field(final String name, final FieldType type) {
        SimpleField field = new SimpleField(name);
        field.setFieldType(type);
        return field;
    }
}
