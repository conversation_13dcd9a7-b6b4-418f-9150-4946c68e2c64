package com.xm.dal.repository.elastic.entity;

import static java.util.Objects.isNull;

import javax.annotation.Nullable;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Data;
import lombok.Setter;
import org.springframework.data.elasticsearch.annotations.Field;

@Builder(toBuilder = true)
@Setter(AccessLevel.PRIVATE)
@Data
public class LeadData {
    @Field(storeNullValue = true) private LatestLeadInfo latestInfo;

    public void updateLatestLeadInfo(@Nullable final LatestLeadInfo latestInfo) {
        if (isNull(latestInfo)) {
            this.latestInfo = null;
        } else if (isNull(this.latestInfo)) {
            this.latestInfo = latestInfo;
        } else {
            this.latestInfo.mergeWith(latestInfo);
        }
    }
}
