package com.xm.dal.repository.elastic.query;

import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.ACCOUNT_MANAGER_FIRST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.ACCOUNT_MANAGER_ID_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.ACCOUNT_MANAGER_LAST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.ACCOUNT_REGISTRATION_DATE_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.ACCOUNT_STATUS_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.ACCOUNT_TYPE_ID_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.AFFILIATE_FIRST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.AFFILIATE_ID_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.AFFILIATE_LAST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.AFFILIATE_MANAGER_FIRST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.AFFILIATE_MANAGER_ID_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.AFFILIATE_MANAGER_LAST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.BRAND_ACCOUNT_ID_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.BRAND_ID_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.BRAND_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.CALLBACK_REQUEST_STATUS_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.CITY_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.COMMENT_DATE_ADDED_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.COMMENT_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.COUNTRY_CODE_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.COUNTRY_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.CURRENCY_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.DATE_OF_BIRTH_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.DEMO_ACCOUNT_MANAGER_FIRST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.DEMO_ACCOUNT_MANAGER_ID_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.DEMO_ACCOUNT_MANAGER_LAST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.DEMO_AFFILIATE_FIRST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.DEMO_AFFILIATE_ID_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.DEMO_AFFILIATE_LAST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.DEMO_AFFILIATE_MANAGER_FIRST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.DEMO_AFFILIATE_MANAGER_ID_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.DEMO_AFFILIATE_MANAGER_LAST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.DEMO_COUNTRY_CODE_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.DEMO_COUNTRY_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.DEMO_FIRST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.DEMO_LAST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.DEMO_LOGIN_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.DEMO_PHONE_NUMBER_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.DEMO_REGISTRATION_DATE_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.DEPOSIT_COUNT_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.DOCUMENT_EXPIRATION_DATE_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.EDUCATION_ACCOUNT_ACCEPTED_CONTACT_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.EDUCATION_ACCOUNT_COUNTRY_CODE_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.EDUCATION_ACCOUNT_COUNTRY_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.EDUCATION_ACCOUNT_FIRST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.EDUCATION_ACCOUNT_ID_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.EDUCATION_ACCOUNT_LAST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.EDUCATION_ACCOUNT_MANAGER_FIRST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.EDUCATION_ACCOUNT_MANAGER_ID_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.EDUCATION_ACCOUNT_MANAGER_LAST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.EDUCATION_ACCOUNT_PHONE_NUMBER_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.EDUCATION_ACCOUNT_REGISTRATION_DATE_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.EMAIL_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.FIRST_DEPOSIT_DATE_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.FIRST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.FIRST_STEP_ACCOUNT_MANAGER_FIRST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.FIRST_STEP_ACCOUNT_MANAGER_ID_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.FIRST_STEP_ACCOUNT_MANAGER_LAST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.FIRST_STEP_AFFILIATE_FIRST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.FIRST_STEP_AFFILIATE_ID_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.FIRST_STEP_AFFILIATE_LAST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.FIRST_STEP_AFFILIATE_MANAGER_FIRST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.FIRST_STEP_AFFILIATE_MANAGER_ID_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.FIRST_STEP_AFFILIATE_MANAGER_LAST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.FIRST_STEP_COUNTRY_CODE_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.FIRST_STEP_COUNTRY_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.FIRST_STEP_FIRST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.FIRST_STEP_ID_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.FIRST_STEP_LAST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.FIRST_STEP_PHONE_NUMBER_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.FIRST_STEP_REGISTRATION_DATE_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.GID_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.GROUP_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.HAS_ACTIVE_TRADING_ACCOUNTS_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.HAS_DEMOS_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.HAS_EDUCATION_ACCOUNTS_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.HAS_FIRST_STEPS_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.HAS_SECOND_STEPS_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.HAS_WALLET_ACCOUNTS_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.HAS_WEBINARS_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.INTERNATIONAL_PHONE_NUMBER_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.IS_AFFILIATED_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.IS_ISLAMIC_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.IS_PROFESSIONAL_CLIENT_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.IS_RAF_QUALIFIED_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.IS_REFERRED_CLIENT_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.IS_REFERRER_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.IS_REVALIDATION_REQUESTED_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.IS_VALIDATED_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.LABEL_ID_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.LAST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.LATEST_COMMENT_ID_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.LATEST_DEPOSIT_DATE_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.LATEST_EMAIL_DATE_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.LATEST_INVALID_PHONE_CALL_DATE_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.LATEST_LEAD_REGISTRATION_DATE_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.LATEST_MONTH_PNL_EUR_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.LATEST_PHONE_CALL_ATTEMPT_DATE_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.LATEST_TRADE_DATE_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.LATEST_VALID_PHONE_CALL_DATE_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.LEAD_ID_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.LEVERAGE_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.LOGIN_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.MEMBERS_AREA_LAST_LOGIN_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.MOBILE_LAST_LOGIN_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.MQ_TERMINAL_LAST_LOGIN_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.PHONE_NUMBER_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.PLATFORM_TYPE_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.PREFERRED_LANGUAGE_CODE_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.PROFILE_ID_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.PROFILE_STATUS_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.PROMO_AGREED_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.RAF_CONNECTION_STATUS_ID_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.RATING_ID_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.REFERRER_TOKEN_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.REGISTRATION_DATE_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.REGISTRATION_STATUS_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.SAT_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.SECOND_STEP_ACCOUNT_MANAGER_FIRST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.SECOND_STEP_ACCOUNT_MANAGER_ID_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.SECOND_STEP_ACCOUNT_MANAGER_LAST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.SECOND_STEP_AFFILIATE_FIRST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.SECOND_STEP_AFFILIATE_ID_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.SECOND_STEP_AFFILIATE_LAST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.SECOND_STEP_AFFILIATE_MANAGER_FIRST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.SECOND_STEP_AFFILIATE_MANAGER_ID_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.SECOND_STEP_AFFILIATE_MANAGER_LAST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.SECOND_STEP_CITY_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.SECOND_STEP_COUNTRY_CODE_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.SECOND_STEP_COUNTRY_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.SECOND_STEP_DATE_OF_BIRTH_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.SECOND_STEP_FIRST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.SECOND_STEP_ID_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.SECOND_STEP_LAST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.SECOND_STEP_PHONE_NUMBER_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.SECOND_STEP_REGISTRATION_DATE_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.SOCIAL_TRADING_STATUS_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.TOTAL_ATTEMPTS_BEFORE_ACTIVATION_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.TOTAL_BALANCE_EUR_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.TOTAL_CREDIT_EUR_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.TOTAL_DEPOSITS_EUR_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.TOTAL_EQUITY_EUR_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.TOTAL_INVALID_PHONE_CALLS_BEFORE_ACTIVATION_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.TOTAL_INVALID_PHONE_CALLS_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.TOTAL_OTHER_PHONE_CALLS_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.TOTAL_PHONE_CALL_ATTEMPTS_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.TOTAL_PNL_EUR_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.TOTAL_VALID_PHONE_CALLS_BEFORE_ACTIVATION_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.TOTAL_WITHDRAWALS_EUR_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.VALIDATION_DATE_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.WALLET_ACCOUNT_AFFILIATE_ID_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.WALLET_ACCOUNT_LOGIN_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.WALLET_ACCOUNT_MANAGER_FIRST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.WALLET_ACCOUNT_MANAGER_ID_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.WALLET_ACCOUNT_MANAGER_LAST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.WALLET_ACCOUNT_REGISTRATION_DATE_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.WEBINAR_ACCOUNT_MANAGER_FIRST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.WEBINAR_ACCOUNT_MANAGER_ID_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.WEBINAR_ACCOUNT_MANAGER_LAST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.WEBINAR_AFFILIATE_FIRST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.WEBINAR_AFFILIATE_ID_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.WEBINAR_AFFILIATE_LAST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.WEBINAR_AFFILIATE_MANAGER_FIRST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.WEBINAR_AFFILIATE_MANAGER_ID_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.WEBINAR_AFFILIATE_MANAGER_LAST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.WEBINAR_CITY_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.WEBINAR_COUNTRY_CODE_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.WEBINAR_COUNTRY_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.WEBINAR_FIRST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.WEBINAR_ID_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.WEBINAR_LAST_NAME_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.WEBINAR_PHONE_NUMBER_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.WEBINAR_REGISTRATION_DATE_V2;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMappingV2.WEB_TRADER_LAST_LOGIN_V2;
import static com.xm.dal.util.Nullable.acceptIfPresent;
import static com.xm.dal.util.Nullable.applyIfPresent;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.partitioningBy;

import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.ExistsQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.NestedQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.QueryBuilders;
import co.elastic.clients.elasticsearch._types.query_dsl.TermsQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.TermsQueryField;
import co.elastic.clients.json.JsonData;
import co.elastic.clients.json.JsonpMapper;
import com.xm.dal.api.input.BooleanFilterInput;
import com.xm.dal.api.input.ComparableFilterInput;
import com.xm.dal.api.input.FilterInput;
import com.xm.dal.api.input.SortDirection;
import com.xm.dal.api.input.StringFilterInput;
import com.xm.dal.api.input.search.SearchProfilesFilterInput;
import com.xm.dal.api.input.search.SearchProfilesInput;
import com.xm.dal.api.input.search.SearchProfilesSortingInput;
import com.xm.dal.domain.ProfileStatus;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Stream;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.client.elc.NativeQuery;
import org.springframework.data.elasticsearch.core.query.Field;
import org.springframework.data.elasticsearch.core.query.Order;
import org.springframework.data.elasticsearch.core.query.Query;
import org.springframework.stereotype.Component;

@Component
class SearchProfilesNativeQueryBuilder extends AbstractSearchProfilesQueryBuilder {

    private final JsonpMapper elasticJsonpMapper;

    public SearchProfilesNativeQueryBuilder(final QueryOffsetUtils queryOffsetUtils,
                                            final JsonpMapper elasticJsonpMapper) {
        super(queryOffsetUtils);
        this.elasticJsonpMapper = elasticJsonpMapper;
    }

    @Override
    protected Query buildQuery(final SearchProfilesInput input) {
        return new NativeQuery(
            QueryBuilders.bool()
                .filter(profileComplete()._toQuery())
                .filter(profileStatus(input.profileStatus())._toQuery())
                .filter(buildQuery(input.filter(), Operator.AND, Set.of()).builder().build()._toQuery())
                .build()._toQuery()
        );
    }

    private BuilderContext buildQuery(final SearchProfilesFilterInput filter,
                                      final Operator operator,
                                      final Set<FieldQuery> globalNestedQueries) {

        List<FieldQuery> fieldQueries = buildFilters(filter);

        Map<Boolean, List<FieldQuery>> fieldQueriesByNested = fieldQueries.stream()
            .collect(partitioningBy(FieldQuery::isNested));

        List<FieldQuery> nonNestedQueries = fieldQueriesByNested.get(false);
        BoolQuery.Builder query = QueryBuilders.bool();
        query.filter(nonNestedQueries.stream().map(FieldQuery::query).toList());

        List<FieldQuery> nestedQueries = fieldQueriesByNested.get(true);
        if (!nestedQueries.isEmpty()) {
            Map<String, List<FieldQuery>> queriesByNestedPath = nestedQueries.stream()
                .collect(groupingBy(FieldQuery::nestedPath));
            Map<String, List<FieldQuery>> globalQueriesByNestedPath = globalNestedQueries.stream()
                .collect(groupingBy(FieldQuery::nestedPath));

            queriesByNestedPath.forEach((path, queries) -> {
                BoolQuery.Builder nestedBuilder = QueryBuilders.bool();
                Stream.of(globalQueriesByNestedPath.getOrDefault(path, List.of()), queries)
                    .flatMap(Collection::stream)
                    .forEach(fieldQuery -> nestedBuilder.filter(fieldQuery.query()));

                NestedQuery.Builder nestedQuery = QueryBuilders.nested().path(path);
                nestedQuery.query(nestedBuilder.build()._toQuery());
                query.filter(nestedQuery.build()._toQuery());
            });
        }
        Set<FieldQuery> extendedGlobalNestedQueries = new HashSet<>(globalNestedQueries);
        if (!nestedQueries.isEmpty() && !operator.equals(Operator.OR)) {
            extendedGlobalNestedQueries.addAll(nestedQueries);
        }

        acceptIfPresent(filter.and(), andFilters ->
            andFilters.stream()
                .map(f -> {
                    BuilderContext ctx = buildQuery(f, Operator.AND, extendedGlobalNestedQueries);
                    extendedGlobalNestedQueries.addAll(ctx.nestedQueries());
                    return ctx.builder();
                })
                .forEach(f -> query.filter(f.build()._toQuery())));

        BoolQuery.Builder orSubQuery = applyIfPresent(filter.or(), orFilters ->
            orFilters.stream()
                .map(f -> buildQuery(f, Operator.OR, extendedGlobalNestedQueries))
                .map(BuilderContext::builder)
                .reduce(QueryBuilders.bool(), (a, b) -> a.should(b.build()._toQuery())));

        BoolQuery.Builder notSubQuery = applyIfPresent(filter.not(), notFilter ->
            QueryBuilders.bool().mustNot(
                buildQuery(notFilter, Operator.NOT, extendedGlobalNestedQueries).builder().build()._toQuery()));

        Stream.of(orSubQuery, notSubQuery)
            .filter(Objects::nonNull)
            .forEach(subQuery -> query.filter(subQuery.build()._toQuery()));

        return new BuilderContext(extendedGlobalNestedQueries, query);
    }

    private List<FieldQuery> buildFilters(final SearchProfilesFilterInput filter) {
        return Stream.of(
                buildFilter(PROFILE_ID_V2, filter.profileId()),
                buildFilter(BRAND_ACCOUNT_ID_V2, filter.brandAccountId()),
                buildFilter(LEAD_ID_V2, filter.leadId()),
                buildFilter(FIRST_NAME_V2, filter.firstName()),
                buildFilter(LAST_NAME_V2, filter.lastName()),
                buildFilter(BRAND_ID_V2, filter.brandId()),
                buildFilter(BRAND_NAME_V2, filter.brandName()),
                buildFilter(EMAIL_V2, filter.email()),
                buildFilter(COUNTRY_CODE_V2, filter.countryCode()),
                buildFilter(COUNTRY_NAME_V2, filter.countryName()),
                buildFilter(PHONE_NUMBER_V2, filter.phoneNumber()),
                buildFilter(DATE_OF_BIRTH_V2, filter.dateOfBirth()),
                buildFilter(CITY_V2, filter.city()),
                buildFilter(REGISTRATION_DATE_V2, filter.registrationDate()),
                buildFilter(REGISTRATION_STATUS_V2, filter.registrationStatus()),
                buildFilter(INTERNATIONAL_PHONE_NUMBER_V2, filter.internationalPhoneNumber()),
                buildFilter(IS_PROFESSIONAL_CLIENT_V2, filter.isProfessionalClient()),
                buildFilter(PREFERRED_LANGUAGE_CODE_V2, filter.preferredLanguage()),
                buildFilter(ACCOUNT_MANAGER_ID_V2, filter.accountManagerLdapId()),
                buildFilter(ACCOUNT_MANAGER_FIRST_NAME_V2, filter.accountManagerFirstName()),
                buildFilter(ACCOUNT_MANAGER_LAST_NAME_V2, filter.accountManagerLastName()),
                buildFilter(AFFILIATE_MANAGER_ID_V2, filter.affiliateManagerLdapId()),
                buildFilter(AFFILIATE_MANAGER_FIRST_NAME_V2, filter.affiliateManagerFirstName()),
                buildFilter(AFFILIATE_MANAGER_LAST_NAME_V2, filter.affiliateManagerLastName()),
                buildFilter(AFFILIATE_ID_V2, filter.affiliateId()),
                buildFilter(AFFILIATE_FIRST_NAME_V2, filter.affiliateFirstName()),
                buildFilter(AFFILIATE_LAST_NAME_V2, filter.affiliateLastName()),
                buildFilter(IS_VALIDATED_V2, filter.validationStatus()),
                buildFilter(DOCUMENT_EXPIRATION_DATE_V2, filter.documentExpirationDate()),
                buildFilter(IS_REVALIDATION_REQUESTED_V2, filter.isRevalidationRequested()),
                buildFilter(VALIDATION_DATE_V2, filter.validationDate()),
                buildFilter(RATING_ID_V2, filter.ratingOptionId()),
                buildFilter(COMMENT_DATE_ADDED_V2, filter.latestCommentAdded()),
                buildFilter(LOGIN_V2, filter.login()),
                buildFilter(ACCOUNT_TYPE_ID_V2, filter.accountTypeId()),
                buildFilter(GROUP_NAME_V2, filter.groupName()),
                buildFilter(HAS_ACTIVE_TRADING_ACCOUNTS_V2, filter.hasActiveTradingAccounts()),
                buildPrecalculatedFilter(filter.hasDemoAccounts(), DEMO_LOGIN_V2, HAS_DEMOS_V2),
                buildPrecalculatedFilter(filter.hasWebinarAccounts(), WEBINAR_ID_V2, HAS_WEBINARS_V2),
                buildPrecalculatedFilter(filter.hasEducationAccounts(), EDUCATION_ACCOUNT_ID_V2,
                    HAS_EDUCATION_ACCOUNTS_V2),
                buildPrecalculatedFilter(filter.hasWalletAccounts(), WALLET_ACCOUNT_LOGIN_V2,
                    HAS_WALLET_ACCOUNTS_V2),
                buildPrecalculatedFilter(filter.hasFirstSteps(), FIRST_STEP_ID_V2, HAS_FIRST_STEPS_V2),
                buildPrecalculatedFilter(filter.hasSecondSteps(), SECOND_STEP_ID_V2, HAS_SECOND_STEPS_V2),
                buildPrecalculatedFilter(filter.isAffiliated(), AFFILIATE_ID_V2, IS_AFFILIATED_V2),
                buildFilter(IS_ISLAMIC_V2, filter.isIslamic()),
                buildFilter(LEVERAGE_V2, filter.leverage()),
                buildFilter(MEMBERS_AREA_LAST_LOGIN_V2, filter.membersAreaLastLogin()),
                buildFilter(MOBILE_LAST_LOGIN_V2, filter.mobileLastLogin()),
                buildFilter(MQ_TERMINAL_LAST_LOGIN_V2, filter.mqTerminalLastLogin()),
                buildFilter(WEB_TRADER_LAST_LOGIN_V2, filter.webTraderLastLogin()),
                buildFilter(GID_V2, filter.gid()),
                buildFilter(ACCOUNT_REGISTRATION_DATE_V2, filter.tradingAccountRegistrationDate()),
                buildFilter(SOCIAL_TRADING_STATUS_V2, filter.socialTradingStatus()),
                buildFilter(COMMENT_V2, filter.userComment()),
                buildFilter(CURRENCY_V2, filter.currency()),
                buildFilter(ACCOUNT_STATUS_V2, filter.accountStatus()),
                buildFilter(PLATFORM_TYPE_V2, filter.platformType()),
                buildFilter(LATEST_TRADE_DATE_V2, filter.latestTradeDate()),
                buildFilter(DEPOSIT_COUNT_V2, filter.depositCount()),
                buildFilter(FIRST_DEPOSIT_DATE_V2, filter.firstDepositDate()),
                buildFilter(LATEST_DEPOSIT_DATE_V2, filter.latestDepositDate()),
                buildFilter(LATEST_MONTH_PNL_EUR_V2, filter.latestMonthPnlEur()),
                buildFilter(TOTAL_BALANCE_EUR_V2, filter.totalBalanceEur()),
                buildFilter(TOTAL_CREDIT_EUR_V2, filter.totalCreditEur()),
                buildFilter(TOTAL_DEPOSITS_EUR_V2, filter.totalDepositsEur()),
                buildFilter(TOTAL_EQUITY_EUR_V2, filter.totalEquityEur()),
                buildFilter(TOTAL_PNL_EUR_V2, filter.totalPnlEur()),
                buildFilter(TOTAL_WITHDRAWALS_EUR_V2, filter.totalWithdrawalsEur()),
                buildFilter(IS_REFERRER_V2, filter.isReferrer()),
                buildFilter(IS_REFERRED_CLIENT_V2, filter.isReferredClient()),
                buildFilter(IS_RAF_QUALIFIED_V2, filter.isRafQualified()),
                buildFilter(RAF_CONNECTION_STATUS_ID_V2, filter.rafConnectionStatusId()),
                buildFilter(REFERRER_TOKEN_V2, filter.referrerToken()),
                buildFilter(LATEST_VALID_PHONE_CALL_DATE_V2, filter.latestValidPhoneCallDate()),
                buildFilter(LATEST_INVALID_PHONE_CALL_DATE_V2, filter.latestInvalidPhoneCallDate()),
                buildFilter(TOTAL_INVALID_PHONE_CALLS_V2, filter.totalInvalidPhoneCalls()),
                buildFilter(LATEST_PHONE_CALL_ATTEMPT_DATE_V2, filter.latestPhoneCallAttemptDate()),
                buildFilter(TOTAL_PHONE_CALL_ATTEMPTS_V2, filter.totalPhoneCallAttempts()),
                buildFilter(TOTAL_VALID_PHONE_CALLS_BEFORE_ACTIVATION_V2, filter.totalValidBeforeClientActivation()),
                buildFilter(TOTAL_INVALID_PHONE_CALLS_BEFORE_ACTIVATION_V2,
                    filter.totalInvalidBeforeClientActivation()),
                buildFilter(TOTAL_ATTEMPTS_BEFORE_ACTIVATION_V2, filter.totalAttemptsBeforeClientActivation()),
                buildFilter(TOTAL_OTHER_PHONE_CALLS_V2, filter.totalOtherCommunicatedPhoneCalls()),
                buildFilter(LABEL_ID_V2, filter.labelId()),
                buildFilter(LATEST_EMAIL_DATE_V2, filter.latestEmailDate()),
                buildFilter(SAT_V2, filter.sat()),
                buildFilter(PROMO_AGREED_V2, filter.promoAgreed()),
                buildFilter(CALLBACK_REQUEST_STATUS_V2, filter.callbackRequestStatus()),
                buildFilter(DEMO_REGISTRATION_DATE_V2, filter.demoAccountRegistrationDate()),
                buildFilter(DEMO_FIRST_NAME_V2, filter.demoAccountFirstName()),
                buildFilter(DEMO_LAST_NAME_V2, filter.demoAccountLastName()),
                buildFilter(DEMO_COUNTRY_CODE_V2, filter.demoAccountCountryCode()),
                buildFilter(DEMO_COUNTRY_NAME_V2, filter.demoAccountCountryName()),
                buildFilter(DEMO_PHONE_NUMBER_V2, filter.demoAccountPhoneNumber()),
                buildFilter(DEMO_LOGIN_V2, filter.demoAccountLogin()),
                buildFilter(DEMO_ACCOUNT_MANAGER_ID_V2, filter.demoAccountManagerLdapId()),
                buildFilter(DEMO_ACCOUNT_MANAGER_FIRST_NAME_V2, filter.demoAccountManagerFirstName()),
                buildFilter(DEMO_ACCOUNT_MANAGER_LAST_NAME_V2, filter.demoAccountManagerLastName()),
                buildFilter(DEMO_AFFILIATE_MANAGER_ID_V2, filter.demoAccountAffiliateManagerLdapId()),
                buildFilter(DEMO_AFFILIATE_MANAGER_FIRST_NAME_V2, filter.demoAccountAffiliateManagerFirstName()),
                buildFilter(DEMO_AFFILIATE_MANAGER_LAST_NAME_V2, filter.demoAccountAffiliateManagerLastName()),
                buildFilter(DEMO_AFFILIATE_ID_V2, filter.demoAccountAffiliateId()),
                buildFilter(DEMO_AFFILIATE_FIRST_NAME_V2, filter.demoAccountAffiliateFirstName()),
                buildFilter(DEMO_AFFILIATE_LAST_NAME_V2, filter.demoAccountAffiliateLastName()),
                buildFilter(WEBINAR_REGISTRATION_DATE_V2, filter.webinarAccountRegistrationDate()),
                buildFilter(WEBINAR_FIRST_NAME_V2, filter.webinarAccountFirstName()),
                buildFilter(WEBINAR_LAST_NAME_V2, filter.webinarAccountLastName()),
                buildFilter(WEBINAR_COUNTRY_CODE_V2, filter.webinarAccountCountryCode()),
                buildFilter(WEBINAR_COUNTRY_NAME_V2, filter.webinarAccountCountryName()),
                buildFilter(WEBINAR_CITY_V2, filter.webinarAccountCity()),
                buildFilter(WEBINAR_PHONE_NUMBER_V2, filter.webinarAccountPhoneNumber()),
                buildFilter(WEBINAR_ACCOUNT_MANAGER_ID_V2, filter.webinarAccountManagerLdapId()),
                buildFilter(WEBINAR_ACCOUNT_MANAGER_FIRST_NAME_V2, filter.webinarAccountManagerFirstName()),
                buildFilter(WEBINAR_ACCOUNT_MANAGER_LAST_NAME_V2, filter.webinarAccountManagerLastName()),
                buildFilter(WEBINAR_AFFILIATE_MANAGER_ID_V2, filter.webinarAccountAffiliateManagerLdapId()),
                buildFilter(WEBINAR_AFFILIATE_MANAGER_FIRST_NAME_V2, filter.webinarAccountAffiliateManagerFirstName()),
                buildFilter(WEBINAR_AFFILIATE_MANAGER_LAST_NAME_V2, filter.webinarAccountAffiliateManagerLastName()),
                buildFilter(WEBINAR_AFFILIATE_ID_V2, filter.webinarAccountAffiliateId()),
                buildFilter(WEBINAR_AFFILIATE_FIRST_NAME_V2, filter.webinarAccountAffiliateFirstName()),
                buildFilter(WEBINAR_AFFILIATE_LAST_NAME_V2, filter.webinarAccountAffiliateLastName()),
                buildFilter(EDUCATION_ACCOUNT_REGISTRATION_DATE_V2, filter.educationAccountRegistrationDate()),
                buildFilter(EDUCATION_ACCOUNT_FIRST_NAME_V2, filter.educationAccountFirstName()),
                buildFilter(EDUCATION_ACCOUNT_LAST_NAME_V2, filter.educationAccountLastName()),
                buildFilter(EDUCATION_ACCOUNT_COUNTRY_CODE_V2, filter.educationAccountCountryCode()),
                buildFilter(EDUCATION_ACCOUNT_COUNTRY_NAME_V2, filter.educationAccountCountryName()),
                buildFilter(EDUCATION_ACCOUNT_PHONE_NUMBER_V2, filter.educationAccountPhoneNumber()),
                buildFilter(EDUCATION_ACCOUNT_ACCEPTED_CONTACT_V2, filter.educationAccountAcceptedContact()),
                buildFilter(EDUCATION_ACCOUNT_MANAGER_ID_V2, filter.educationAccountManagerLdapId()),
                buildFilter(EDUCATION_ACCOUNT_MANAGER_FIRST_NAME_V2, filter.educationAccountManagerFirstName()),
                buildFilter(EDUCATION_ACCOUNT_MANAGER_LAST_NAME_V2, filter.educationAccountManagerLastName()),
                buildFilter(WALLET_ACCOUNT_LOGIN_V2, filter.walletAccountLogin()),
                buildFilter(WALLET_ACCOUNT_AFFILIATE_ID_V2, filter.walletAccountAffiliateId()),
                buildFilter(WALLET_ACCOUNT_REGISTRATION_DATE_V2, filter.walletAccountRegistrationDate()),
                buildFilter(WALLET_ACCOUNT_MANAGER_ID_V2, filter.walletAccountManagerLdapId()),
                buildFilter(WALLET_ACCOUNT_MANAGER_FIRST_NAME_V2, filter.walletAccountManagerFirstName()),
                buildFilter(WALLET_ACCOUNT_MANAGER_LAST_NAME_V2, filter.walletAccountManagerLastName()),
                buildFilter(FIRST_STEP_REGISTRATION_DATE_V2, filter.firstStepRegistrationDate()),
                buildFilter(FIRST_STEP_FIRST_NAME_V2, filter.firstStepFirstName()),
                buildFilter(FIRST_STEP_LAST_NAME_V2, filter.firstStepLastName()),
                buildFilter(FIRST_STEP_COUNTRY_CODE_V2, filter.firstStepCountryCode()),
                buildFilter(FIRST_STEP_COUNTRY_NAME_V2, filter.firstStepCountryName()),
                buildFilter(FIRST_STEP_PHONE_NUMBER_V2, filter.firstStepPhoneNumber()),
                buildFilter(FIRST_STEP_ACCOUNT_MANAGER_ID_V2, filter.firstStepAccountManagerLdapId()),
                buildFilter(FIRST_STEP_ACCOUNT_MANAGER_FIRST_NAME_V2, filter.firstStepAccountManagerFirstName()),
                buildFilter(FIRST_STEP_ACCOUNT_MANAGER_LAST_NAME_V2, filter.firstStepAccountManagerLastName()),
                buildFilter(FIRST_STEP_AFFILIATE_MANAGER_ID_V2, filter.firstStepAffiliateManagerLdapId()),
                buildFilter(FIRST_STEP_AFFILIATE_MANAGER_FIRST_NAME_V2, filter.firstStepAffiliateManagerFirstName()),
                buildFilter(FIRST_STEP_AFFILIATE_MANAGER_LAST_NAME_V2, filter.firstStepAffiliateManagerLastName()),
                buildFilter(FIRST_STEP_AFFILIATE_ID_V2, filter.firstStepAffiliateId()),
                buildFilter(FIRST_STEP_AFFILIATE_FIRST_NAME_V2, filter.firstStepAffiliateFirstName()),
                buildFilter(FIRST_STEP_AFFILIATE_LAST_NAME_V2, filter.firstStepAffiliateLastName()),
                buildFilter(SECOND_STEP_REGISTRATION_DATE_V2, filter.secondStepRegistrationDate()),
                buildFilter(SECOND_STEP_FIRST_NAME_V2, filter.secondStepFirstName()),
                buildFilter(SECOND_STEP_LAST_NAME_V2, filter.secondStepLastName()),
                buildFilter(SECOND_STEP_COUNTRY_CODE_V2, filter.secondStepCountryCode()),
                buildFilter(SECOND_STEP_COUNTRY_NAME_V2, filter.secondStepCountryName()),
                buildFilter(SECOND_STEP_CITY_V2, filter.secondStepCity()),
                buildFilter(SECOND_STEP_DATE_OF_BIRTH_V2, filter.secondStepDateOfBirth()),
                buildFilter(SECOND_STEP_PHONE_NUMBER_V2, filter.secondStepPhoneNumber()),
                buildFilter(SECOND_STEP_ACCOUNT_MANAGER_ID_V2, filter.secondStepAccountManagerLdapId()),
                buildFilter(SECOND_STEP_ACCOUNT_MANAGER_FIRST_NAME_V2, filter.secondStepAccountManagerFirstName()),
                buildFilter(SECOND_STEP_ACCOUNT_MANAGER_LAST_NAME_V2, filter.secondStepAccountManagerLastName()),
                buildFilter(SECOND_STEP_AFFILIATE_MANAGER_ID_V2, filter.secondStepAffiliateManagerLdapId()),
                buildFilter(SECOND_STEP_AFFILIATE_MANAGER_FIRST_NAME_V2, filter.secondStepAffiliateManagerFirstName()),
                buildFilter(SECOND_STEP_AFFILIATE_MANAGER_LAST_NAME_V2, filter.secondStepAffiliateManagerLastName()),
                buildFilter(SECOND_STEP_AFFILIATE_ID_V2, filter.secondStepAffiliateId()),
                buildFilter(SECOND_STEP_AFFILIATE_FIRST_NAME_V2, filter.secondStepAffiliateFirstName()),
                buildFilter(SECOND_STEP_AFFILIATE_LAST_NAME_V2, filter.secondStepAffiliateLastName())
            )
            .filter(Objects::nonNull)
            .toList();
    }

    private FieldQuery buildFilter(final Field field, final FilterInput filterInput) {
        if (filterInput == null) {
            return null;
        }
        if (filterInput instanceof BooleanFilterInput filter) {
            return buildBooleanFilter(field, filter);
        }
        if (filterInput instanceof StringFilterInput filter) {
            return buildStringFilter(field, filter);
        }
        if (filterInput instanceof ComparableFilterInput<?> filter) {
            return buildComparableFilter(field, filter);
        }
        throw new IllegalStateException();
    }

    private FieldQuery buildBooleanFilter(final Field field, final BooleanFilterInput filter) {
        BoolQuery.Builder builder = QueryBuilders.bool();

        acceptIfPresent(filter.value(), value -> builder.filter(
            QueryBuilders.term(q -> q.field(field.getName()).value(value))));

        acceptIfPresent(filter.exists(), value -> applyFieldExistsQuery(field, builder, value));

        return new FieldQuery(field, builder.build()._toQuery());
    }

    private FieldQuery buildStringFilter(final Field field, final StringFilterInput filter) {
        BoolQuery.Builder builder = QueryBuilders.bool();

        acceptIfPresent(filter.eq(),
            value -> builder.filter(
                QueryBuilders.term(q -> q.field(field.getName()).value(value).caseInsensitive(true))));

        acceptIfPresent(filter.neq(),
            value -> builder.mustNot(
                QueryBuilders.term(q -> q.field(field.getName()).value(value).caseInsensitive(true))));

        acceptIfPresent(filter.in(),
            value -> builder.filter(termsQueryBuilder(field, value).build()._toQuery()));

        acceptIfPresent(filter.nin(),
            value -> builder.mustNot(termsQueryBuilder(field, value).build()._toQuery()));

        acceptIfPresent(filter.like(),
            value -> builder.filter(
                QueryBuilders.regexp(q -> q.field(field.getName()).value(value).caseInsensitive(true))));

        acceptIfPresent(filter.nlike(),
            value -> builder.mustNot(
                QueryBuilders.regexp(q -> q.field(field.getName()).value(value).caseInsensitive(true))));

        acceptIfPresent(filter.exists(), value -> applyFieldExistsQuery(field, builder, value));

        return new FieldQuery(field, builder.build()._toQuery());
    }

    private FieldQuery buildComparableFilter(final Field field, final ComparableFilterInput<?> filter) {
        BoolQuery.Builder builder = QueryBuilders.bool();

        acceptIfPresent(filter.eq(),
            value -> builder.filter(
                QueryBuilders.term(q -> q.field(field.getName()).value(value.toString()))));

        acceptIfPresent(filter.neq(),
            value -> builder.mustNot(
                QueryBuilders.term(q -> q.field(field.getName()).value(value.toString()))));

        acceptIfPresent(filter.gt(),
            value -> builder.filter(
                QueryBuilders.range(q ->
                    q.untyped(u -> u.field(field.getName()).gt(JsonData.of(value, elasticJsonpMapper))))));

        acceptIfPresent(filter.gte(),
            value -> builder.filter(
                QueryBuilders.range(q ->
                    q.untyped(u -> u.field(field.getName()).gte(JsonData.of(value, elasticJsonpMapper))))));

        acceptIfPresent(filter.lt(),
            value -> builder.filter(
                QueryBuilders.range(q ->
                    q.untyped(u -> u.field(field.getName()).lt(JsonData.of(value, elasticJsonpMapper))))));

        acceptIfPresent(filter.lte(),
            value -> builder.filter(
                QueryBuilders.range(q ->
                    q.untyped(u -> u.field(field.getName()).lte(JsonData.of(value, elasticJsonpMapper))))));

        acceptIfPresent(filter.in(),
            value -> builder.filter(termsQueryBuilder(field, value).build()._toQuery()));

        acceptIfPresent(filter.nin(),
            value -> builder.mustNot(termsQueryBuilder(field, value).build()._toQuery()));

        acceptIfPresent(filter.exists(),
            value -> applyFieldExistsQuery(field, builder, value));

        return new FieldQuery(field, builder.build()._toQuery());
    }

    private static FieldQuery buildPrecalculatedFilter(final BooleanFilterInput filterInput,
                                                       final Field targetField,
                                                       final Field precalculatedField) {

        final BoolQuery.Builder builder = QueryBuilders.bool();

        if (filterInput == null) {
            return null;
        }

        if (filterInput.value() != null) {
            applyFieldExistsQuery(targetField, builder, filterInput.value());
        }

        if (filterInput.exists() != null) {
            applyFieldExistsQuery(targetField, builder, filterInput.exists());
        }

        return new FieldQuery(precalculatedField, builder.build()._toQuery());
    }

    private TermsQuery.Builder termsQueryBuilder(final Field field, final List<?> values) {
        final List<FieldValue> terms = values.stream()
            .map(value -> FieldValue.of(JsonData.of(value, elasticJsonpMapper)))
            .toList();

        return QueryBuilders.terms()
            .field(field.getName())
            .terms(TermsQueryField.of(builder -> builder.value(terms)));
    }

    private static void applyFieldExistsQuery(final Field field,
                                              final BoolQuery.Builder builder,
                                              final Boolean exists) {

        final var existQuery = QueryBuilders.exists(q -> q.field(field.getName()));
        if (Boolean.TRUE.equals(exists)) {
            builder.filter(existQuery);
        } else {
            builder.mustNot(existQuery);
        }
    }

    private static ExistsQuery profileComplete() {
        return QueryBuilders.exists().field(EMAIL_V2.getName()).build();
    }

    private TermsQuery profileStatus(final List<ProfileStatus> profileStatus) {
        return termsQueryBuilder(PROFILE_STATUS_V2, profileStatus).build();
    }

    @Override
    protected Sort buildSort(final SearchProfilesInput input) {
        return Sort.by(input.sort()
                .stream()
                .map(this::getSortingField)
                .toList())
            .and(getSortTieBreaker());
    }

    private Sort getSortTieBreaker() {
        return Sort.by(buildSortOrder(EMAIL_V2, SortDirection.ASC));
    }

    private Sort.Order getSortingField(final SearchProfilesSortingInput sort) {
        return Stream.of(
                buildSortOrder(PROFILE_ID_V2, sort.profileId()),
                buildSortOrder(BRAND_ACCOUNT_ID_V2, sort.brandAccountId()),
                buildSortOrder(REGISTRATION_DATE_V2, sort.registrationDate()),
                buildSortOrder(LATEST_LEAD_REGISTRATION_DATE_V2, sort.latestLeadRegistrationDate()),
                buildSortOrder(LATEST_COMMENT_ID_V2, sort.latestLeadComment()),
                buildSortOrder(IS_VALIDATED_V2, sort.validationStatus()),
                buildSortOrder(VALIDATION_DATE_V2, sort.validationDate()),
                buildSortOrder(COUNTRY_CODE_V2, sort.countryCode()),
                buildSortOrder(COUNTRY_NAME_V2, sort.countryName()),
                buildSortOrder(BRAND_ID_V2, sort.brandId()),
                buildSortOrder(BRAND_NAME_V2, sort.brandName()),
                buildSortOrder(DATE_OF_BIRTH_V2, sort.dateOfBirth()),
                buildSortOrder(COMMENT_DATE_ADDED_V2, sort.latestCommentAdded()),
                buildSortOrder(FIRST_DEPOSIT_DATE_V2, sort.firstDepositDate()),
                buildSortOrder(SAT_V2, sort.sat()),
                buildSortOrder(LATEST_VALID_PHONE_CALL_DATE_V2, sort.latestValidPhoneCallDate()),
                buildSortOrder(LATEST_INVALID_PHONE_CALL_DATE_V2, sort.latestInvalidPhoneCallDate()),
                buildSortOrder(TOTAL_INVALID_PHONE_CALLS_V2, sort.totalInvalidPhoneCalls()),
                buildSortOrder(LATEST_PHONE_CALL_ATTEMPT_DATE_V2, sort.latestPhoneCallAttemptDate()),
                buildSortOrder(TOTAL_PHONE_CALL_ATTEMPTS_V2, sort.totalPhoneCallAttempts()),
                buildSortOrder(TOTAL_VALID_PHONE_CALLS_BEFORE_ACTIVATION_V2, sort.totalValidBeforeClientActivation()),
                buildSortOrder(TOTAL_INVALID_PHONE_CALLS_BEFORE_ACTIVATION_V2,
                    sort.totalInvalidBeforeClientActivation()),
                buildSortOrder(TOTAL_ATTEMPTS_BEFORE_ACTIVATION_V2, sort.totalAttemptsBeforeClientActivation()),
                buildSortOrder(TOTAL_OTHER_PHONE_CALLS_V2, sort.totalOtherCommunicatedPhoneCalls()),
                buildSortOrder(LATEST_TRADE_DATE_V2, sort.latestTradeDate()),
                buildSortOrder(DEPOSIT_COUNT_V2, sort.depositCount()),
                buildSortOrder(LATEST_DEPOSIT_DATE_V2, sort.latestDepositDate()),
                buildSortOrder(TOTAL_DEPOSITS_EUR_V2, sort.totalDepositsEur()),
                buildSortOrder(TOTAL_WITHDRAWALS_EUR_V2, sort.totalWithdrawalsEur()),
                buildSortOrder(TOTAL_BALANCE_EUR_V2, sort.totalBalanceEur()),
                buildSortOrder(TOTAL_CREDIT_EUR_V2, sort.totalCreditEur()),
                buildSortOrder(TOTAL_EQUITY_EUR_V2, sort.totalEquityEur()),
                buildSortOrder(TOTAL_PNL_EUR_V2, sort.totalPnlEur()),
                buildSortOrder(LATEST_MONTH_PNL_EUR_V2, sort.latestMonthPnlEur()),
                buildSortOrder(DEMO_REGISTRATION_DATE_V2, sort.demoAccountRegistrationDate()),
                buildSortOrder(DEMO_COUNTRY_CODE_V2, sort.demoAccountCountryCode()),
                buildSortOrder(DEMO_COUNTRY_NAME_V2, sort.demoAccountCountryName()),
                buildSortOrder(WEBINAR_REGISTRATION_DATE_V2, sort.webinarAccountRegistrationDate()),
                buildSortOrder(WEBINAR_COUNTRY_CODE_V2, sort.webinarAccountCountryCode()),
                buildSortOrder(WEBINAR_COUNTRY_NAME_V2, sort.webinarAccountCountryName()),
                buildSortOrder(EDUCATION_ACCOUNT_REGISTRATION_DATE_V2, sort.educationAccountRegistrationDate()),
                buildSortOrder(EDUCATION_ACCOUNT_COUNTRY_CODE_V2, sort.educationAccountCountryCode()),
                buildSortOrder(EDUCATION_ACCOUNT_COUNTRY_NAME_V2, sort.educationAccountCountryName()),
                buildSortOrder(WALLET_ACCOUNT_REGISTRATION_DATE_V2, sort.walletAccountRegistrationDate())
            )
            .filter(Objects::nonNull)
            .findFirst()
            .orElseGet(() -> Sort.Order.desc(ProfileDocumentMapping.BRAND_ACCOUNT_ID.getName()));
    }

    private Sort.Order buildSortOrder(final Field field, final SortDirection sortDirection) {
        if (sortDirection == null) {
            return null;
        }

        Order order = new Order(Sort.Direction.fromString(sortDirection.name()), field.getName());
        String path = field.getPath();
        return path != null ? order.withNested(Order.Nested.builder(path).build()) : order;
    }

    private record FieldQuery(Field field,
                              co.elastic.clients.elasticsearch._types.query_dsl.Query query) {
        boolean isNested() {
            return field.getPath() != null;
        }

        String nestedPath() {
            return field.getPath();
        }
    }

    private record BuilderContext(Set<FieldQuery> nestedQueries, BoolQuery.Builder builder) {
    }

    private enum Operator {
        AND, OR, NOT
    }
}
