package com.xm.dal.repository.elastic.entity;

import java.time.OffsetDateTime;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Builder(toBuilder = true)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@AllArgsConstructor
@Getter
@Setter
public class WalletAccount {
    Long login;
    Brand brand;
    String currency;
    OffsetDateTime registrationDate;
    String registrationIp;
    Country registrationIpCountry;
    AccountStatus accountStatus;
    Boolean isEnabled;
    Boolean isReadOnly;
    Boolean sendReports;
    Boolean promoAgreed;
    OffsetDateTime archivedDate;
    String source;
    ServerInfo serverInfo;
    AccountManager accountManager;
    Long affiliateId;
    String comment;
    String groupName;
}
