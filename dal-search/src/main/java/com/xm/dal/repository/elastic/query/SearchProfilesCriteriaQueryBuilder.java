package com.xm.dal.repository.elastic.query;

import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.ACCOUNT_MANAGER_FIRST_NAME;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.ACCOUNT_MANAGER_ID;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.ACCOUNT_MANAGER_LAST_NAME;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.ACCOUNT_REGISTRATION_DATE;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.ACCOUNT_STATUS;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.ACCOUNT_TYPE_ID;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.AFFILIATE_FIRST_NAME;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.AFFILIATE_ID;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.AFFILIATE_LAST_NAME;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.AFFILIATE_MANAGER_FIRST_NAME;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.AFFILIATE_MANAGER_ID;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.AFFILIATE_MANAGER_LAST_NAME;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.BRAND_ACCOUNT_ID;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.BRAND_ID;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.BRAND_NAME;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.CALLBACK_REQUEST_STATUS;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.CITY;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.COMMENT;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.COMMENT_DATE_ADDED;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.COUNTRY_CODE;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.COUNTRY_NAME;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.CURRENCY;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.DATE_OF_BIRTH;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.DEPOSIT_COUNT;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.DOCUMENT_EXPIRATION_DATE;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.EMAIL;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.FIRST_DEPOSIT_DATE;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.FIRST_NAME;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.GID;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.GROUP_NAME;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.HAS_ACTIVE_TRADING_ACCOUNTS;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.INTERNATIONAL_PHONE_NUMBER;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.IS_ISLAMIC;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.IS_PROFESSIONAL_CLIENT;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.IS_RAF_QUALIFIED;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.IS_REFERRED_CLIENT;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.IS_REFERRER;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.IS_REVALIDATION_REQUESTED;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.IS_VALIDATED;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.LABEL_ID;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.LAST_NAME;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.LATEST_DEPOSIT_DATE;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.LATEST_EMAIL_DATE;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.LATEST_INVALID_PHONE_CALL_DATE;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.LATEST_MONTH_PNL_EUR;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.LATEST_PHONE_CALL_ATTEMPT_DATE;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.LATEST_TRADE_DATE;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.LATEST_VALID_PHONE_CALL_DATE;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.LEVERAGE;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.LOGIN;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.MEMBERS_AREA_LAST_LOGIN;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.MOBILE_LAST_LOGIN;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.MQ_TERMINAL_LAST_LOGIN;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.PHONE_NUMBER;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.PLATFORM_TYPE;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.PREFERRED_LANGUAGE_CODE;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.PROFILE_ID;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.PROMO_AGREED;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.RAF_CONNECTION_STATUS_ID;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.RATING_ID;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.REFERRER_TOKEN;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.REGISTRATION_DATE;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.REGISTRATION_STATUS;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.SAT;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.SOCIAL_TRADING_STATUS;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.TOTAL_ATTEMPTS_BEFORE_ACTIVATION;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.TOTAL_BALANCE_EUR;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.TOTAL_CREDIT_EUR;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.TOTAL_DEPOSITS_EUR;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.TOTAL_EQUITY_EUR;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.TOTAL_INVALID_PHONE_CALLS;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.TOTAL_INVALID_PHONE_CALLS_BEFORE_ACTIVATION;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.TOTAL_OTHER_PHONE_CALLS;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.TOTAL_PHONE_CALL_ATTEMPTS;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.TOTAL_PNL_EUR;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.TOTAL_VALID_PHONE_CALLS_BEFORE_ACTIVATION;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.TOTAL_WITHDRAWALS_EUR;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.VALIDATION_DATE;
import static com.xm.dal.repository.elastic.query.ProfileDocumentMapping.WEB_TRADER_LAST_LOGIN;
import static com.xm.dal.util.Nullable.acceptIfPresent;

import com.xm.dal.api.input.BooleanFilterInput;
import com.xm.dal.api.input.ComparableFilterInput;
import com.xm.dal.api.input.FilterInput;
import com.xm.dal.api.input.SortDirection;
import com.xm.dal.api.input.StringFilterInput;
import com.xm.dal.api.input.search.SearchProfilesFilterInput;
import com.xm.dal.api.input.search.SearchProfilesInput;
import com.xm.dal.api.input.search.SearchProfilesSortingInput;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.BinaryOperator;
import java.util.stream.Stream;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.annotations.FieldType;
import org.springframework.data.elasticsearch.core.query.Criteria;
import org.springframework.data.elasticsearch.core.query.CriteriaQuery;
import org.springframework.data.elasticsearch.core.query.Field;
import org.springframework.data.elasticsearch.core.query.Order;
import org.springframework.data.elasticsearch.core.query.Query;
import org.springframework.stereotype.Component;

@Component
class SearchProfilesCriteriaQueryBuilder extends AbstractSearchProfilesQueryBuilder {

    public SearchProfilesCriteriaQueryBuilder(final QueryOffsetUtils queryOffsetUtils) {
        super(queryOffsetUtils);
    }

    @Override
    protected Query buildQuery(final SearchProfilesInput input) {
        return new CriteriaQuery(
            profileComplete()
                .subCriteria(buildCriteria(input.filter())));
    }

    private Criteria buildCriteria(final SearchProfilesFilterInput filter) {
        final Criteria criteria = buildFieldCriteria(filter)
            .reduce(null, combineCriteria());

        List<Criteria> subCriteria = new ArrayList<>();
        acceptIfPresent(filter.and(), andFilters ->
            subCriteria.add(andFilters.stream()
                .map(this::buildCriteria)
                .reduce(null, combineCriteria())));

        acceptIfPresent(filter.or(), orFilters ->
            subCriteria.add(orFilters.stream()
                .map(this::buildCriteria)
                .reduce(Criteria.or(), combineCriteria())));

        acceptIfPresent(filter.not(), notFilter ->
            subCriteria.add(new Criteria().not().subCriteria(buildCriteria(notFilter))));

        return subCriteria.stream()
            .reduce(criteria, combineCriteria());
    }

    protected Stream<Criteria> buildFieldCriteria(final SearchProfilesFilterInput filter) {
        return Stream.of(
                buildFilter(PROFILE_ID, filter.profileId()),
                buildFilter(BRAND_ACCOUNT_ID, filter.brandAccountId()),
                buildFilter(FIRST_NAME, filter.firstName()),
                buildFilter(LAST_NAME, filter.lastName()),
                buildFilter(BRAND_ID, filter.brandId()),
                buildFilter(BRAND_NAME, filter.brandName()),
                buildFilter(EMAIL, filter.email()),
                buildFilter(COUNTRY_CODE, filter.countryCode()),
                buildFilter(COUNTRY_NAME, filter.countryName()),
                buildFilter(PHONE_NUMBER, filter.phoneNumber()),
                buildFilter(DATE_OF_BIRTH, filter.dateOfBirth()),
                buildFilter(CITY, filter.city()),
                buildFilter(REGISTRATION_DATE, filter.registrationDate()),
                buildFilter(REGISTRATION_STATUS, filter.registrationStatus()),
                buildFilter(INTERNATIONAL_PHONE_NUMBER, filter.internationalPhoneNumber()),
                buildFilter(IS_PROFESSIONAL_CLIENT, filter.isProfessionalClient()),
                buildFilter(PREFERRED_LANGUAGE_CODE, filter.preferredLanguage()),
                buildFilter(ACCOUNT_MANAGER_ID, filter.accountManagerLdapId()),
                buildFilter(ACCOUNT_MANAGER_FIRST_NAME, filter.accountManagerFirstName()),
                buildFilter(ACCOUNT_MANAGER_LAST_NAME, filter.accountManagerLastName()),
                buildFilter(AFFILIATE_MANAGER_ID, filter.affiliateManagerLdapId()),
                buildFilter(AFFILIATE_MANAGER_FIRST_NAME, filter.affiliateManagerFirstName()),
                buildFilter(AFFILIATE_MANAGER_LAST_NAME, filter.affiliateManagerLastName()),
                buildFilter(AFFILIATE_ID, filter.affiliateId()),
                buildFilter(AFFILIATE_FIRST_NAME, filter.affiliateFirstName()),
                buildFilter(AFFILIATE_LAST_NAME, filter.affiliateLastName()),
                buildFilter(IS_VALIDATED, filter.validationStatus()),
                buildFilter(IS_REVALIDATION_REQUESTED, filter.isRevalidationRequested()),
                buildFilter(DOCUMENT_EXPIRATION_DATE, filter.documentExpirationDate()),
                buildFilter(VALIDATION_DATE, filter.validationDate()),
                buildFilter(RATING_ID, filter.ratingOptionId()),
                buildFilter(COMMENT_DATE_ADDED, filter.latestCommentAdded()),
                buildFilter(LOGIN, filter.login()),
                buildFilter(ACCOUNT_TYPE_ID, filter.accountTypeId()),
                buildFilter(GROUP_NAME, filter.groupName()),
                buildFilter(HAS_ACTIVE_TRADING_ACCOUNTS, filter.hasActiveTradingAccounts()),
                buildFilter(IS_ISLAMIC, filter.isIslamic()),
                buildFilter(LEVERAGE, filter.leverage()),
                buildFilter(MEMBERS_AREA_LAST_LOGIN, filter.membersAreaLastLogin()),
                buildFilter(MOBILE_LAST_LOGIN, filter.mobileLastLogin()),
                buildFilter(MQ_TERMINAL_LAST_LOGIN, filter.mqTerminalLastLogin()),
                buildFilter(WEB_TRADER_LAST_LOGIN, filter.webTraderLastLogin()),
                buildFilter(GID, filter.gid()),
                buildFilter(ACCOUNT_REGISTRATION_DATE, filter.tradingAccountRegistrationDate()),
                buildFilter(SOCIAL_TRADING_STATUS, filter.socialTradingStatus()),
                buildFilter(COMMENT, filter.userComment()),
                buildFilter(CURRENCY, filter.currency()),
                buildFilter(ACCOUNT_STATUS, filter.accountStatus()),
                buildFilter(PLATFORM_TYPE, filter.platformType()),
                buildFilter(LATEST_TRADE_DATE, filter.latestTradeDate()),
                buildFilter(DEPOSIT_COUNT, filter.depositCount()),
                buildFilter(FIRST_DEPOSIT_DATE, filter.firstDepositDate()),
                buildFilter(LATEST_DEPOSIT_DATE, filter.latestDepositDate()),
                buildFilter(LATEST_MONTH_PNL_EUR, filter.latestMonthPnlEur()),
                buildFilter(TOTAL_BALANCE_EUR, filter.totalBalanceEur()),
                buildFilter(TOTAL_CREDIT_EUR, filter.totalCreditEur()),
                buildFilter(TOTAL_DEPOSITS_EUR, filter.totalDepositsEur()),
                buildFilter(TOTAL_EQUITY_EUR, filter.totalEquityEur()),
                buildFilter(TOTAL_PNL_EUR, filter.totalPnlEur()),
                buildFilter(TOTAL_WITHDRAWALS_EUR, filter.totalWithdrawalsEur()),
                buildFilter(IS_REFERRER, filter.isReferrer()),
                buildFilter(IS_REFERRED_CLIENT, filter.isReferredClient()),
                buildFilter(IS_RAF_QUALIFIED, filter.isRafQualified()),
                buildFilter(RAF_CONNECTION_STATUS_ID, filter.rafConnectionStatusId()),
                buildFilter(REFERRER_TOKEN, filter.referrerToken()),
                buildFilter(LATEST_VALID_PHONE_CALL_DATE, filter.latestValidPhoneCallDate()),
                buildFilter(LATEST_INVALID_PHONE_CALL_DATE, filter.latestInvalidPhoneCallDate()),
                buildFilter(TOTAL_INVALID_PHONE_CALLS, filter.totalInvalidPhoneCalls()),
                buildFilter(LATEST_PHONE_CALL_ATTEMPT_DATE, filter.latestPhoneCallAttemptDate()),
                buildFilter(TOTAL_PHONE_CALL_ATTEMPTS, filter.totalPhoneCallAttempts()),
                buildFilter(TOTAL_VALID_PHONE_CALLS_BEFORE_ACTIVATION, filter.totalValidBeforeClientActivation()),
                buildFilter(TOTAL_INVALID_PHONE_CALLS_BEFORE_ACTIVATION, filter.totalInvalidBeforeClientActivation()),
                buildFilter(TOTAL_ATTEMPTS_BEFORE_ACTIVATION, filter.totalAttemptsBeforeClientActivation()),
                buildFilter(TOTAL_OTHER_PHONE_CALLS, filter.totalOtherCommunicatedPhoneCalls()),
                buildFilter(LABEL_ID, filter.labelId()),
                buildFilter(LATEST_EMAIL_DATE, filter.latestEmailDate()),
                buildFilter(SAT, filter.sat()),
                buildFilter(PROMO_AGREED, filter.promoAgreed()),
                buildFilter(CALLBACK_REQUEST_STATUS, filter.callbackRequestStatus())
            )
            .filter(Objects::nonNull);
    }

    private Criteria buildFilter(final Field field, final FilterInput filterInput) {
        if (filterInput == null) {
            return null;
        }
        if (filterInput instanceof BooleanFilterInput filter) {
            return buildBooleanFilter(field, filter);
        }
        if (filterInput instanceof StringFilterInput filter) {
            return buildStringFilter(field, filter);
        }
        if (filterInput instanceof ComparableFilterInput<?> filter) {
            return buildComparableFilter(field, filter);
        }
        throw new IllegalStateException();
    }

    private Criteria buildBooleanFilter(final Field field, final BooleanFilterInput filter) {
        Criteria criteria = new Criteria(field.getName());

        acceptIfPresent(filter.value(), criteria::is);
        acceptIfPresent(filter.exists(), value -> applyFieldExistsCriteria(field.getName(), criteria, value));

        return criteria;
    }

    private Criteria buildStringFilter(final Field field, final StringFilterInput filter) {
        Criteria criteria = new Criteria(field.getName());

        acceptIfPresent(filter.eq(), criteria::is);
        acceptIfPresent(filter.neq(), value -> criteria.subCriteria(new Criteria(field.getName()).not().is(value)));
        acceptIfPresent(filter.in(), criteria::in);
        acceptIfPresent(filter.nin(), criteria::notIn);
        acceptIfPresent(filter.like(), value -> criteria.subCriteria(new Criteria(keyword(field)).regexp(value)));
        acceptIfPresent(filter.nlike(),
            value -> criteria.subCriteria(new Criteria(keyword(field)).not().regexp(value)));
        acceptIfPresent(filter.exists(), value -> applyFieldExistsCriteria(field.getName(), criteria, value));

        return criteria;
    }

    private Criteria buildComparableFilter(final Field field, final ComparableFilterInput<?> filter) {
        Criteria criteria = new Criteria(field.getName());

        acceptIfPresent(filter.eq(), value -> criteria.is(value.toString()));
        acceptIfPresent(filter.neq(),
            value -> criteria.subCriteria(new Criteria(field.getName()).not().is(value.toString())));
        acceptIfPresent(filter.gt(), value -> criteria.greaterThan(value.toString()));
        acceptIfPresent(filter.gte(), value -> criteria.greaterThanEqual(value.toString()));
        acceptIfPresent(filter.lt(), value -> criteria.lessThan(value.toString()));
        acceptIfPresent(filter.lte(), value -> criteria.lessThanEqual(value.toString()));
        acceptIfPresent(filter.in(), value -> criteria.in(value.stream().map(Object::toString).toList()));
        acceptIfPresent(filter.nin(), value -> criteria.notIn(value.stream().map(Object::toString).toList()));
        acceptIfPresent(filter.exists(), value -> applyFieldExistsCriteria(field.getName(), criteria, value));

        return criteria;
    }

    private static void applyFieldExistsCriteria(final String key, final Criteria criteria, final Boolean exists) {
        if (Boolean.TRUE.equals(exists)) {
            criteria.exists();
        } else {
            criteria.subCriteria(new Criteria(key).not().exists());
        }
    }

    private static Criteria profileComplete() {
        return new Criteria(keyword(EMAIL)).exists();
    }

    private static BinaryOperator<Criteria> combineCriteria() {
        return (c1, c2) -> {
            if (c1 == null) {
                if (c2.isOr() || c2.isNegating()) {
                    return new Criteria().subCriteria(c2);
                }
                return c2;
            }
            return c1.subCriteria(c2);
        };
    }

    @Override
    protected Sort buildSort(final SearchProfilesInput input) {
        return Sort.by(input.sort()
                .stream()
                .map(this::getSortingField)
                .toList())
            .and(getSortTieBreaker());
    }

    private Sort getSortTieBreaker() {
        return Sort.by(buildSortOrder(EMAIL, SortDirection.ASC));
    }

    private Sort.Order getSortingField(final SearchProfilesSortingInput sort) {
        return Stream.of(
                buildSortOrder(PROFILE_ID, sort.profileId()),
                buildSortOrder(BRAND_ACCOUNT_ID, sort.brandAccountId()),
                buildSortOrder(REGISTRATION_DATE, sort.registrationDate()),
                buildSortOrder(IS_VALIDATED, sort.validationStatus()),
                buildSortOrder(VALIDATION_DATE, sort.validationDate()),
                buildSortOrder(COUNTRY_CODE, sort.countryCode()),
                buildSortOrder(COUNTRY_NAME, sort.countryName()),
                buildSortOrder(BRAND_ID, sort.brandId()),
                buildSortOrder(BRAND_NAME, sort.brandName()),
                buildSortOrder(DATE_OF_BIRTH, sort.dateOfBirth()),
                buildSortOrder(COMMENT_DATE_ADDED, sort.latestCommentAdded()),
                buildSortOrder(FIRST_DEPOSIT_DATE, sort.firstDepositDate()),
                buildSortOrder(SAT, sort.sat()),
                buildSortOrder(LATEST_VALID_PHONE_CALL_DATE, sort.latestValidPhoneCallDate()),
                buildSortOrder(LATEST_INVALID_PHONE_CALL_DATE, sort.latestInvalidPhoneCallDate()),
                buildSortOrder(TOTAL_INVALID_PHONE_CALLS, sort.totalInvalidPhoneCalls()),
                buildSortOrder(LATEST_PHONE_CALL_ATTEMPT_DATE, sort.latestPhoneCallAttemptDate()),
                buildSortOrder(TOTAL_PHONE_CALL_ATTEMPTS, sort.totalPhoneCallAttempts()),
                buildSortOrder(TOTAL_VALID_PHONE_CALLS_BEFORE_ACTIVATION, sort.totalValidBeforeClientActivation()),
                buildSortOrder(TOTAL_INVALID_PHONE_CALLS_BEFORE_ACTIVATION, sort.totalInvalidBeforeClientActivation()),
                buildSortOrder(TOTAL_ATTEMPTS_BEFORE_ACTIVATION, sort.totalAttemptsBeforeClientActivation()),
                buildSortOrder(TOTAL_OTHER_PHONE_CALLS, sort.totalOtherCommunicatedPhoneCalls()),
                buildSortOrder(LATEST_TRADE_DATE, sort.latestTradeDate()),
                buildSortOrder(DEPOSIT_COUNT, sort.depositCount()),
                buildSortOrder(LATEST_DEPOSIT_DATE, sort.latestDepositDate()),
                buildSortOrder(TOTAL_DEPOSITS_EUR, sort.totalDepositsEur()),
                buildSortOrder(TOTAL_WITHDRAWALS_EUR, sort.totalWithdrawalsEur()),
                buildSortOrder(TOTAL_BALANCE_EUR, sort.totalBalanceEur()),
                buildSortOrder(TOTAL_CREDIT_EUR, sort.totalCreditEur()),
                buildSortOrder(TOTAL_EQUITY_EUR, sort.totalEquityEur()),
                buildSortOrder(TOTAL_PNL_EUR, sort.totalPnlEur()),
                buildSortOrder(LATEST_MONTH_PNL_EUR, sort.latestMonthPnlEur())
            )
            .filter(Objects::nonNull)
            .findFirst()
            .orElseGet(() -> Sort.Order.desc(BRAND_ACCOUNT_ID.getName()));
    }

    private Sort.Order buildSortOrder(final Field field, final SortDirection sortDirection) {
        if (sortDirection == null) {
            return null;
        }
        return new Order(Sort.Direction.fromString(sortDirection.name()), keyword(field));
    }

    private static String keyword(final Field field) {
        if (FieldType.Text == field.getFieldType()) {
            return field.getName().concat(".keyword");
        }
        return field.getName();
    }

}
