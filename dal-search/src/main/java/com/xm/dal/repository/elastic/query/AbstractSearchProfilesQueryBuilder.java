package com.xm.dal.repository.elastic.query;

import com.xm.dal.api.input.search.SearchProfilesInput;
import com.xm.dal.exception.graphql.DalError;
import jakarta.validation.ValidationException;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.core.query.FetchSourceFilter;
import org.springframework.data.elasticsearch.core.query.Query;

@RequiredArgsConstructor
public abstract class AbstractSearchProfilesQueryBuilder implements SearchProfilesQueryBuilder {

    private final QueryOffsetUtils queryOffsetUtils;

    @Override
    public Query build(final SearchProfilesInput input, final String... includes) {
        if (!input.isValid()) {
            throw new ValidationException(DalError.Code.INVALID_SEARCH_CRITERIA);
        }

        Query query = buildQuery(input);
        query.addSourceFilter(FetchSourceFilter.of(null, includes, null));
        query.addSort(buildSort(input));
        query.setPageable(Pageable.ofSize(input.limit()));
        query.setSearchAfter(input.offset() != null ? queryOffsetUtils.getSearchAfterValues(input.offset()) : null);
        query.setTrackTotalHits(true);

        return query;
    }

    protected abstract Query buildQuery(SearchProfilesInput input);

    protected abstract Sort buildSort(SearchProfilesInput input);
}
