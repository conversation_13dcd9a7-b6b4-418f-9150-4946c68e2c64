package com.xm.dal.api;

import com.xm.dal.service.EsMcpService;
import io.modelcontextprotocol.spec.McpSchema;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api/mcp")
@RequiredArgsConstructor
public class ProfileSearchNlqController {

    private final EsMcpService esMcpService;

    @GetMapping("/health")
    public ResponseEntity<?> health() {
        log.info("Received MCP health check request");
        try {
            List<McpSchema.Tool> tools = esMcpService.listAvailableTools();
            return ResponseEntity.ok(Map.of(
                "status", "healthy",
                "toolCount", tools.size(),
                "message", "MCP connection is working"
            ));
        } catch (Exception e) {
            log.error("MCP health check failed", e);
            return ResponseEntity.status(503).body(Map.of(
                "status", "unhealthy",
                "error", e.getMessage(),
                "message", "MCP connection failed"
            ));
        }
    }

    @GetMapping("/tools")
    public ResponseEntity<?> listTools() {
        log.info("Received request to list MCP tools");
        try {
            List<McpSchema.Tool> tools = esMcpService.listAvailableTools();
            log.info("Found {} tools", tools.size());
            return ResponseEntity.ok(Map.of(
                "tools", tools,
                "count", tools.size(),
                "toolNames", tools.stream().map(McpSchema.Tool::name).toList()
            ));
        } catch (Exception e) {
            log.error("Error listing MCP tools", e);
            return ResponseEntity.internalServerError().body(Map.of(
                "error", e.getMessage(),
                "type", e.getClass().getSimpleName()
            ));
        }
    }

    @PostMapping("/search")
    public ResponseEntity<Object> search(@RequestParam String index, @RequestBody String query) {
        log.info("Received search request for index: {} with query: {}", index, query);
        try {
            Object result = esMcpService.executeSearch(index, query);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Error executing search via MCP", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/index/{index}/info")
    public ResponseEntity<Object> getIndexInfo(@PathVariable String index) {
        log.info("Received request for index info: {}", index);
        try {
            Object result = esMcpService.getIndexInfo(index);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Error getting index info via MCP", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/indices")
    public ResponseEntity<Object> listIndices() {
        log.info("Received request to list indices");
        try {
            Object result = esMcpService.listIndices();
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Error listing indices via MCP", e);
            return ResponseEntity.internalServerError().build();
        }
    }


    @PostMapping("/execute/{toolName}")
    public ResponseEntity<Object> executeCustomOperation(
        @PathVariable String toolName,
        @RequestBody Map<String, Object> arguments) {
        log.info("Received request to execute custom operation: {} with arguments: {}", toolName, arguments);
        try {
            Object result = esMcpService.executeCustomOperation(toolName, arguments);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Error executing custom operation via MCP", e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
