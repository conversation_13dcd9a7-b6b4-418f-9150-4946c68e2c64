management:
  info:
    git:
      mode: full
  endpoint:
    health:
      probes:
        enabled: true
  endpoints:
    web:
      exposure:
        include: health,info
      base-path: /actuator
  health:
    livenessstate:
      enabled: true
    readinessstate:
      enabled: true

spring:
  ai:
    openai:
      api-key: ${OPENAI_API_KEY:your-api-key-here}
      chat:
        options:
          model: gpt-4o-mini
          temperature: 0.1
    mcp:
      client:
        stdio:
          servers-configuration: classpath:mcp-servers.json
  graphql:
    schema:
      printer:
        enabled: true
    graphiql:
      enabled: true
  elasticsearch:
    uris: localhost:9200
    apikey: apikey
    connection-timeout: 5s
    socket-timeout: 10s
    evolution:
      placeholders:
        index: ${app.elasticsearch-index}
        real-time-index: ${app.elasticsearch-real-time-index}
      history-index: profiles_es_evolution
  kafka:
    bootstrap-servers: localhost:19000
    properties:
      security.protocol: PLAINTEXT
    consumer:
      value-deserializer: org.springframework.kafka.support.serializer.DelegatingByTopicDeserializer
      key-deserializer: org.springframework.kafka.support.serializer.DelegatingByTopicDeserializer
      properties:
        # Delegating deserialization configuration for specific topics
        spring.kafka.value.serialization.bytopic.config: >
          jthales-domain-events*: com.xm.dal.consumer.serde.ProfileEventDeserializer, 
          jthales-crm-metadata-events*: com.xm.dal.consumer.serde.ProfileMetadataEventDeserializer, 
          services.trading_mesh.server_operation.v1.1*: com.xm.dal.consumer.serde.ServerOperationEventDeserializer,
          services.loyalty_program.callback.v1.0*: com.xm.dal.consumer.serde.LoyaltyProgramCallbackEventDeserializer,
          kyc.xm.validation-status-events.v1.0*: com.xm.dal.consumer.serde.ValidationStatusEventDeserializer,
          kyc.xm.validation-identity-events.v1.0*: com.xm.dal.consumer.serde.ValidationDocumentEventDeserializer
        spring.kafka.key.serialization.bytopic.config: >
          jthales-domain-events*: org.apache.kafka.common.serialization.StringDeserializer, 
          jthales-crm-metadata-events*: org.apache.kafka.common.serialization.StringDeserializer, 
          services.trading_mesh.server_operation.v1.1*: com.xm.dal.consumer.serde.UnsignedIntKeyDeserializer,
          services.loyalty_program.callback.v1.0*: org.apache.kafka.common.serialization.StringDeserializer,
          kyc.xm.validation-status-events.v1.0*: org.apache.kafka.common.serialization.StringDeserializer, 
          kyc.xm.validation-identity-events.v1.0*: org.apache.kafka.common.serialization.StringDeserializer
    producer:
      value-serializer: com.xm.dal.consumer.serde.MessageEventSerializer
logging:
  level:
    tracer: INFO

app:
  elasticsearch-index: profiles
  elasticsearch-real-time-index: profiles-rt

client:
  user-id: 4469
  user-ip: ${DAL_SERVICE_HOST:127.0.0.1}
  jthales:
    base-uri: https://test-jthales.api.trading-point.com/jThalesServer/jApi
  jmanager:
    base-uri: https://test-jmansrv.api.trading-point.com/jManagerServer/jApi

consumer:
  enabled: true
  kafka:
    profile:
      topic: jthales-domain-events
      group-id: dal-profile-consumer-group
      auto-startup: false
    profile-metadata:
      topic: jthales-crm-metadata-events
      group-id: dal-profile-metadata-consumer-group
      auto-startup: false
    server-operation:
      topic: services.trading_mesh.server_operation.v1.1
      group-id: dal-trading_mesh-server_operation-consumer-group
      auto-startup: false
    callback-request:
      topic: services.loyalty_program.callback.v1.0
      group-id: dal-loyalty_program.callback-consumer-group
      auto-startup: false
      properties:
        auto.offset.reset: earliest
    validation-status:
      topic: kyc.xm.validation-status-events.v1.0
      group-id: dal-kyc-validation-status-consumer-group
      auto-startup: false
    validation-document:
      topic: kyc.xm.validation-identity-events.v1.0
      group-id: dal-kyc-validation-document-consumer-group
      auto-startup: false
    dlt:
      suffix: -dal-DLT
      backoff:
        initial-interval: 2000ms
        max-interval: 30000ms
        max-retries: 4
        multiplier: 1.5