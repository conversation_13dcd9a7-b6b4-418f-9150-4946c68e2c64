package com.xm.dal.repository.elastic.converter;

import com.xm.dal.domain.Country;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-04T17:52:36+0300",
    comments = "version: 1.6.3, compiler: javac, environment: Java 17.0.13 (Eclipse Adoptium)"
)
@Component
public class CountryConverterImpl implements CountryConverter {

    @Override
    public com.xm.dal.repository.elastic.entity.Country convert(Country country) {
        if ( country == null ) {
            return null;
        }

        com.xm.dal.repository.elastic.entity.Country.CountryBuilder country1 = com.xm.dal.repository.elastic.entity.Country.builder();

        country1.code( country.code() );
        country1.name( country.name() );

        return country1.build();
    }
}
