package com.xm.dal.repository.elastic.converter;

import com.xm.dal.domain.Address;
import com.xm.dal.domain.AddressType;
import com.xm.dal.domain.Brand;
import com.xm.dal.domain.CallbackRequestStatus;
import com.xm.dal.domain.Comment;
import com.xm.dal.domain.Country;
import com.xm.dal.domain.Label;
import com.xm.dal.domain.Language;
import com.xm.dal.domain.LeadPhone;
import com.xm.dal.domain.Name;
import com.xm.dal.domain.Phone;
import com.xm.dal.domain.PhoneType;
import com.xm.dal.domain.Profile;
import com.xm.dal.domain.ProfileData;
import com.xm.dal.domain.ProfileStatus;
import com.xm.dal.domain.RegistrationStatus;
import com.xm.dal.domain.SimpleName;
import com.xm.dal.domain.ValidationStatus;
import com.xm.dal.domain.account.AccountStatus;
import com.xm.dal.domain.account.DemoAccount;
import com.xm.dal.domain.account.EducationAccount;
import com.xm.dal.domain.account.WalletAccount;
import com.xm.dal.domain.account.trading.Affiliate;
import com.xm.dal.domain.account.trading.LoginActivity;
import com.xm.dal.domain.account.trading.PlatformType;
import com.xm.dal.domain.account.trading.ServerInfo;
import com.xm.dal.domain.account.trading.TradingAccount;
import com.xm.dal.repository.elastic.entity.AccountManager;
import com.xm.dal.repository.elastic.entity.AccountType;
import com.xm.dal.repository.elastic.entity.AddressDetails;
import com.xm.dal.repository.elastic.entity.Author;
import com.xm.dal.repository.elastic.entity.AutoRebate;
import com.xm.dal.repository.elastic.entity.CallbackRequest;
import com.xm.dal.repository.elastic.entity.Confirmation;
import com.xm.dal.repository.elastic.entity.FinancialInfo;
import com.xm.dal.repository.elastic.entity.FirstStep;
import com.xm.dal.repository.elastic.entity.LatestLeadInfo;
import com.xm.dal.repository.elastic.entity.LeadData;
import com.xm.dal.repository.elastic.entity.PhoneCallsInfo;
import com.xm.dal.repository.elastic.entity.ProfileDocument;
import com.xm.dal.repository.elastic.entity.RafInfo;
import com.xm.dal.repository.elastic.entity.Rating;
import com.xm.dal.repository.elastic.entity.SecondStep;
import com.xm.dal.repository.elastic.entity.StreetType;
import com.xm.dal.repository.elastic.entity.WebinarAccount;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-04T17:52:36+0300",
    comments = "version: 1.6.3, compiler: javac, environment: Java 17.0.13 (Eclipse Adoptium)"
)
@Component
public class ProfileDocumentConverterImpl implements ProfileDocumentConverter {

    @Override
    public Profile convert(ProfileDocument profileDocument) {
        if ( profileDocument == null ) {
            return null;
        }

        Profile.ProfileBuilder profile = Profile.builder();

        profile.email( profileDocument.getEmail() );
        profile.brand( brandToBrand( profileDocument.getBrand() ) );
        profile.profileId( profileDocument.getProfileId() );
        profile.brandAccountId( profileDocument.getBrandAccountId() );
        profile.leadId( profileDocument.getLeadId() );
        profile.profileStatus( profileStatusToProfileStatus( profileDocument.getProfileStatus() ) );
        profile.profileData( profileDataToProfileData( profileDocument.getProfileData() ) );
        profile.tradingAccounts( tradingAccountListToTradingAccountList( profileDocument.getTradingAccounts() ) );
        profile.demoAccounts( demoAccountListToDemoAccountList( profileDocument.getDemoAccounts() ) );
        profile.educationAccounts( educationAccountListToEducationAccountList( profileDocument.getEducationAccounts() ) );
        profile.webinarAccounts( webinarAccountListToWebinarAccountList( profileDocument.getWebinarAccounts() ) );
        profile.walletAccounts( walletAccountListToWalletAccountList( profileDocument.getWalletAccounts() ) );
        profile.firstSteps( firstStepListToFirstStepList( profileDocument.getFirstSteps() ) );
        profile.secondSteps( secondStepListToSecondStepList( profileDocument.getSecondSteps() ) );
        profile.validationStatus( validationStatusToValidationStatus( profileDocument.getValidationStatus() ) );
        profile.phoneCallsInfo( phoneCallsInfoToPhoneCallsInfo( profileDocument.getPhoneCallsInfo() ) );
        profile.financialInfo( financialInfoToFinancialInfo( profileDocument.getFinancialInfo() ) );
        profile.rafInfo( rafInfoToRafInfo( profileDocument.getRafInfo() ) );
        profile.labels( labelListToLabelList( profileDocument.getLabels() ) );
        profile.ratings( ratingListToRatingList( profileDocument.getRatings() ) );
        profile.comments( commentListToCommentList( profileDocument.getComments() ) );
        profile.sat( profileDocument.getSat() );
        profile.isProfessionalClient( profileDocument.getIsProfessionalClient() );
        profile.hasActiveTradingAccounts( profileDocument.getHasActiveTradingAccounts() );
        profile.latestEmailDate( profileDocument.getLatestEmailDate() );
        profile.latestTradeDate( profileDocument.getLatestTradeDate() );
        profile.confirmations( confirmationToConfirmation( profileDocument.getConfirmations() ) );
        profile.accountManager( accountManagerToAccountManager( profileDocument.getAccountManager() ) );
        profile.affiliate( affiliateToAffiliate( profileDocument.getAffiliate() ) );
        profile.affiliateManager( accountManagerToAccountManager( profileDocument.getAffiliateManager() ) );
        profile.leadData( leadDataToLeadData( profileDocument.getLeadData() ) );
        profile.callbackRequests( callbackRequestListToCallbackRequestList( profileDocument.getCallbackRequests() ) );

        return profile.build();
    }

    protected Brand brandToBrand(com.xm.dal.repository.elastic.entity.Brand brand) {
        if ( brand == null ) {
            return null;
        }

        Brand.BrandBuilder brand1 = Brand.builder();

        brand1.brandId( brand.getBrandId() );
        brand1.brandName( brand.getBrandName() );

        return brand1.build();
    }

    protected ProfileStatus profileStatusToProfileStatus(com.xm.dal.repository.elastic.entity.ProfileStatus profileStatus) {
        if ( profileStatus == null ) {
            return null;
        }

        ProfileStatus profileStatus1;

        switch ( profileStatus ) {
            case BRAND_ACCOUNT: profileStatus1 = ProfileStatus.BRAND_ACCOUNT;
            break;
            case LEAD: profileStatus1 = ProfileStatus.LEAD;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + profileStatus );
        }

        return profileStatus1;
    }

    protected RegistrationStatus registrationStatusToRegistrationStatus(com.xm.dal.repository.elastic.entity.RegistrationStatus registrationStatus) {
        if ( registrationStatus == null ) {
            return null;
        }

        RegistrationStatus registrationStatus1;

        switch ( registrationStatus ) {
            case PENDING: registrationStatus1 = RegistrationStatus.PENDING;
            break;
            case COMPLETED: registrationStatus1 = RegistrationStatus.COMPLETED;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + registrationStatus );
        }

        return registrationStatus1;
    }

    protected Country countryToCountry(com.xm.dal.repository.elastic.entity.Country country) {
        if ( country == null ) {
            return null;
        }

        Country.CountryBuilder country1 = Country.builder();

        country1.code( country.getCode() );
        country1.name( country.getName() );

        return country1.build();
    }

    protected Language languageToLanguage(com.xm.dal.repository.elastic.entity.Language language) {
        if ( language == null ) {
            return null;
        }

        Language.LanguageBuilder language1 = Language.builder();

        language1.code( language.getCode() );
        language1.name( language.getName() );

        return language1.build();
    }

    protected Name nameToName(com.xm.dal.repository.elastic.entity.Name name) {
        if ( name == null ) {
            return null;
        }

        Name.NameBuilder name1 = Name.builder();

        name1.firstName( name.getFirstName() );
        name1.lastName( name.getLastName() );
        name1.middleName( name.getMiddleName() );
        name1.title( name.getTitle() );
        name1.nativeFirstName( name.getNativeFirstName() );
        name1.nativeLastName( name.getNativeLastName() );

        return name1.build();
    }

    protected AddressType addressTypeToAddressType(com.xm.dal.repository.elastic.entity.AddressType addressType) {
        if ( addressType == null ) {
            return null;
        }

        AddressType.AddressTypeBuilder addressType1 = AddressType.builder();

        addressType1.id( addressType.getId() );
        addressType1.description( addressType.getDescription() );

        return addressType1.build();
    }

    protected com.xm.dal.domain.StreetType streetTypeToStreetType(StreetType streetType) {
        if ( streetType == null ) {
            return null;
        }

        com.xm.dal.domain.StreetType.StreetTypeBuilder streetType1 = com.xm.dal.domain.StreetType.builder();

        streetType1.type( streetType.getType() );
        streetType1.abbreviatedType( streetType.getAbbreviatedType() );

        return streetType1.build();
    }

    protected com.xm.dal.domain.AddressDetails addressDetailsToAddressDetails(AddressDetails addressDetails) {
        if ( addressDetails == null ) {
            return null;
        }

        com.xm.dal.domain.AddressDetails.AddressDetailsBuilder addressDetails1 = com.xm.dal.domain.AddressDetails.builder();

        addressDetails1.zipCode( addressDetails.getZipCode() );
        addressDetails1.state( addressDetails.getState() );
        addressDetails1.province( addressDetails.getProvince() );
        addressDetails1.poBox( addressDetails.getPoBox() );
        addressDetails1.streetType( streetTypeToStreetType( addressDetails.getStreetType() ) );
        addressDetails1.streetName( addressDetails.getStreetName() );
        addressDetails1.streetNumber( addressDetails.getStreetNumber() );
        addressDetails1.buildingName( addressDetails.getBuildingName() );
        addressDetails1.buildingNumber( addressDetails.getBuildingNumber() );

        return addressDetails1.build();
    }

    protected Address addressToAddress(com.xm.dal.repository.elastic.entity.Address address) {
        if ( address == null ) {
            return null;
        }

        Address.AddressBuilder address1 = Address.builder();

        address1.addressType( addressTypeToAddressType( address.getAddressType() ) );
        address1.addressFull( address.getAddressFull() );
        address1.isVerified( address.getIsVerified() );
        address1.city( address.getCity() );
        address1.country( countryToCountry( address.getCountry() ) );
        address1.details( addressDetailsToAddressDetails( address.getDetails() ) );

        return address1.build();
    }

    protected PhoneType phoneTypeToPhoneType(com.xm.dal.repository.elastic.entity.PhoneType phoneType) {
        if ( phoneType == null ) {
            return null;
        }

        PhoneType.PhoneTypeBuilder phoneType1 = PhoneType.builder();

        phoneType1.id( phoneType.getId() );
        phoneType1.description( phoneType.getDescription() );

        return phoneType1.build();
    }

    protected Phone phoneToPhone(com.xm.dal.repository.elastic.entity.Phone phone) {
        if ( phone == null ) {
            return null;
        }

        Phone.PhoneBuilder phone1 = Phone.builder();

        phone1.phoneId( phone.getPhoneId() );
        phone1.phoneNumber( phone.getPhoneNumber() );
        phone1.phoneType( phoneTypeToPhoneType( phone.getPhoneType() ) );
        phone1.priorityOrder( phone.getPriorityOrder() );
        phone1.isVerified( phone.getIsVerified() );
        phone1.regionCode( phone.getRegionCode() );
        phone1.prefix( phone.getPrefix() );
        phone1.local( phone.getLocal() );
        phone1.international( phone.getInternational() );
        phone1.processingType( phone.getProcessingType() );

        return phone1.build();
    }

    protected List<Phone> phoneListToPhoneList(List<com.xm.dal.repository.elastic.entity.Phone> list) {
        if ( list == null ) {
            return null;
        }

        List<Phone> list1 = new ArrayList<Phone>( list.size() );
        for ( com.xm.dal.repository.elastic.entity.Phone phone : list ) {
            list1.add( phoneToPhone( phone ) );
        }

        return list1;
    }

    protected ProfileData profileDataToProfileData(com.xm.dal.repository.elastic.entity.ProfileData profileData) {
        if ( profileData == null ) {
            return null;
        }

        ProfileData.ProfileDataBuilder profileData1 = ProfileData.builder();

        profileData1.registrationDate( profileData.getRegistrationDate() );
        profileData1.registrationStatus( registrationStatusToRegistrationStatus( profileData.getRegistrationStatus() ) );
        profileData1.country( countryToCountry( profileData.getCountry() ) );
        profileData1.preferredLanguage( languageToLanguage( profileData.getPreferredLanguage() ) );
        profileData1.dateOfBirth( profileData.getDateOfBirth() );
        profileData1.name( nameToName( profileData.getName() ) );
        profileData1.address( addressToAddress( profileData.getAddress() ) );
        profileData1.phones( phoneListToPhoneList( profileData.getPhones() ) );
        profileData1.lastUpdateDate( profileData.getLastUpdateDate() );

        return profileData1.build();
    }

    protected AccountStatus accountStatusToAccountStatus(com.xm.dal.repository.elastic.entity.AccountStatus accountStatus) {
        if ( accountStatus == null ) {
            return null;
        }

        AccountStatus accountStatus1;

        switch ( accountStatus ) {
            case ACTIVE: accountStatus1 = AccountStatus.ACTIVE;
            break;
            case ARCHIVED: accountStatus1 = AccountStatus.ARCHIVED;
            break;
            case CLOSED: accountStatus1 = AccountStatus.CLOSED;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + accountStatus );
        }

        return accountStatus1;
    }

    protected PlatformType platformTypeToPlatformType(com.xm.dal.repository.elastic.entity.PlatformType platformType) {
        if ( platformType == null ) {
            return null;
        }

        PlatformType platformType1;

        switch ( platformType ) {
            case MT4: platformType1 = PlatformType.MT4;
            break;
            case MT5: platformType1 = PlatformType.MT5;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + platformType );
        }

        return platformType1;
    }

    protected ServerInfo serverInfoToServerInfo(com.xm.dal.repository.elastic.entity.ServerInfo serverInfo) {
        if ( serverInfo == null ) {
            return null;
        }

        ServerInfo.ServerInfoBuilder serverInfo1 = ServerInfo.builder();

        serverInfo1.serverId( serverInfo.getServerId() );
        serverInfo1.serverName( serverInfo.getServerName() );
        serverInfo1.serverType( serverInfo.getServerType() );
        serverInfo1.platformType( platformTypeToPlatformType( serverInfo.getPlatformType() ) );
        serverInfo1.serverLoginName( serverInfo.getServerLoginName() );

        return serverInfo1.build();
    }

    protected com.xm.dal.domain.account.AccountType accountTypeToAccountType(AccountType accountType) {
        if ( accountType == null ) {
            return null;
        }

        com.xm.dal.domain.account.AccountType.AccountTypeBuilder accountType1 = com.xm.dal.domain.account.AccountType.builder();

        accountType1.id( accountType.getId() );
        accountType1.name( accountType.getName() );

        return accountType1.build();
    }

    protected com.xm.dal.domain.account.AccountManager accountManagerToAccountManager(AccountManager accountManager) {
        if ( accountManager == null ) {
            return null;
        }

        com.xm.dal.domain.account.AccountManager.AccountManagerBuilder accountManager1 = com.xm.dal.domain.account.AccountManager.builder();

        accountManager1.id( accountManager.getId() );
        accountManager1.firstName( accountManager.getFirstName() );
        accountManager1.lastName( accountManager.getLastName() );
        accountManager1.email( accountManager.getEmail() );
        accountManager1.office( accountManager.getOffice() );

        return accountManager1.build();
    }

    protected Affiliate affiliateToAffiliate(com.xm.dal.repository.elastic.entity.Affiliate affiliate) {
        if ( affiliate == null ) {
            return null;
        }

        Affiliate.AffiliateBuilder affiliate1 = Affiliate.builder();

        affiliate1.id( affiliate.getId() );
        affiliate1.firstName( affiliate.getFirstName() );
        affiliate1.lastName( affiliate.getLastName() );

        return affiliate1.build();
    }

    protected com.xm.dal.domain.account.trading.AutoRebate autoRebateToAutoRebate(AutoRebate autoRebate) {
        if ( autoRebate == null ) {
            return null;
        }

        com.xm.dal.domain.account.trading.AutoRebate.AutoRebateBuilder autoRebate1 = com.xm.dal.domain.account.trading.AutoRebate.builder();

        autoRebate1.isEligible( autoRebate.getIsEligible() );
        autoRebate1.hasAccepted( autoRebate.getHasAccepted() );
        autoRebate1.acceptedDate( autoRebate.getAcceptedDate() );

        return autoRebate1.build();
    }

    protected LoginActivity loginActivityToLoginActivity(com.xm.dal.repository.elastic.entity.LoginActivity loginActivity) {
        if ( loginActivity == null ) {
            return null;
        }

        LoginActivity.LoginActivityBuilder loginActivity1 = LoginActivity.builder();

        loginActivity1.membersAreaLastLogin( loginActivity.getMembersAreaLastLogin() );
        loginActivity1.mobileLastLogin( loginActivity.getMobileLastLogin() );
        loginActivity1.mqTerminalLastLogin( loginActivity.getMqTerminalLastLogin() );
        loginActivity1.webTraderLastLogin( loginActivity.getWebTraderLastLogin() );

        return loginActivity1.build();
    }

    protected TradingAccount tradingAccountToTradingAccount(com.xm.dal.repository.elastic.entity.TradingAccount tradingAccount) {
        if ( tradingAccount == null ) {
            return null;
        }

        TradingAccount.TradingAccountBuilder tradingAccount1 = TradingAccount.builder();

        tradingAccount1.login( tradingAccount.getLogin() );
        tradingAccount1.currency( tradingAccount.getCurrency() );
        tradingAccount1.registrationDate( tradingAccount.getRegistrationDate() );
        tradingAccount1.registrationIp( tradingAccount.getRegistrationIp() );
        tradingAccount1.registrationIpCountry( countryToCountry( tradingAccount.getRegistrationIpCountry() ) );
        tradingAccount1.accountStatus( accountStatusToAccountStatus( tradingAccount.getAccountStatus() ) );
        tradingAccount1.isEnabled( tradingAccount.getIsEnabled() );
        tradingAccount1.isReadOnly( tradingAccount.getIsReadOnly() );
        tradingAccount1.sendReports( tradingAccount.getSendReports() );
        tradingAccount1.archivedDate( tradingAccount.getArchivedDate() );
        tradingAccount1.closedDate( tradingAccount.getClosedDate() );
        tradingAccount1.gid( tradingAccount.getGid() );
        tradingAccount1.source( tradingAccount.getSource() );
        tradingAccount1.gwClickDate( tradingAccount.getGwClickDate() );
        tradingAccount1.serverInfo( serverInfoToServerInfo( tradingAccount.getServerInfo() ) );
        tradingAccount1.accountType( accountTypeToAccountType( tradingAccount.getAccountType() ) );
        tradingAccount1.accountManager( accountManagerToAccountManager( tradingAccount.getAccountManager() ) );
        tradingAccount1.affiliate( affiliateToAffiliate( tradingAccount.getAffiliate() ) );
        tradingAccount1.affiliateManager( accountManagerToAccountManager( tradingAccount.getAffiliateManager() ) );
        tradingAccount1.comment( tradingAccount.getComment() );
        tradingAccount1.leverage( tradingAccount.getLeverage() );
        tradingAccount1.socialTradingStatus( tradingAccount.getSocialTradingStatus() );
        tradingAccount1.groupName( tradingAccount.getGroupName() );
        tradingAccount1.accountName( tradingAccount.getAccountName() );
        tradingAccount1.bonusTermsAgreed( tradingAccount.getBonusTermsAgreed() );
        tradingAccount1.autoRebate( autoRebateToAutoRebate( tradingAccount.getAutoRebate() ) );
        tradingAccount1.loginActivity( loginActivityToLoginActivity( tradingAccount.getLoginActivity() ) );
        tradingAccount1.isIslamic( tradingAccount.getIsIslamic() );

        return tradingAccount1.build();
    }

    protected List<TradingAccount> tradingAccountListToTradingAccountList(List<com.xm.dal.repository.elastic.entity.TradingAccount> list) {
        if ( list == null ) {
            return null;
        }

        List<TradingAccount> list1 = new ArrayList<TradingAccount>( list.size() );
        for ( com.xm.dal.repository.elastic.entity.TradingAccount tradingAccount : list ) {
            list1.add( tradingAccountToTradingAccount( tradingAccount ) );
        }

        return list1;
    }

    protected SimpleName simpleNameToSimpleName(com.xm.dal.repository.elastic.entity.SimpleName simpleName) {
        if ( simpleName == null ) {
            return null;
        }

        SimpleName.SimpleNameBuilder simpleName1 = SimpleName.builder();

        simpleName1.firstName( simpleName.getFirstName() );
        simpleName1.lastName( simpleName.getLastName() );
        simpleName1.middleName( simpleName.getMiddleName() );

        return simpleName1.build();
    }

    protected LeadPhone leadPhoneToLeadPhone(com.xm.dal.repository.elastic.entity.LeadPhone leadPhone) {
        if ( leadPhone == null ) {
            return null;
        }

        LeadPhone.LeadPhoneBuilder leadPhone1 = LeadPhone.builder();

        leadPhone1.phoneNumber( leadPhone.getPhoneNumber() );
        leadPhone1.regionCode( leadPhone.getRegionCode() );
        leadPhone1.prefix( leadPhone.getPrefix() );
        leadPhone1.local( leadPhone.getLocal() );
        leadPhone1.international( leadPhone.getInternational() );
        leadPhone1.processingType( leadPhone.getProcessingType() );

        return leadPhone1.build();
    }

    protected DemoAccount demoAccountToDemoAccount(com.xm.dal.repository.elastic.entity.DemoAccount demoAccount) {
        if ( demoAccount == null ) {
            return null;
        }

        DemoAccount.DemoAccountBuilder demoAccount1 = DemoAccount.builder();

        demoAccount1.login( demoAccount.getLogin() );
        demoAccount1.currency( demoAccount.getCurrency() );
        demoAccount1.registrationDate( demoAccount.getRegistrationDate() );
        demoAccount1.registrationIp( demoAccount.getRegistrationIp() );
        demoAccount1.registrationIpCountry( countryToCountry( demoAccount.getRegistrationIpCountry() ) );
        demoAccount1.accountStatus( accountStatusToAccountStatus( demoAccount.getAccountStatus() ) );
        demoAccount1.isEnabled( demoAccount.getIsEnabled() );
        demoAccount1.isReadOnly( demoAccount.getIsReadOnly() );
        demoAccount1.sendReports( demoAccount.getSendReports() );
        demoAccount1.archivedDate( demoAccount.getArchivedDate() );
        demoAccount1.closedDate( demoAccount.getClosedDate() );
        demoAccount1.gid( demoAccount.getGid() );
        demoAccount1.source( demoAccount.getSource() );
        demoAccount1.gwClickDate( demoAccount.getGwClickDate() );
        demoAccount1.serverInfo( serverInfoToServerInfo( demoAccount.getServerInfo() ) );
        demoAccount1.accountType( accountTypeToAccountType( demoAccount.getAccountType() ) );
        demoAccount1.accountManager( accountManagerToAccountManager( demoAccount.getAccountManager() ) );
        demoAccount1.affiliate( affiliateToAffiliate( demoAccount.getAffiliate() ) );
        demoAccount1.affiliateManager( accountManagerToAccountManager( demoAccount.getAffiliateManager() ) );
        demoAccount1.comment( demoAccount.getComment() );
        demoAccount1.leverage( demoAccount.getLeverage() );
        demoAccount1.socialTradingStatus( demoAccount.getSocialTradingStatus() );
        demoAccount1.groupName( demoAccount.getGroupName() );
        demoAccount1.accountName( demoAccount.getAccountName() );
        demoAccount1.simCountry( countryToCountry( demoAccount.getSimCountry() ) );
        demoAccount1.name( simpleNameToSimpleName( demoAccount.getName() ) );
        demoAccount1.phone( leadPhoneToLeadPhone( demoAccount.getPhone() ) );
        demoAccount1.country( countryToCountry( demoAccount.getCountry() ) );
        demoAccount1.preferredLanguage( languageToLanguage( demoAccount.getPreferredLanguage() ) );

        return demoAccount1.build();
    }

    protected List<DemoAccount> demoAccountListToDemoAccountList(List<com.xm.dal.repository.elastic.entity.DemoAccount> list) {
        if ( list == null ) {
            return null;
        }

        List<DemoAccount> list1 = new ArrayList<DemoAccount>( list.size() );
        for ( com.xm.dal.repository.elastic.entity.DemoAccount demoAccount : list ) {
            list1.add( demoAccountToDemoAccount( demoAccount ) );
        }

        return list1;
    }

    protected EducationAccount educationAccountToEducationAccount(com.xm.dal.repository.elastic.entity.EducationAccount educationAccount) {
        if ( educationAccount == null ) {
            return null;
        }

        EducationAccount.EducationAccountBuilder educationAccount1 = EducationAccount.builder();

        educationAccount1.id( educationAccount.getId() );
        educationAccount1.name( simpleNameToSimpleName( educationAccount.getName() ) );
        educationAccount1.phone( leadPhoneToLeadPhone( educationAccount.getPhone() ) );
        educationAccount1.country( countryToCountry( educationAccount.getCountry() ) );
        educationAccount1.accountManager( accountManagerToAccountManager( educationAccount.getAccountManager() ) );
        educationAccount1.registrationDate( educationAccount.getRegistrationDate() );
        educationAccount1.acceptedContact( educationAccount.getAcceptedContact() );

        return educationAccount1.build();
    }

    protected List<EducationAccount> educationAccountListToEducationAccountList(List<com.xm.dal.repository.elastic.entity.EducationAccount> list) {
        if ( list == null ) {
            return null;
        }

        List<EducationAccount> list1 = new ArrayList<EducationAccount>( list.size() );
        for ( com.xm.dal.repository.elastic.entity.EducationAccount educationAccount : list ) {
            list1.add( educationAccountToEducationAccount( educationAccount ) );
        }

        return list1;
    }

    protected com.xm.dal.domain.account.WebinarAccount webinarAccountToWebinarAccount(WebinarAccount webinarAccount) {
        if ( webinarAccount == null ) {
            return null;
        }

        com.xm.dal.domain.account.WebinarAccount.WebinarAccountBuilder webinarAccount1 = com.xm.dal.domain.account.WebinarAccount.builder();

        webinarAccount1.id( webinarAccount.getId() );
        webinarAccount1.name( simpleNameToSimpleName( webinarAccount.getName() ) );
        webinarAccount1.phone( leadPhoneToLeadPhone( webinarAccount.getPhone() ) );
        webinarAccount1.country( countryToCountry( webinarAccount.getCountry() ) );
        webinarAccount1.city( webinarAccount.getCity() );
        webinarAccount1.accountManager( accountManagerToAccountManager( webinarAccount.getAccountManager() ) );
        webinarAccount1.affiliate( affiliateToAffiliate( webinarAccount.getAffiliate() ) );
        webinarAccount1.affiliateManager( accountManagerToAccountManager( webinarAccount.getAffiliateManager() ) );
        webinarAccount1.registrationDate( webinarAccount.getRegistrationDate() );

        return webinarAccount1.build();
    }

    protected List<com.xm.dal.domain.account.WebinarAccount> webinarAccountListToWebinarAccountList(List<WebinarAccount> list) {
        if ( list == null ) {
            return null;
        }

        List<com.xm.dal.domain.account.WebinarAccount> list1 = new ArrayList<com.xm.dal.domain.account.WebinarAccount>( list.size() );
        for ( WebinarAccount webinarAccount : list ) {
            list1.add( webinarAccountToWebinarAccount( webinarAccount ) );
        }

        return list1;
    }

    protected WalletAccount walletAccountToWalletAccount(com.xm.dal.repository.elastic.entity.WalletAccount walletAccount) {
        if ( walletAccount == null ) {
            return null;
        }

        WalletAccount.WalletAccountBuilder walletAccount1 = WalletAccount.builder();

        walletAccount1.login( walletAccount.getLogin() );
        walletAccount1.brand( brandToBrand( walletAccount.getBrand() ) );
        walletAccount1.currency( walletAccount.getCurrency() );
        walletAccount1.registrationDate( walletAccount.getRegistrationDate() );
        walletAccount1.registrationIp( walletAccount.getRegistrationIp() );
        walletAccount1.registrationIpCountry( countryToCountry( walletAccount.getRegistrationIpCountry() ) );
        walletAccount1.accountStatus( accountStatusToAccountStatus( walletAccount.getAccountStatus() ) );
        walletAccount1.isEnabled( walletAccount.getIsEnabled() );
        walletAccount1.isReadOnly( walletAccount.getIsReadOnly() );
        walletAccount1.sendReports( walletAccount.getSendReports() );
        walletAccount1.promoAgreed( walletAccount.getPromoAgreed() );
        walletAccount1.archivedDate( walletAccount.getArchivedDate() );
        walletAccount1.source( walletAccount.getSource() );
        walletAccount1.serverInfo( serverInfoToServerInfo( walletAccount.getServerInfo() ) );
        walletAccount1.accountManager( accountManagerToAccountManager( walletAccount.getAccountManager() ) );
        walletAccount1.affiliateId( walletAccount.getAffiliateId() );
        walletAccount1.comment( walletAccount.getComment() );
        walletAccount1.groupName( walletAccount.getGroupName() );

        return walletAccount1.build();
    }

    protected List<WalletAccount> walletAccountListToWalletAccountList(List<com.xm.dal.repository.elastic.entity.WalletAccount> list) {
        if ( list == null ) {
            return null;
        }

        List<WalletAccount> list1 = new ArrayList<WalletAccount>( list.size() );
        for ( com.xm.dal.repository.elastic.entity.WalletAccount walletAccount : list ) {
            list1.add( walletAccountToWalletAccount( walletAccount ) );
        }

        return list1;
    }

    protected com.xm.dal.domain.account.FirstStep firstStepToFirstStep(FirstStep firstStep) {
        if ( firstStep == null ) {
            return null;
        }

        com.xm.dal.domain.account.FirstStep.FirstStepBuilder firstStep1 = com.xm.dal.domain.account.FirstStep.builder();

        firstStep1.id( firstStep.getId() );
        firstStep1.name( simpleNameToSimpleName( firstStep.getName() ) );
        firstStep1.phone( leadPhoneToLeadPhone( firstStep.getPhone() ) );
        firstStep1.country( countryToCountry( firstStep.getCountry() ) );
        firstStep1.preferredLanguage( languageToLanguage( firstStep.getPreferredLanguage() ) );
        firstStep1.accountManager( accountManagerToAccountManager( firstStep.getAccountManager() ) );
        firstStep1.affiliate( affiliateToAffiliate( firstStep.getAffiliate() ) );
        firstStep1.affiliateManager( accountManagerToAccountManager( firstStep.getAffiliateManager() ) );
        firstStep1.registrationDate( firstStep.getRegistrationDate() );

        return firstStep1.build();
    }

    protected List<com.xm.dal.domain.account.FirstStep> firstStepListToFirstStepList(List<FirstStep> list) {
        if ( list == null ) {
            return null;
        }

        List<com.xm.dal.domain.account.FirstStep> list1 = new ArrayList<com.xm.dal.domain.account.FirstStep>( list.size() );
        for ( FirstStep firstStep : list ) {
            list1.add( firstStepToFirstStep( firstStep ) );
        }

        return list1;
    }

    protected com.xm.dal.domain.account.SecondStep secondStepToSecondStep(SecondStep secondStep) {
        if ( secondStep == null ) {
            return null;
        }

        com.xm.dal.domain.account.SecondStep.SecondStepBuilder secondStep1 = com.xm.dal.domain.account.SecondStep.builder();

        secondStep1.id( secondStep.getId() );
        secondStep1.name( simpleNameToSimpleName( secondStep.getName() ) );
        secondStep1.phone( leadPhoneToLeadPhone( secondStep.getPhone() ) );
        secondStep1.country( countryToCountry( secondStep.getCountry() ) );
        secondStep1.city( secondStep.getCity() );
        secondStep1.dateOfBirth( secondStep.getDateOfBirth() );
        secondStep1.preferredLanguage( languageToLanguage( secondStep.getPreferredLanguage() ) );
        secondStep1.accountManager( accountManagerToAccountManager( secondStep.getAccountManager() ) );
        secondStep1.affiliate( affiliateToAffiliate( secondStep.getAffiliate() ) );
        secondStep1.affiliateManager( accountManagerToAccountManager( secondStep.getAffiliateManager() ) );
        secondStep1.registrationDate( secondStep.getRegistrationDate() );

        return secondStep1.build();
    }

    protected List<com.xm.dal.domain.account.SecondStep> secondStepListToSecondStepList(List<SecondStep> list) {
        if ( list == null ) {
            return null;
        }

        List<com.xm.dal.domain.account.SecondStep> list1 = new ArrayList<com.xm.dal.domain.account.SecondStep>( list.size() );
        for ( SecondStep secondStep : list ) {
            list1.add( secondStepToSecondStep( secondStep ) );
        }

        return list1;
    }

    protected ValidationStatus validationStatusToValidationStatus(com.xm.dal.repository.elastic.entity.ValidationStatus validationStatus) {
        if ( validationStatus == null ) {
            return null;
        }

        ValidationStatus.ValidationStatusBuilder validationStatus1 = ValidationStatus.builder();

        validationStatus1.documentExpirationDate( validationStatus.getDocumentExpirationDate() );
        validationStatus1.isValidated( validationStatus.getIsValidated() );
        validationStatus1.isRevalidationRequested( validationStatus.getIsRevalidationRequested() );
        validationStatus1.validationDate( validationStatus.getValidationDate() );

        return validationStatus1.build();
    }

    protected com.xm.dal.domain.PhoneCallsInfo phoneCallsInfoToPhoneCallsInfo(PhoneCallsInfo phoneCallsInfo) {
        if ( phoneCallsInfo == null ) {
            return null;
        }

        com.xm.dal.domain.PhoneCallsInfo.PhoneCallsInfoBuilder phoneCallsInfo1 = com.xm.dal.domain.PhoneCallsInfo.builder();

        phoneCallsInfo1.latestValidPhoneCallDate( phoneCallsInfo.getLatestValidPhoneCallDate() );
        phoneCallsInfo1.latestInvalidPhoneCallDate( phoneCallsInfo.getLatestInvalidPhoneCallDate() );
        phoneCallsInfo1.latestPhoneCallAttemptDate( phoneCallsInfo.getLatestPhoneCallAttemptDate() );
        phoneCallsInfo1.totalPhoneCallAttempts( phoneCallsInfo.getTotalPhoneCallAttempts() );
        phoneCallsInfo1.totalInvalidPhoneCalls( phoneCallsInfo.getTotalInvalidPhoneCalls() );
        phoneCallsInfo1.totalValidBeforeClientActivation( phoneCallsInfo.getTotalValidBeforeClientActivation() );
        phoneCallsInfo1.totalInvalidBeforeClientActivation( phoneCallsInfo.getTotalInvalidBeforeClientActivation() );
        phoneCallsInfo1.totalAttemptsBeforeClientActivation( phoneCallsInfo.getTotalAttemptsBeforeClientActivation() );
        phoneCallsInfo1.totalOtherCommunicatedPhoneCalls( phoneCallsInfo.getTotalOtherCommunicatedPhoneCalls() );

        return phoneCallsInfo1.build();
    }

    protected com.xm.dal.domain.FinancialInfo financialInfoToFinancialInfo(FinancialInfo financialInfo) {
        if ( financialInfo == null ) {
            return null;
        }

        com.xm.dal.domain.FinancialInfo.FinancialInfoBuilder financialInfo1 = com.xm.dal.domain.FinancialInfo.builder();

        financialInfo1.depositCount( financialInfo.getDepositCount() );
        financialInfo1.firstDepositDate( financialInfo.getFirstDepositDate() );
        financialInfo1.latestDepositDate( financialInfo.getLatestDepositDate() );
        financialInfo1.latestMonthPnlEur( financialInfo.getLatestMonthPnlEur() );
        financialInfo1.totalBalanceEur( financialInfo.getTotalBalanceEur() );
        financialInfo1.totalCreditEur( financialInfo.getTotalCreditEur() );
        financialInfo1.totalDepositsEur( financialInfo.getTotalDepositsEur() );
        financialInfo1.totalEquityEur( financialInfo.getTotalEquityEur() );
        financialInfo1.totalPnlEur( financialInfo.getTotalPnlEur() );
        financialInfo1.totalWithdrawalsEur( financialInfo.getTotalWithdrawalsEur() );

        return financialInfo1.build();
    }

    protected com.xm.dal.domain.RafInfo rafInfoToRafInfo(RafInfo rafInfo) {
        if ( rafInfo == null ) {
            return null;
        }

        com.xm.dal.domain.RafInfo.RafInfoBuilder rafInfo1 = com.xm.dal.domain.RafInfo.builder();

        rafInfo1.isReferrer( rafInfo.getIsReferrer() );
        rafInfo1.isReferredClient( rafInfo.getIsReferredClient() );
        rafInfo1.isRafQualified( rafInfo.getIsRafQualified() );
        rafInfo1.connectionStatusId( rafInfo.getConnectionStatusId() );
        rafInfo1.connectionStatusDesc( rafInfo.getConnectionStatusDesc() );
        rafInfo1.referrerToken( rafInfo.getReferrerToken() );

        return rafInfo1.build();
    }

    protected Label labelToLabel(com.xm.dal.repository.elastic.entity.Label label) {
        if ( label == null ) {
            return null;
        }

        Label.LabelBuilder label1 = Label.builder();

        label1.id( label.getId() );
        label1.name( label.getName() );
        label1.categoryColor( label.getCategoryColor() );
        label1.isActive( label.getIsActive() );

        return label1.build();
    }

    protected List<Label> labelListToLabelList(List<com.xm.dal.repository.elastic.entity.Label> list) {
        if ( list == null ) {
            return null;
        }

        List<Label> list1 = new ArrayList<Label>( list.size() );
        for ( com.xm.dal.repository.elastic.entity.Label label : list ) {
            list1.add( labelToLabel( label ) );
        }

        return list1;
    }

    protected com.xm.dal.domain.Author authorToAuthor(Author author) {
        if ( author == null ) {
            return null;
        }

        com.xm.dal.domain.Author.AuthorBuilder author1 = com.xm.dal.domain.Author.builder();

        author1.ldapId( author.getLdapId() );
        author1.firstName( author.getFirstName() );
        author1.lastName( author.getLastName() );

        return author1.build();
    }

    protected com.xm.dal.domain.Rating ratingToRating(Rating rating) {
        if ( rating == null ) {
            return null;
        }

        com.xm.dal.domain.Rating.RatingBuilder rating1 = com.xm.dal.domain.Rating.builder();

        rating1.id( rating.getId() );
        rating1.description( rating.getDescription() );
        rating1.dateAdded( rating.getDateAdded() );
        rating1.author( authorToAuthor( rating.getAuthor() ) );

        return rating1.build();
    }

    protected List<com.xm.dal.domain.Rating> ratingListToRatingList(List<Rating> list) {
        if ( list == null ) {
            return null;
        }

        List<com.xm.dal.domain.Rating> list1 = new ArrayList<com.xm.dal.domain.Rating>( list.size() );
        for ( Rating rating : list ) {
            list1.add( ratingToRating( rating ) );
        }

        return list1;
    }

    protected Comment commentToComment(com.xm.dal.repository.elastic.entity.Comment comment) {
        if ( comment == null ) {
            return null;
        }

        Comment.CommentBuilder comment1 = Comment.builder();

        comment1.id( comment.getId() );
        comment1.text( comment.getText() );
        comment1.dateAdded( comment.getDateAdded() );
        comment1.relatedLogin( comment.getRelatedLogin() );
        comment1.author( authorToAuthor( comment.getAuthor() ) );

        return comment1.build();
    }

    protected List<Comment> commentListToCommentList(List<com.xm.dal.repository.elastic.entity.Comment> list) {
        if ( list == null ) {
            return null;
        }

        List<Comment> list1 = new ArrayList<Comment>( list.size() );
        for ( com.xm.dal.repository.elastic.entity.Comment comment : list ) {
            list1.add( commentToComment( comment ) );
        }

        return list1;
    }

    protected com.xm.dal.domain.Confirmation confirmationToConfirmation(Confirmation confirmation) {
        if ( confirmation == null ) {
            return null;
        }

        com.xm.dal.domain.Confirmation.ConfirmationBuilder confirmation1 = com.xm.dal.domain.Confirmation.builder();

        confirmation1.promoAgreed( confirmation.getPromoAgreed() );

        return confirmation1.build();
    }

    protected com.xm.dal.domain.LatestLeadInfo latestLeadInfoToLatestLeadInfo(LatestLeadInfo latestLeadInfo) {
        if ( latestLeadInfo == null ) {
            return null;
        }

        com.xm.dal.domain.LatestLeadInfo.LatestLeadInfoBuilder latestLeadInfo1 = com.xm.dal.domain.LatestLeadInfo.builder();

        latestLeadInfo1.name( simpleNameToSimpleName( latestLeadInfo.getName() ) );
        latestLeadInfo1.phone( leadPhoneToLeadPhone( latestLeadInfo.getPhone() ) );
        latestLeadInfo1.country( countryToCountry( latestLeadInfo.getCountry() ) );
        latestLeadInfo1.preferredLanguage( languageToLanguage( latestLeadInfo.getPreferredLanguage() ) );
        latestLeadInfo1.registrationDate( latestLeadInfo.getRegistrationDate() );
        latestLeadInfo1.comment( commentToComment( latestLeadInfo.getComment() ) );
        latestLeadInfo1.ratings( ratingListToRatingList( latestLeadInfo.getRatings() ) );

        return latestLeadInfo1.build();
    }

    protected com.xm.dal.domain.LeadData leadDataToLeadData(LeadData leadData) {
        if ( leadData == null ) {
            return null;
        }

        com.xm.dal.domain.LeadData.LeadDataBuilder leadData1 = com.xm.dal.domain.LeadData.builder();

        leadData1.latestInfo( latestLeadInfoToLatestLeadInfo( leadData.getLatestInfo() ) );

        return leadData1.build();
    }

    protected CallbackRequestStatus callbackRequestStatusToCallbackRequestStatus(com.xm.dal.repository.elastic.entity.CallbackRequestStatus callbackRequestStatus) {
        if ( callbackRequestStatus == null ) {
            return null;
        }

        CallbackRequestStatus callbackRequestStatus1;

        switch ( callbackRequestStatus ) {
            case CREATED: callbackRequestStatus1 = CallbackRequestStatus.CREATED;
            break;
            case COMPLETED: callbackRequestStatus1 = CallbackRequestStatus.COMPLETED;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + callbackRequestStatus );
        }

        return callbackRequestStatus1;
    }

    protected com.xm.dal.domain.CallbackRequest callbackRequestToCallbackRequest(CallbackRequest callbackRequest) {
        if ( callbackRequest == null ) {
            return null;
        }

        com.xm.dal.domain.CallbackRequest.CallbackRequestBuilder callbackRequest1 = com.xm.dal.domain.CallbackRequest.builder();

        callbackRequest1.id( callbackRequest.getId() );
        callbackRequest1.conversationTopic( callbackRequest.getConversationTopic() );
        callbackRequest1.status( callbackRequestStatusToCallbackRequestStatus( callbackRequest.getStatus() ) );
        callbackRequest1.creationDate( callbackRequest.getCreationDate() );
        callbackRequest1.completionDate( callbackRequest.getCompletionDate() );

        return callbackRequest1.build();
    }

    protected List<com.xm.dal.domain.CallbackRequest> callbackRequestListToCallbackRequestList(List<CallbackRequest> list) {
        if ( list == null ) {
            return null;
        }

        List<com.xm.dal.domain.CallbackRequest> list1 = new ArrayList<com.xm.dal.domain.CallbackRequest>( list.size() );
        for ( CallbackRequest callbackRequest : list ) {
            list1.add( callbackRequestToCallbackRequest( callbackRequest ) );
        }

        return list1;
    }
}
