package com.xm.dal.repository.elastic.converter;

import com.xm.dal.domain.Language;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-04T17:52:36+0300",
    comments = "version: 1.6.3, compiler: javac, environment: Java 17.0.13 (Eclipse Adoptium)"
)
@Component
public class LanguageConverterImpl implements LanguageConverter {

    @Override
    public com.xm.dal.repository.elastic.entity.Language convert(Language language) {
        if ( language == null ) {
            return null;
        }

        com.xm.dal.repository.elastic.entity.Language.LanguageBuilder language1 = com.xm.dal.repository.elastic.entity.Language.builder();

        language1.code( language.code() );
        language1.name( language.name() );

        return language1.build();
    }
}
