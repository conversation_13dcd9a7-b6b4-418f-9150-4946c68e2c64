# Spring AI MCP Client + LLM Integration with Elasticsearch

This document describes the complete integration of Spring AI MCP (Model Context Protocol) client with the Elasticsearch MCP server, enhanced with LLM-powered natural language query translation.

## Overview

The integration provides two main capabilities:
1. **Direct MCP Communication**: Direct interaction with Elasticsearch through the MCP protocol
2. **Natural Language Processing**: LLM-powered translation of natural language queries to Elasticsearch queries, then executed via MCP

This creates a complete pipeline: Natural Language → LLM Translation → Elasticsearch Query → MCP Execution → Results

## Prerequisites

1. **Node.js and npm**: Required to run the Elasticsearch MCP server
2. **Elasticsearch MCP Server**: Install the Elastic MCP server package
   ```bash
   npm install -g @elastic/mcp-server-elasticsearch
   ```
3. **Elasticsearch**: Running instance (default: localhost:9200)
4. **OpenAI API Key** (Optional): Required for natural language query translation
   - Set environment variable: `OPENAI_API_KEY=your-api-key-here`
   - Without this, only direct MCP operations are available

## Configuration

### MCP Client Configuration
The MCP client is configured in `application.yaml`:

```yaml
spring:
  ai:
    mcp:
      client:
        stdio:
          servers-configuration: classpath:mcp-servers.json
```

### MCP Servers Configuration
The MCP servers are defined in `mcp-servers.json`:

```json
{
  "elasticsearch": {
    "command": "npx",
    "args": [
      "@elastic/mcp-server-elasticsearch",
      "--elasticsearch-url",
      "http://localhost:9200"
    ]
  }
}
```

### OpenAI Configuration (Optional)
For natural language processing:

```yaml
spring:
  ai:
    openai:
      api-key: ${OPENAI_API_KEY:sk-dummy-key-for-testing}
      chat:
        options:
          model: gpt-4o-mini
          temperature: 0.1
```

## Components

### 1. McpClientConfig
Configuration class that sets up the MCP client beans.

### 2. EsMcpService
Service class providing methods to interact with Elasticsearch via MCP:
- `listAvailableTools()`: Lists all available MCP tools
- `executeSearch(index, query)`: Executes search operations
- `getIndexInfo(index)`: Gets information about an index
- `listIndices()`: Lists all indices
- `executeCustomOperation(toolName, arguments)`: Executes custom operations

### 3. EsNlqService (Conditional)
Service class for natural language query translation (only available when OpenAI is configured):
- `translateToElasticsearchQuery(naturalLanguageQuery)`: Translates natural language to Elasticsearch query
- `searchWithNaturalLanguage(query, index, mcpService)`: Complete pipeline from natural language to results
- `getExampleQueries()`: Provides example natural language queries

### 4. ProfileSearchNlqController
REST controller exposing both MCP and natural language functionality:

**MCP Endpoints:**
- `GET /api/mcp/health`: Health check for MCP connection
- `GET /api/mcp/tools`: List available tools
- `POST /api/mcp/search?index={index}`: Execute search
- `GET /api/mcp/index/{index}/info`: Get index information
- `GET /api/mcp/indices`: List all indices
- `POST /api/mcp/execute/{toolName}`: Execute custom operations

**Natural Language Endpoints:**
- `POST /api/mcp/translate`: Translate natural language to Elasticsearch query
- `POST /api/mcp/search/natural-language?index={index}`: Execute natural language search
- `GET /api/mcp/examples`: Get example natural language queries

## Usage Examples

### MCP Operations

#### Health Check
```bash
curl -X GET http://localhost:8080/api/mcp/health
```

#### List Available Tools
```bash
curl -X GET http://localhost:8080/api/mcp/tools
```

#### Execute Direct Search
```bash
curl -X POST "http://localhost:8080/api/mcp/search?index=profiles" \
  -H "Content-Type: application/json" \
  -d '{"query": {"match_all": {}}}'
```

#### Get Index Information
```bash
curl -X GET http://localhost:8080/api/mcp/index/profiles/info
```

#### List All Indices
```bash
curl -X GET http://localhost:8080/api/mcp/indices
```

### Natural Language Operations

#### Get Example Queries
```bash
curl -X GET http://localhost:8080/api/mcp/examples
```

#### Translate Natural Language to Elasticsearch Query
```bash
curl -X POST http://localhost:8080/api/mcp/translate \
  -H "Content-Type: text/plain" \
  -d "Find all active users"
```

#### Execute Natural Language Search
```bash
curl -X POST "http://localhost:8080/api/mcp/search/natural-language?index=profiles" \
  -H "Content-Type: text/plain" \
  -d "Find users with gmail email addresses"
```

#### More Natural Language Examples
```bash
# Find users by name
curl -X POST "http://localhost:8080/api/mcp/search/natural-language?index=profiles" \
  -H "Content-Type: text/plain" \
  -d "Find users named John"

# Find users by age
curl -X POST "http://localhost:8080/api/mcp/search/natural-language?index=profiles" \
  -H "Content-Type: text/plain" \
  -d "Show users older than 30"

# Find recent users
curl -X POST "http://localhost:8080/api/mcp/search/natural-language?index=profiles" \
  -H "Content-Type: text/plain" \
  -d "Show users created in the last month"
```

## Testing

Run the unit tests:
```bash
./gradlew :dal-search:test --tests "*ElasticsearchMcpServiceTest"
```

## Troubleshooting

### Step 1: Check MCP Connection Health
```bash
curl -X GET http://localhost:8080/api/mcp/health
```

### Step 2: List Available Tools
```bash
curl -X GET http://localhost:8080/api/mcp/tools
```

### Common Issues and Solutions

1. **"tool not found" Error**:
   - First, check what tools are actually available using the `/api/mcp/tools` endpoint
   - The Elasticsearch MCP server might use different tool names than expected
   - Common tool names to try: `search`, `elasticsearch_search`, `es_search`

2. **MCP Server Connection Issues**:
   - Ensure Node.js and the Elasticsearch MCP server package are installed:
     ```bash
     npm install -g @elastic/mcp-server-elasticsearch
     ```
   - Verify Elasticsearch is running and accessible at localhost:9200
   - Check the command and arguments in the configuration

3. **Elasticsearch Connection Issues**:
   - Verify Elasticsearch URL in the MCP server configuration
   - Check Elasticsearch authentication if required
   - Test direct connection to Elasticsearch:
     ```bash
     curl -X GET "localhost:9200/_cluster/health"
     ```

4. **Process/Transport Issues**:
   - The MCP client uses stdio transport to communicate with the Node.js process
   - Check application logs for stderr messages from the MCP server process
   - Ensure the MCP server process can start successfully

### Debug Steps

1. **Test Elasticsearch MCP Server Manually**:
   ```bash
   npx @elastic/mcp-server-elasticsearch --elasticsearch-url http://localhost:9200
   ```

2. **Check Available Tools**:
   The service now automatically tries multiple tool names and logs available tools.

3. **Monitor Logs**:
   Look for these log messages:
   - "Using MCP sync client" - confirms client is configured
   - "Available tools: [...]" - shows what tools are available
   - "Trying tool name: ..." - shows which tools are being attempted

## Dependencies Added

- `spring-ai-mcp-client-spring-boot-starter:1.0.0-M6`
- `spring-ai-openai-spring-boot-starter:1.0.0-M6`
- `spring-ai-bom:1.0.0-M6` (for version management)
- Spring Milestones repository for milestone versions

## Architecture Flow

### Direct MCP Flow
```
Client Request → ProfileSearchNlqController → EsMcpService → MCP Client → Elasticsearch MCP Server → Elasticsearch
```

### Natural Language Flow
```
Natural Language Query → ProfileSearchNlqController → EsNlqService → OpenAI ChatModel → Elasticsearch Query → EsMcpService → MCP Client → Elasticsearch MCP Server → Elasticsearch
```

## Error Handling

- **MCP Connection Issues**: Graceful degradation with helpful error messages
- **OpenAI Unavailable**: Natural language endpoints return clear error messages when OpenAI is not configured
- **Tool Not Found**: Automatic retry with different tool names and detailed logging
- **Invalid Queries**: LLM response cleaning and fallback to empty queries

## Notes

- The integration uses synchronous MCP client for simplicity
- The MCP server runs as a subprocess via stdio transport
- Natural language service is conditionally loaded only when ChatModel is available
- Error handling includes proper logging and exception propagation
- All operations are exposed via REST endpoints for easy testing and integration
- The system gracefully handles missing OpenAI configuration by disabling natural language features
