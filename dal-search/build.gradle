plugins {
    id 'groovy'
    alias libs.plugins.spring.boot
    alias libs.plugins.spring.dependencies
    alias libs.plugins.gatling
    alias libs.plugins.git.properties
    alias libs.plugins.pitest
}

pitest {
    targetClasses = ['com.xm.dal.*']
    excludedClasses = ['com.xm.dal.config.*',
                       'com.xm.dal.consumer.config.*'
    ]
    threads = Runtime.runtime.availableProcessors() - 1
    outputFormats = ['XML', 'HTML']
    timestampedReports = false
    mutators = ['DEFAULTS']
    mutationThreshold = 85
    useClasspathFile = true
}

dependencyManagement {
    imports {
        mavenBom libs.spring.cloud.dependencies.get().toString()
        mavenBom "org.springframework.ai:spring-ai-bom:1.0.0-M6"
    }
}

dependencies {
    annotationProcessor 'org.projectlombok:lombok'
    compileOnly 'org.projectlombok:lombok'

    annotationProcessor libs.mapstruct.processor
    compileOnly libs.mapstruct
    annotationProcessor libs.lombok.mapstruct.binding

    developmentOnly 'org.springframework.boot:spring-boot-devtools'
    developmentOnly libs.spring.boot.docker.compose

    implementation project(':dal-domain')
    implementation libs.graphql.extendedScalars
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter-graphql'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.data:spring-data-commons'
    implementation 'org.springframework.data:spring-data-elasticsearch'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-cache'
    implementation('org.springframework.cloud:spring-cloud-starter-openfeign') {
        exclude module: "commons-fileupload"
    }

    implementation libs.guava
    implementation libs.kaffeine

    implementation 'org.apache.kafka:kafka-streams'
    implementation 'org.springframework.kafka:spring-kafka'

    implementation 'org.aspectj:aspectjweaver'

    implementation(platform(libs.aws.spring.cloud))
    implementation("software.amazon.awssdk:sts")

    implementation 'org.springframework.cloud:spring-cloud-commons'
    implementation 'org.springframework.cloud:spring-cloud-context'

    implementation libs.elasticsearch.evolution.starter
    implementation 'org.springframework.ai:spring-ai-mcp-client-spring-boot-starter'
    implementation 'org.springframework.ai:spring-ai-openai-spring-boot-starter'

    implementation libs.protobuf.util
    implementation libs.xm.contracts.common
    implementation libs.xm.contracts.jthales.common
    implementation libs.xm.contracts.jthales.profile
    implementation libs.xm.contracts.jthales.profilemetadata
    implementation libs.xm.contracts.trading.mesh.server.operation
    implementation libs.xm.contracts.loyalty.program.callback
    implementation libs.xm.contracts.kyc.validation.status

    runtimeOnly libs.logstash.logback.encoder
    runtimeOnly libs.newrelic.agent
}

bootBuildImage {
    imageName = "$dalSearchEcrRepoUrl:$version"
    bindings = ["$rootDir/config/certificates:/platform/bindings/ca-certificates"]
    environment = [
            "BP_EMBED_CERTS"              : "true",
            "BPE_DELIM_JAVA_TOOL_OPTIONS" : " ",
            "BPE_APPEND_JAVA_TOOL_OPTIONS": "-javaagent:/workspace/BOOT-INF/lib/newrelic-agent-${libs.newrelic.agent.get().version}.jar -Dnewrelic.config.file=/workspace/BOOT-INF/classes/newrelic/newrelic.yml".toString()
    ]
    docker {
        bindHostToBuilder = true
        publishRegistry {
            username = "$ecrUsername"
            password = "$ecrPassword"
            url = "$dalSearchEcrRepoUrl"
        }
    }
    network = "host"
}

task addNewrelic(type: Copy) {
    from "$rootDir/config/newrelic"
    into "$buildDir/resources/main/newrelic"
}

bootRun {
    args = [
            "--spring.profiles.active=local",
            "--spring.docker.compose.file=$rootDir/docker-compose.yaml"
    ]
}

testing {
    suites {
        test {
            useJUnitJupiter()
            dependencies {
                implementation platform(testLibs.spock.bom)
                implementation testLibs.pitest
                implementation "org.spockframework:spock-core"
                implementation "org.spockframework:spock-spring"
                implementation "org.springframework.boot:spring-boot-starter-test"
                implementation testLibs.spring.boot.graphql.test
                implementation testLibs.groovy.json
                implementation testLibs.podam
            }
        }
        integrationTest(JvmTestSuite) {
            dependencies {
                annotationProcessor libs.mapstruct.processor
                compileOnly libs.mapstruct

                implementation project()
                implementation project(':dal-domain')
                implementation platform(testLibs.spock.bom)
                implementation "org.spockframework:spock-core"
                implementation "org.spockframework:spock-spring"
                implementation testLibs.spring.boot.graphql.test
                implementation "org.springframework.boot:spring-boot-starter-test"
                implementation "org.springframework.boot:spring-boot-starter-web"
                implementation "org.springframework.boot:spring-boot-starter-webflux"
                implementation "org.springframework.data:spring-data-elasticsearch"
                implementation "org.springframework.kafka:spring-kafka"
                implementation "org.springframework.kafka:spring-kafka-test"
                implementation libs.protobuf.util
                implementation libs.xm.contracts.common
                implementation libs.xm.contracts.jthales.common
                implementation libs.xm.contracts.jthales.profile
                implementation libs.xm.contracts.jthales.profilemetadata
                implementation libs.xm.contracts.trading.mesh.server.operation
                implementation libs.xm.contracts.loyalty.program.callback
                implementation libs.xm.contracts.kyc.validation.status
                implementation libs.elasticsearch.evolution.starter
                implementation testLibs.test.containers.elasticsearch
                implementation testLibs.test.containers.kafka
                implementation testLibs.test.containers.spock
                implementation testLibs.datafaker
            }
        }
    }
}

gitProperties {
    dotGitDirectory = project.rootProject.layout.projectDirectory.dir(".git")
    keys = ['git.build.host', 'git.build.user.email', 'git.build.user.name', 'git.build.version', 'git.closest.tag.name', 'git.tags']
}

compileJava.dependsOn addNewrelic