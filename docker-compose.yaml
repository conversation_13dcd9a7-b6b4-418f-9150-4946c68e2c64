services:

  elastic:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.10.4
    container_name: elastic
    volumes:
      - elastic_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    environment:
      - xpack.security.enabled=false
      - discovery.type=single-node
    networks:
      - dal
    ulimits:
      memlock:
        soft: -1
        hard: -1

  mcp-server:
    image: docker.elastic.co/mcp/elasticsearch:latest
    container_name: mcp-server
    command: http --sse
    ports:
      - "8088:8080"
    environment:
      ES_URL: https://xm-eu-dal-dev.es.eu-west-2.aws.cloud.es.io
      ES_API_KEY: MEF4SFpvc0ItWlozc2pVSEg5UlE6cUExbmJ6SU5RTk9TRFlBc1YtcFdEQQ==
      ES_VERSION: "8"
      ES_TLS_INSECURE_SKIP_VERIFY: "true"  # if self-signed cert
    networks:
      - dal

  curl-test:
    image: curlimages/curl:latest
    entrypoint: [ "sleep", "infinity" ]
    networks:
      - dal

  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    hostname: zookeeper
    container_name: zookeeper
    ports:
      - '2181:2181'
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - dal

  kafka:
    image: confluentinc/cp-kafka:latest
    hostname: kafka
    container_name: kafka
    ports:
      - '19000:19000'
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: HOST:PLAINTEXT,DOCKER:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: HOST://localhost:19000,DOCKER://kafka:9092
      KAFKA_INTER_BROKER_LISTENER_NAME: DOCKER
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_LOG4J_ROOT_LOGLEVEL: WARN
      CONFLUENT_METRICS_ENABLE: 'false'
      JMX_PORT: 9099
    depends_on:
      - zookeeper
    networks:
      - dal

  kafka-ui:
    image: provectuslabs/kafka-ui
    hostname: kafka-ui
    container_name: kafka-ui
    ports:
      - '8082:8080'
    environment:
      KAFKA_CLUSTERS_0_NAME: jthales
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:9092
      KAFKA_CLUSTERS_0_ZOOKEEPER: zookeeper:2181
    depends_on:
      - kafka
    networks:
      - dal

volumes:
  elastic_data:

networks:
  dal:
    name: dal-network