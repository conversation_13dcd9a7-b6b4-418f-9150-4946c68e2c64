package jThalesServer.webService.brandAccount;

import jThalesServer.core.EntryPointCommonState;
import jThalesServer.core.ExecutionContext;
import jThalesServer.core.ExecutionContext.Ignored;
import jThalesServer.core.ExecutionResult;
import jThalesServer.dataAccess.brandAccount.BrandAccountDAO;
import jThalesServer.exception.DataAccessExceptionFactory;
import jThalesServer.service.brandAccount.BrandAccountFull;
import jThalesServer.service.brandAccount.BrandAccountFullV2;
import jThalesServer.service.brandAccount.BrandAccountService;
import jThalesServer.webService.brandAccount.FetchBrandAccountByEmailBrandEntryPoint.FetchBrandAccountByEmailBrandCommand;
import jThalesUtils.BrandAccountKey;
import jThalesUtils.JThalesCommandResponseModule;
import jThalesUtils.jThalesServerOutput.response.brandAccount.BrandAccountDTO;
import jThalesUtils.jThalesServerOutput.response.brandAccount.FetchBrandAccountByEmailBrandResponseV2;
import jThalesUtils.jThalesServerOutput.response.brandAccount.FetchBrandAccountByEmailBrandResponseV4;
import jThalesUtils.jThalesServerOutput.responseObjects.BrandAccountV2;
import jThalesUtils.jThalesServerOutput.responseObjects.TradingAccount;
import jThalesUtils.jThalesServerOutput.responseObjects.vulnerabilityCharacteristics.BrandAccountVulnerabilityCharacteristics;
import jThalesUtils.shared.BrandAccountId;
import jxmUtils.CommandVersion;
import jxmUtils.ConnectionWithTempTable;
import jxmUtils.CustomWebServiceExceptions.BadRequestException;
import jxmUtils.EmailBrand;
import jxmUtils.MySqlConnectionWithTempTable;
import jxmUtils.guard.DBLockGuardFactory;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import redis.clients.jedis.JedisCluster;
import testUtils.SampleObjects;

import java.io.IOException;
import java.sql.SQLException;
import java.util.List;
import java.util.Optional;
import java.util.function.Supplier;

import static org.junit.Assert.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.RETURNS_DEEP_STUBS;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.Silent.class)
public class FetchBrandAccountByEmailBrandEntryPointTest {

    private final BrandAccountId brandAccountId = BrandAccountId.create(1L);
    private final String email = "getbyemailbrand";
    private final String brandName = "xm";
    private final EmailBrand emailBrand = EmailBrand.create(email, brandName);
    private final BrandAccountKey brandAccountKey = BrandAccountKey.create(brandAccountId, emailBrand);

    @Mock
    EntryPointCommonState commonState;
    @Mock
    DBLockGuardFactory dbLockGuardFactory;
    @Mock
    BrandAccountDAO brandAccountDAO;
    @Mock
    ConnectionWithTempTable oracleConnection;
    @Mock
    MySqlConnectionWithTempTable mySQLConnection;
    @Mock
    JedisCluster jedisCluster;
    @Mock
    BrandAccountService brandAccountService;

    @Rule
    public ExpectedException thrown = ExpectedException.none();

    private FetchBrandAccountByEmailBrandEntryPoint entryPoint;
    private FetchBrandAccountByEmailBrandCommand command;

    private Supplier<AssertionError> unexpectedEmptyOptional(String element){
        return () -> new AssertionError("Expected non-null element " + element);
    }

    @Before
    public void setUpTest() throws SQLException {

        entryPoint = FetchBrandAccountByEmailBrandEntryPoint.create(commonState, brandAccountService);
        final BrandAccountV2 brandAccountV2 = Mockito.mock(BrandAccountV2.class, RETURNS_DEEP_STUBS);
        final TradingAccount tradingAccount = Mockito.mock(TradingAccount.class, RETURNS_DEEP_STUBS);
        when(brandAccountV2.getTradingAccounts().get(0)).thenReturn(tradingAccount);
        when(brandAccountV2.getTradingAccounts()).thenReturn(List.of(tradingAccount));
        when(tradingAccount.getLogin()).thenReturn(1L);
        when(commonState.getJThalesConnection()).thenReturn(oracleConnection);
        when(commonState.getMySqlClusterConnection()).thenReturn(mySQLConnection);
        when(brandAccountDAO.getByBrandAccountId(any(ConnectionWithTempTable.class), any(BrandAccountId.class))).thenReturn(Optional.of(brandAccountV2));
        when(commonState.getJedisCluster()).thenReturn(jedisCluster);
        when(jedisCluster.get(anyString())).thenReturn(null);

        command = (FetchBrandAccountByEmailBrandCommand) entryPoint.createCommand(email, brandName, CommandVersion.V1, true);
    }

    @After
    public void tearDownTest() {

        verifyNoMoreInteractions(commonState);
        verifyNoMoreInteractions(dbLockGuardFactory);
        verifyNoMoreInteractions(brandAccountDAO);
        verifyNoMoreInteractions(oracleConnection);
        verifyNoMoreInteractions(mySQLConnection);
        verifyNoMoreInteractions(brandAccountService);
    }

    @Test
    public void shouldNotDoAnythingOnPreExecute() throws SQLException, IOException {

        final ExecutionContext<Ignored> context =
            entryPoint.createCommand(email, brandName, CommandVersion.V2, true).preExecute();

        Assert.assertTrue("Should not do anything on pre-execute", context.getGuard().isEmpty());
        Assert.assertTrue("Should not do anything on pre-execute", context.getArg() == Ignored.INSTANCE);
    }

    @Test
    public void shouldForwardServiceBadRequestV1() throws IOException, SQLException {

        thrown.expect(BadRequestException.class);
        thrown.expectMessage("fail");

        when(brandAccountService.getBrandAccountKey(emailBrand)).thenThrow(BadRequestException.create("fail"));

        try {

            entryPoint.createCommand(email, brandName, CommandVersion.V1, true).execute(Ignored.INSTANCE);
            fail("Exception Expected");
        }
        finally {
            verify(brandAccountService).getBrandAccountKey(emailBrand);
        }
    }

    @Test
    public void shouldForwardServiceBadRequestV2() throws IOException, SQLException {

        thrown.expect(BadRequestException.class);
        thrown.expectMessage("fail");
        when(brandAccountService.getBrandAccountKey(emailBrand)).thenReturn(brandAccountKey);
        when(brandAccountService.getFromCache(oracleConnection, brandAccountKey, CommandVersion.V2,  true)).thenThrow(BadRequestException.create("fail"));

        try {

            entryPoint.createCommand(email, brandName, CommandVersion.V2, true).execute(Ignored.INSTANCE);
            fail("Exception Expected");
        }
        finally {
            verify(commonState).getJThalesConnection();
            verify(brandAccountService).getBrandAccountKey(emailBrand);
            verify(brandAccountService).getFromCache(oracleConnection, brandAccountKey, CommandVersion.V2, true);
            verify(oracleConnection).close();
        }
    }

    @Test
    public void shouldForwardServiceBadRequestV4() throws IOException, SQLException {

        thrown.expect(BadRequestException.class);
        thrown.expectMessage("fail");
        when(brandAccountService.getBrandAccountKeys(email)).thenThrow(BadRequestException.create("fail"));

        try {
            entryPoint.createCommand(email, "", CommandVersion.V4, true).execute(Ignored.INSTANCE);
            fail("Exception Expected");
        }
        finally {
            verify(brandAccountService).getBrandAccountKeys(email);
        }
    }

    @Test
    public void shouldForwardServiceDataAccessExceptionV1() throws SQLException, IOException {

        thrown.expect(RuntimeException.class);
        when(brandAccountService.getBrandAccountKey(emailBrand)).thenThrow(DataAccessExceptionFactory.create("fail"));

        try {
            entryPoint.createCommand(email, brandName, CommandVersion.V1, true).execute(Ignored.INSTANCE);
            fail("Exception Expected");
        }
        finally {
            verify(brandAccountService).getBrandAccountKey(emailBrand);
        }
    }

    @Test
    public void shouldReturnCorrectInfoV1() throws SQLException, IOException {

        final BrandAccountFull brandAccountFull = SampleObjects.getMockedBA();
        final String serializedBrandAccountFull = JThalesCommandResponseModule.serialize(brandAccountFull);
        final String v1Response = serializedBrandAccountFull.substring(0, serializedBrandAccountFull.length() - 1) + ",\"mAssessmentScore\":17}";
        when(brandAccountService.getBrandAccountKey(emailBrand)).thenReturn(brandAccountKey);
        when(brandAccountService.getFromDB(brandAccountKey)).thenReturn(brandAccountFull);

        final ExecutionResult res = entryPoint.createCommand(email, brandName, CommandVersion.V1, true).execute(Ignored.INSTANCE);

        verify(brandAccountService).getBrandAccountKey(emailBrand);
        verify(brandAccountService).getFromDB(brandAccountKey);

        Assert.assertEquals(res.getResponse(), v1Response);
    }

    @Test
    public void shouldReturnVulnerabilityInfoForV2WhenCachingIsEnabled() throws SQLException, IOException {
        final BrandAccountFullV2 brandAccountFullV2 = SampleObjects.getMockedBAV2();
        when(brandAccountService.getBrandAccountKey(emailBrand)).thenReturn(brandAccountKey);
        when(brandAccountService.getFromCache(oracleConnection, brandAccountKey, CommandVersion.V2 ,true)).thenReturn(brandAccountFullV2);

        final ExecutionResult res = entryPoint.createCommand(email, brandName, CommandVersion.V2, true).execute(Ignored.INSTANCE);

        verify(brandAccountService, times(1)).getBrandAccountKey(emailBrand);
        verify(brandAccountService, times(1)).getFromCache(oracleConnection, brandAccountKey, CommandVersion.V2, true);
        verify(brandAccountService, never()).getFromDBV2WithGroups(any(), any(), any(), anyBoolean());
        verify(brandAccountService, never()).getFromDB(any());
        verify(commonState, times(1)).getJThalesConnection();
        verify(oracleConnection, times(1)).close();
        BrandAccountVulnerabilityCharacteristics vulnerabilityCharacteristicsInResponse =
            JThalesCommandResponseModule.deserialize(res.getResponse(), FetchBrandAccountByEmailBrandResponseV2.class)
                                        .getBrandAccount().getVulnerabilityCharacteristics()
                                        .orElseThrow(unexpectedEmptyOptional("mVulnerabilityCharacteristics"));
        Assert.assertEquals(true, vulnerabilityCharacteristicsInResponse.getHasVulnerability().orElseThrow(unexpectedEmptyOptional("mHasVulnerability")));
        Assert.assertEquals("Detail 14", vulnerabilityCharacteristicsInResponse.getVulnerabilityDetails().orElseThrow(unexpectedEmptyOptional("mVulnerabilityDetails")));
    }

    /* The endpoint's execute() method has a special case for CommandVersion.V4 and an empty brand name,
    * wo we will test for that as well. */
    @Test
    public void shouldReturnCorrectInfoForV4AndEmptyBrandNameWithCachingEnabled() throws SQLException, IOException {
        // We will take three mocked brand accounts and a mocked list of three BrandAccountKeys and force
        // the cache call to associate every BrandAccountKey to be associated with a corresponding brand account.
        final List<BrandAccountFullV2> mockedBrandAccountsForTest = List.of(
            SampleObjects.getMockedBAV2(), SampleObjects.getMockedBAV2WithWalletAccount(), SampleObjects.getMockedWalletAccountWithNoTradingAccount());
        final List<BrandAccountKey> sampleBAKeys = SampleObjects.getSampleBrandAccountKeys();
        for(int i = 0; i < mockedBrandAccountsForTest.size(); i++){
            when(brandAccountService.getFromCache(oracleConnection, sampleBAKeys.get(i), CommandVersion.V4, true))
                .thenReturn(mockedBrandAccountsForTest.get(i));
        }
        when(brandAccountService.getBrandAccountKeys(email)).thenReturn(sampleBAKeys);

        final ExecutionResult res = entryPoint.createCommand(email, "", CommandVersion.V4, true).execute(Ignored.INSTANCE);

        verify(brandAccountService, never()).getFromDBV2WithGroups(any(), any(), any(), anyBoolean());
        verify(brandAccountService, never()).getFromDB(any());
        verify(brandAccountService, times(1)).getBrandAccountKeys(email);
        verify(oracleConnection, times(3)).close();
        verify(commonState, times(3)).getJThalesConnection();

        // Final assertions
        final FetchBrandAccountByEmailBrandResponseV4 deserializedResponse = JThalesCommandResponseModule.deserialize(res.getResponse(), FetchBrandAccountByEmailBrandResponseV4.class);
        List<Optional<BrandAccountVulnerabilityCharacteristics>> vulnerabilityCharacteristicsInResponse = deserializedResponse.getAccounts().stream()
                                                                .map(FetchBrandAccountByEmailBrandResponseV2::getBrandAccount).map(BrandAccountDTO::getVulnerabilityCharacteristics).toList();
        for(int i = 0; i < mockedBrandAccountsForTest.size(); i++){
            verify(brandAccountService, times(1)).getFromCache(oracleConnection,
                                                               sampleBAKeys.get(i), CommandVersion.V4, true);
            // Asserting the equality between optionals is fine and saves us one call to get()
            Assert.assertEquals(mockedBrandAccountsForTest.get(i).getBrandAccount().getVulnerabilityCharacteristics().get().getHasVulnerability(),
                vulnerabilityCharacteristicsInResponse.get(i).get().getHasVulnerability());
            Assert.assertEquals(mockedBrandAccountsForTest.get(i).getBrandAccount().getVulnerabilityCharacteristics().get().getVulnerabilityDetails(),
                                vulnerabilityCharacteristicsInResponse.get(i).get().getVulnerabilityDetails());
        }


    }

    @Test
    public void shouldReturnExpectedJSON() throws SQLException, IOException {

        final BrandAccountFullV2 brandAccountFull = SampleObjects.getMockedBAV2WithWalletAccount();
        when(brandAccountService.getBrandAccountKey(emailBrand)).thenReturn(brandAccountKey);
        when(brandAccountService.getFromCache(oracleConnection, brandAccountKey, CommandVersion.V2, true)).thenReturn(brandAccountFull);

        final ExecutionResult res = entryPoint.createCommand(email, brandName, CommandVersion.V2, true).execute(Ignored.INSTANCE);

        verify(commonState).getJThalesConnection();
        verify(brandAccountService).getBrandAccountKey(emailBrand);
        verify(brandAccountService).getFromCache(oracleConnection, brandAccountKey, CommandVersion.V2, true);
        verify(oracleConnection).close();
        Assert.assertEquals(V2_RESPONSE, res.getResponse());
    }

    @Test
    public void shouldReturnExpectedJSONV4() throws SQLException, IOException {

        final BrandAccountFullV2 brandAccountFull = SampleObjects.getMockedBAV2WithWalletAccount();
        when(brandAccountService.getBrandAccountKeys(email)).thenReturn(List.of(brandAccountKey, brandAccountKey));
        when(brandAccountService.getFromCache(oracleConnection, brandAccountKey, CommandVersion.V4, true)).thenReturn(brandAccountFull);

        final ExecutionResult res = entryPoint.createCommand(email, "", CommandVersion.V4, true)
                .execute(Ignored.INSTANCE);

        verify(commonState, times(2)).getJThalesConnection();
        verify(brandAccountService).getBrandAccountKeys(email);
        verify(brandAccountService, times(2)).getFromCache(oracleConnection, brandAccountKey, CommandVersion.V4, true);
        verify(oracleConnection, times(2)).close();
        Assert.assertEquals("{\"mAccounts\":[" + String.join(",", V2_RESPONSE, V2_RESPONSE) + "]}", res.getResponse());
    }

    public static final String V2_RESPONSE = """ 
            {\"mBrandAccount\":{\"mBrandAccountId\":2798993,\"mBrand\":{\"mBrandId\":0,\"mBrandName\":\"XM\"},\"mPreferredLanguage\":{\"value\":{\"mLanguageIso639_1\":\"el\",\"mLanguageName\":\"Greek\"}},\"mRegistrationDate\":{\"date\":{\"year\":2017,\"month\":1,\"day\":1},\"time\":{\"hour\":0,\"minute\":0,\"second\":8,\"nano\":0}},\"mIsRejectedExperience\":true,\"mDateOfBirth\":{\"year\":2017,\"month\":1,\"day\":1},\"mPassportId\":{\"value\":\"passportId\"},\"mIsValidated\":true,\"mRiskStatus\":{\"value\":{\"mRiskStatusId\":1,\"mRiskStatusDescription\":\"RE-HR\"}},\"mTaxIds\":[{\"mTaxEntryId\":0,\"mTaxIdentificationNumber\":{\"value\":\"taxCY\"},\"mTaxCountry\":{\"mCountryCodeIso2\":\"CY\",\"mCountryName\":\"Cyprus\"},\"mTaxType\":{\"mId\":1,\"mDescription\":\"DEFAULT\"}},{\"mTaxEntryId\":1,\"mTaxIdentificationNumber\":{\"value\":\"taxUS\"},\"mTaxCountry\":{\"mCountryCodeIso2\":\"US\",\"mCountryName\":\"United States\"},\"mTaxType\":{\"mId\":2,\"mDescription\":\"CPF\"}}],\"mSocialMediaAccounts\":[{\"mSocialMediaType\":{\"mSocialMediaTypeId\":1,\"mSocialMediaTypeDescription\":\"Twitter\"},\"mSocialMediaUsername\":\"socialMediaAccount\"}],\"mAddress\":{\"mAddressFull\":\"streetName, streetNumber\",\"mBuildingName\":{\"value\":\"buildingName\"},\"mBuildingNumber\":{\"value\":\"buildingNumber\"},\"mStreetName\":{\"value\":\"streetName\"},\"mStreetNumber\":{\"value\":\"streetNumber\"},\"mProvince\":{\"value\":\"province\"},\"mPoBox\":{\"value\":\"poBox\"},\"mCity\":\"nicosia\",\"mState\":{\"value\":\"state\"},\"mZipCode\":{\"value\":\"zipCode\"},\"mCountry\":{\"mCountryCodeIso2\":\"AU\",\"mCountryName\":\"Australia\"},\"mStreetType\":{\"value\":{\"mStreetTypeAbbreviation\":\"ACCS\",\"mStreetType\":\"Access\"}}},\"mNames\":{\"mTitle\":{\"value\":\"Mr\"},\"mFirstName\":\"firstName\",\"mLastName\":\"lastName\",\"mNativeFirstName\":{\"value\":\"nativeFirstName\"},\"mNativeLastName\":{\"value\":\"nativeLastName\"},\"mMiddleName\":{\"value\":\"middleName\"}},\"mPhones\":[{\"mPhoneNumber\":\"3_0123456\",\"mPrefix\":{\"value\":null},\"mRegionCode\":{\"value\":null},\"mLocal\":{\"value\":null},\"mInternational\":{\"value\":null},\"mPriorityOrder\":{\"value\":1},\"mIsVerified\":{\"value\":true}},{\"mPhoneNumber\":\"1_01234567\",\"mPrefix\":{\"value\":null},\"mRegionCode\":{\"value\":null},\"mLocal\":{\"value\":null},\"mInternational\":{\"value\":null},\"mPriorityOrder\":{\"value\":2},\"mIsVerified\":{\"value\":true}}],\"mEmail\":{\"mEmail\":\"<EMAIL>\",\"mIsVerified\":true},\"mConfirmations\":{\"mAgreedPromo\":{\"value\":true},\"mAgreedAcknowledgement\":{\"value\":true},\"mIsOpeAgreementOptIn\":null,\"mAwareForexComplexity\":0},\"mInvestorInfo\":{\"value\":{\"mIncomeOption\":{\"value\":{\"mEstimatedIncomeOptionId\":0,\"mEstimatedIncomeOptionDescription\":\"More than 100,000\",\"mRFCode\":\"\"}},\"mNetWorthOption\":{\"value\":{\"mNetWorthOptionId\":0,\"mNetWorthOptionDescription\":\"More than 1,000,000\",\"mRFCode\":\"\"}},\"mEducationLevelOption\":{\"value\":{\"mEducationLevelOptionId\":0,\"mEducationLevelOptionDescription\":\"Secondary\",\"mRFCode\":\"\"}},\"mEmploymentOption\":{\"value\":{\"mEmploymentOptionId\":0,\"mEmploymentOptionDescription\":\"Employed\"}},\"mTransactionPurposeOption\":{\"value\":{\"mTransactionPurposeOptionId\":0,\"mTransactionPurposeOptionDescription\":\"Speculative\"}},\"mNatureOfBusinessOption\":{\"value\":{\"mNatureOfBusinessOptionId\":0,\"mNatureOfBusinessOptionDescription\":\"Business to business\"}},\"mSourceOfFundsOption\":{\"value\":{\"mSourceOfFundsOptionId\":0,\"mSourceOfFundsOptionDescription\":\"Savings\"}},\"mInvestmentAmountOption\":{\"value\":{\"mInvestmentAmountOptionId\":0,\"mInvestmentAmountOptionDescription\":\"5 - 20,000\",\"mRFCode\":\"\"}},\"sourceOfFundsAdditionalInfo\":null}},\"mInvestExperience\":{\"value\":{\"mInvestDerive\":{\"value\":true},\"mInvestDeriveTradesFrequency\":{\"value\":{\"mTradesFrequencyOptionId\":1,\"mTradesFrequencyOptionDescription\":\"Daily\"}},\"mInvestDeriveTradesNo\":{\"value\":{\"mTradesNumberOptionId\":1,\"mTradesNumberOptionDescription\":\"0-10\"}},\"mInvestDeriveYears\":{\"value\":{\"mTradesYearsOptionId\":1,\"mTradesYearOptionDescription\":\"LT 1\"}},\"mInvestEquities\":{\"value\":true},\"mInvestEquitiesTradesFrequency\":{\"value\":{\"mTradesFrequencyOptionId\":2,\"mTradesFrequencyOptionDescription\":\"Weekly\"}},\"mInvestEquitiesTradesNo\":{\"value\":{\"mTradesNumberOptionId\":2,\"mTradesNumberOptionDescription\":\"10-30\"}},\"mInvestEquitiesYears\":{\"value\":{\"mTradesYearsOptionId\":2,\"mTradesYearOptionDescription\":\"1-2\"}},\"mInvestForex\":{\"value\":true},\"mInvestForexTradesFrequency\":{\"value\":{\"mTradesFrequencyOptionId\":3,\"mTradesFrequencyOptionDescription\":\"Monthly\"}},\"mInvestForexTradesNo\":{\"value\":{\"mTradesNumberOptionId\":3,\"mTradesNumberOptionDescription\":\"30-50\"}},\"mInvestForexYears\":{\"value\":{\"mTradesYearsOptionId\":3,\"mTradesYearOptionDescription\":\"2-5\"}},\"mInvestKnowledge\":{\"value\":true},\"mInvestRisk\":{\"value\":true},\"mTradingFrequency\":{\"value\":{\"mTradingFrequencyOptionId\":0,\"mTradingFrequencyOptionDescription\":\"Rarely\"}},\"mVolumeTraded\":{\"value\":{\"mVolumeTradedOptionId\":0,\"mVolumeTradedOptionDescription\":\"Less than 50 Lots\"}}}},\"mTradingKnowledge\":{\"value\":{\"mProfessionalExperienceLevel\":{\"value\":{\"mProfessionalExpLevelOptionId\":0,\"mProfessionalExpLevelOptionDescription\":\"Recent work experience in a financial institution\"}},\"mTradedDerivativesTimes\":{\"value\":{\"mTradedTimesOptionId\":1,\"mTradedTimesOptionDescription\":\"10 to 25 times\"}},\"mTradedSharesTimes\":{\"value\":{\"mTradedTimesOptionId\":0,\"mTradedTimesOptionDescription\":\"More than 25 times\"}},\"mTradedOTCTimes\":{\"value\":{\"mTradedTimesOptionId\":2,\"mTradedTimesOptionDescription\":\"Less than 10 times\"}},\"mUnderstandRisk\":{\"value\":true},\"mIsExperiencedTrader\":{\"value\":true}}},\"mBrandAccountTransfer\":{\"value\":{\"mTransferredEmail\":\"transferredEmail\"}},\"mNationalClientIdentifier\":{\"value\":{\"mNationality\":{\"value\":\"CY\"},\"mNCINumber\":{\"value\":\"nciNumber\"},\"mNCIType\":{\"value\":\"1\"},\"mNCIPriority\":{\"value\":1},\"mNCIUpdatedDate\":{\"value\":{\"date\":{\"year\":2017,\"month\":1,\"day\":1},\"time\":{\"hour\":0,\"minute\":0,\"second\":11,\"nano\":0}}}}},\"mLegalEntityIdentifier\":{\"value\":{\"mLEINumber\":\"leiNumber\"}},\"mHasTaxUS\":{\"value\":true},\"mHasTaxInAnotherCountry\":{\"value\":true},\"mNoTaxIdReason\":{\"value\":null},\"mAppropriateness\":{\"value\":{\"mAnnualDisposableIncomeOption\":{\"value\":{\"mAnnualDisposableIncomeOptId\":0,\"mAnnualDisposableIncomeOptDesc\":\"Not submitted\"}},\"mMoneyRiskLosingOption\":{\"value\":{\"mMoneyRiskLosingOptId\":1,\"mMoneyRiskLosingOptDesc\":\"More than 80%. I am a risk taker\"}},\"mDescribeInvestmentObjectiveOption\":{\"value\":{\"mDescribeInvestmentObjectiveOptId\":2,\"mDescribeInvestmentObjectiveOptDesc\":\"Hedging\"}},\"mRequiredMarginOneLotOption\":{\"value\":{\"mRequiredMarginOneLotOptId\":0,\"mRequiredMarginOneLotOptDesc\":\"Not submitted\"}},\"mLargestPotentialProfitLossOption\":{\"value\":{\"mLargestPotentialProfitLossOptId\":1,\"mLargestPotentialProfitLossOptDesc\":\"1:5\"}},\"mClosingOrderLimitLossesOption\":{\"value\":{\"mClosingOrderLimitLossesOptId\":2,\"mClosingOrderLimitLossesOptDesc\":\"A \\\"stop loss\\\" order\"}},\"mTransactionsPastThreeYearsOption\":{\"value\":{\"mTransactionsPastThreeYearsOptId\":0,\"mTransactionsPastThreeYearsOptDesc\":\"Not submitted\"}},\"mAverageAmountInvestedAnnuallyOption\":{\"value\":{\"mAverageAmountInvestedAnnuallyOptId\":1,\"mAverageAmountInvestedAnnuallyOptDesc\":\"More than $5,000\"}},\"mExperienceServicesProductOption\":{\"value\":{\"mExperienceServicesProductOptId\":2,\"mExperienceServicesProductOptDesc\":\"I have at least one year of experience in trading in the above products\"}},\"mMoneySavingOption\":{\"value\":{\"mMoneySavingOptId\":1,\"mMoneySavingOptDesc\":\"More than 50%\"}},\"mRisksOfLeverageTradingOption\":{\"value\":{\"mRisksOfLeverageTradingOptId\":1,\"mRisksOfLeverageTradingOptDesc\":\"It could magnify my profits and losses\"}},\"mPriceMovementsEffectsOption\":{\"value\":{\"mPriceMovementsEffectsOptId\":1,\"mPriceMovementsEffectsOptDesc\":\"When the market is moving against me and I don’t have enough equity to meet the margin requirement\"}},\"mRiskInCfdOption\":{\"value\":{\"mRiskInCfdOptId\":2,\"mRiskInCfdOptDesc\":\"Most of it.I’m happy to accept considerable risk\"}},\"mCfdTradingOption\":{\"value\":{\"mCfdTradingOptId\":3,\"mCfdTradingOptDesc\":\"To be able to profit from both rising and falling markets\"}},\"mOrderPlacingOption\":{\"value\":{\"mOrderPlacingOptId\":3,\"mOrderPlacingOptDesc\":\"There’s no such order\"}},\"mPreviousTradingExperienceOption\":{\"value\":{\"mPreviousTradingExperienceOptId\":4,\"mPreviousTradingExperienceOptDesc\":\"Never\"}},\"mTradingEducationOption\":{\"value\":{\"mTradingEducationOptId\":6,\"mTradingEducationOptDesc\":\"None\"}},\"mAnnualSaveOption\":{\"value\":{\"mAnnualSaveOptId\":5,\"mAnnualSaveOptDesc\":\"I don’t save any of it\"}},\"mCFDSellValueOption\":{\"value\":{\"mId\":1,\"mDescription\":\"decreases\"}}}},\"mProfessionalClient\":{\"value\":{\"mIsProfessionalClient\":{\"value\":true},\"mHasTradedTenTimes\":{\"value\":true},\"mHoldsInvestmentPortfolio\":{\"value\":true},\"mHasWorkedFinancialSector\":{\"value\":true},\"mSignificantlySizedTradesOption\":{\"value\":{\"mSignificantlySizedTradeId\":1,\"mSignificantlySizedTradeDescription\":\"0 - 9\"}},\"mSavingsInvestmentsAmountOption\":{\"value\":{\"mSavingsInvestmentsAmountTypeId\":2,\"mSavingsInvestmentsAmountTypeDescription\":\"500K - 1M\"}},\"mMostRelevantEmployerName\":{\"value\":\"employer name\"},\"mIsMostRelevantEmployerRegulated\":{\"value\":true},\"mMostRelevantEmployerRoleOption\":{\"value\":{\"mMostRelevantEmployerRoleTypeId\":3,\"mMostRelevantEmployerRoleTypeDescription\":\"Analyst\"}},\"mMostRelevantEmployerRoleOther\":{\"value\":\"employer role other\"},\"mHasAcceptedTreadedAsProfessionalClient\":{\"value\":true},\"mHasAcceptedMayLooseProtections\":{\"value\":true},\"mApplicationStatusOption\":{\"value\":{\"mApplicationStatusId\":1,\"mApplicationStatusDescription\":\"Pending\"}}}},\"mEconomicProfile\":{\"value\":{\"mPurposeOfAccountOpeningOption\":{\"value\":[{\"mPurposeOfAccountOpeningOptionId\":0,\"mPurposeOfAccountOpeningOptionDescription\":\"Trade in Forex CFDs\"},{\"mPurposeOfAccountOpeningOptionId\":1,\"mPurposeOfAccountOpeningOptionDescription\":\"Trade in Shares CFDs\"}]},\"mOriginOfIncomingFundsOption\":{\"value\":[{\"mOriginOfIncomingFundsOptionId\":0,\"mOriginOfIncomingFundsDescription\":\"Credit / Debit Cards\"},{\"mOriginOfIncomingFundsOptionId\":1,\"mOriginOfIncomingFundsDescription\":\"Electronic Payment / E-Wallet\"}]},\"mCountrySourceOfFunds\":{\"value\":[{\"mCountryCodeIso2\":\"AD\",\"mCountryName\":\"Andorra\"},{\"mCountryCodeIso2\":\"CY\",\"mCountryName\":\"Cyprus\"}]},\"mCountryDestinationOfFunds\":{\"value\":[{\"mCountryCodeIso2\":\"AD\",\"mCountryName\":\"Andorra\"},{\"mCountryCodeIso2\":\"CY\",\"mCountryName\":\"Cyprus\"}]},\"mEmploymentPositionOption\":{\"value\":{\"mEmploymentPositionOptionId\":0,\"mEmploymentPositionDescription\":\"Owner\"}},\"mEmploymentPositionOther\":{\"value\":\"Other\"},\"mSourceOfWealthOption\":{\"value\":{\"mSourceOfWealthOptionId\":0,\"mSourceOfWealthDescription\":\"Employment / Savings from salary\"}},\"mSourceOfWealthOther\":{\"value\":\"Other\"},\"mPoliticallyExposedPerson\":{\"value\":true},\"mPoliticallyExposedPersonFamily\":{\"value\":true},\"mCurrentJobDescription\":{\"value\":\"currentJobDescription\"},\"mCurrentNameOfEmployer\":{\"value\":\"currentNameOfEmployer\"},\"mCurrentEmploymentStartedYear\":{\"value\":2019},\"mCurrentEmploymentUpToYear\":{\"value\":2020},\"mCurrentEmploymentStartedMonth\":{\"value\":2},\"mCurrentEmploymentUpToMonth\":{\"value\":4},\"mPastJobDescription\":{\"value\":\"pastJobDescription\"},\"mPastNameOfEmployer\":{\"value\":\"pastNameOfEmployer\"},\"mPastEmploymentStartedYear\":{\"value\":2018},\"mPastEmploymentUpToYear\":{\"value\":2019},\"mPastEmploymentUpToMonth\":{\"value\":2},\"mPastEmploymentStartedMonth\":{\"value\":1},\"mTraveledRelocatedPastYears\":{\"value\":true},\"mTraveledRelocatedPastYearsClarification\":{\"value\":\"Travel in another country\"},\"mSourceOfWealthAdditionalInfo\":null,\"mPoliticallyExposedPersonOrFamily\":{\"value\":false},\"mPoliticallyExposedPersonOrFamilyInfo\":{\"value\":null}}},\"mLastUpdateDate\":{\"date\":{\"year\":2020,\"month\":3,\"day\":9},\"time\":{\"hour\":13,\"minute\":32,\"second\":56,\"nano\":*********}},\"mClosedDate\":{\"value\":{\"dateTime\":{\"date\":{\"year\":2011,\"month\":12,\"day\":3},\"time\":{\"hour\":10,\"minute\":15,\"second\":30,\"nano\":0}},\"offset\":{\"totalSeconds\":3600},\"zone\":{\"id\":\"+01:00\"}}},\"mIsAffiliate\":false,\"mPlaceOfBirth\":{\"value\":{\"mCountryCodeIso2\":\"CY\",\"mCountryName\":\"Cyprus\"}},\"mRegimeAcceptance\":{\"value\":{\"mAcceptRegime\":false,\"mAcceptRegimeDate\":{\"date\":{\"year\":2020,\"month\":3,\"day\":9},\"time\":{\"hour\":15,\"minute\":32,\"second\":59,\"nano\":*********}},\"mAuditLog\":null}},\"mXmProfileId\":{\"value\":12345},\"mRegistrationStatus\":\"COMPLETED\",\"mVulnerabilityCharacteristics\":{\"value\":{\"mHasVulnerability\":{\"value\":true},\"mVulnerabilityDetails\":{\"value\":\"Detail 15\"}}},\"moCommentReason\":{\"value\":\"Some reason\"},\"mXmProfileAffiliateId\":{\"value\":1},\"mPorState\":{\"value\":null}},\"mTradingAccounts\":[{\"mAccountName\":\"Nickname\",\"mLogin\":2000007,\"mServerId\":2,\"mServerInformation\":{\"mServerId\":2,\"mPlatformType\":\"MT4\",\"mServerLoginName\":\"XM.COM-Real 2\"},\"mCurrency\":\"EUR\",\"mRegistrationDate\":{\"date\":{\"year\":2017,\"month\":1,\"day\":1},\"time\":{\"hour\":0,\"minute\":0,\"second\":10,\"nano\":0}},\"mRegistrationIp\":\"registrationIp\",\"mRegistrationIpCountry\":{\"value\":{\"mCountryCodeIso2\":\"CY\",\"mCountryName\":\"Cyprus\"}},\"mGid\":{\"value\":0},\"mGwClickDate\":{\"value\":{\"date\":{\"year\":2017,\"month\":1,\"day\":1},\"time\":{\"hour\":0,\"minute\":0,\"second\":11,\"nano\":0}}},\"mSource\":{\"value\":\"EL-Website\"},\"mGroup\":\"EUR-2NR-XA0-2\",\"mAccountType\":{\"mAccountTypeId\":0,\"mAccountTypeDescription\":\"Standard\"},\"mClientType\":{\"mClientTypeId\":0,\"mClientTypeDescription\":\"Personal\"},\"mIsArchived\":false,\"mArchiveDate\":{\"value\":null},\"mClosedDate\":{\"value\":null},\"mIsIslamic\":true,\"mIsEnabled\":true,\"mIsReadOnly\":true,\"mIsSendReports\":true,\"mIsAgreeTermsBonus\":{\"value\":true},\"mAutoRebate\":{\"value\":{\"mIsEligible\":true,\"mHasAccepted\":{\"value\":true},\"mAcceptedDate\":{\"value\":{\"date\":{\"year\":2017,\"month\":1,\"day\":1},\"time\":{\"hour\":0,\"minute\":0,\"second\":11,\"nano\":0}}}}},\"mTradingAccountTransfer\":{\"value\":{\"mTransferBetweenBrands\":1,\"mTransferDate\":{\"value\":{\"date\":{\"year\":2017,\"month\":1,\"day\":1},\"time\":{\"hour\":0,\"minute\":0,\"second\":9,\"nano\":0}}}}},\"mLeverage\":100,\"mAccountStatus\":\"Active\",\"mComment\":{\"value\":\"comment\"},\"mAffiliateId\":{\"value\":null},\"mLeverageBlock\":true,\"mSocialTradingStatus\":{\"value\":\"MASTER\"}}],\"mWalletAccounts\":[{\"mWalletAccountId\":78577,\"mWalletBrand\":{\"mBrandId\":0,\"mBrandName\":\"XM\"},\"mLogin\":4357098,\"mServerId\":50,\"mCurrency\":\"USD\",\"mRegistrationDate\":{\"date\":{\"year\":2017,\"month\":11,\"day\":26},\"time\":{\"hour\":11,\"minute\":41,\"second\":17,\"nano\":********}},\"mRegistrationIp\":\"registrationIp\",\"mRegistrationIpCountry\":{\"value\":null},\"mSource\":{\"value\":null},\"mGroup\":\"Real50\\\\xm\\\\NoTrade\\\\USD-8NR-XA0-2\",\"mAccountType\":{\"mAccountTypeId\":4,\"mAccountTypeDescription\":\"AffPayment\"},\"mClientType\":{\"mClientTypeId\":2,\"mClientTypeDescription\":\"Wallet\"},\"mIsEnabled\":true,\"mIsReadOnly\":false,\"mSendReports\":true,\"mAgreedTermsBonus\":true,\"mAgreedPromo\":true,\"mLeverage\":1,\"mAffiliateId\":1015,\"mPlatformComment\":{\"value\":\"comment\"},\"mIsArchived\":false,\"mAccountStatus\":\"Active\",\"mArchiveDate\":{\"value\":{\"date\":{\"year\":2017,\"month\":11,\"day\":26},\"time\":{\"hour\":11,\"minute\":41,\"second\":17,\"nano\":********}}},\"mServerName\":\"real\",\"mServerType\":\"real\"}],\"mMinimumRequirements\":{\"value\":null},\"mRestrictedExperience\":{\"value\":null},\"mValidationFlow\":{\"mValidationFlowStatus\":0,\"mIslamicState\":0,\"mIsDefaultResponse\":true,\"mValidationFlowsData\":[{\"mId\":0,\"mDescription\":\"Test\",\"mStatus\":0,\"mData\":[{\"mType\":0,\"mStatus\":1,\"mError\":{\"mCode\":0,\"mErrorMessage\":\"Test message\"}}]}],\"mNextFlowId\":0},\"mComments\":[\"NOSF\"],\"mW8BENStatus\":\"NOT_SIGNED\",\"mCitizenship\":{\"value\":\"UK\"},\"mAssessmentScore\":17,\"mBrandChangeInfo\":{\"value\":{\"mStatus\":\"PENDING\",\"mSourceBrand\":\"XMBZ\",\"mDestinationBrand\":\"XMMB\",\"mUpdateDate\":{\"date\":{\"year\":2024,\"month\":5,\"day\":26},\"time\":{\"hour\":12,\"minute\":0,\"second\":0,\"nano\":0}}}}}""";

    public static final String V2_RESPONSE_BY_LOGIN = """
            {\"mBrandAccount\":{\"mBrandAccountId\":2798993,\"mBrand\":{\"mBrandId\":0,\"mBrandName\":\"XM\"},\"mPreferredLanguage\":{\"value\":{\"mLanguageIso639_1\":\"el\",\"mLanguageName\":\"Greek\"}},\"mRegistrationDate\":{\"date\":{\"year\":2017,\"month\":1,\"day\":1},\"time\":{\"hour\":0,\"minute\":0,\"second\":8,\"nano\":0}},\"mIsRejectedExperience\":true,\"mDateOfBirth\":{\"year\":2017,\"month\":1,\"day\":1},\"mPassportId\":{\"value\":\"passportId\"},\"mIsValidated\":true,\"mRiskStatus\":{\"value\":{\"mRiskStatusId\":1,\"mRiskStatusDescription\":\"RE-HR\"}},\"mTaxIds\":[{\"mTaxEntryId\":0,\"mTaxIdentificationNumber\":{\"value\":\"taxCY\"},\"mTaxCountry\":{\"mCountryCodeIso2\":\"CY\",\"mCountryName\":\"Cyprus\"},\"mTaxType\":{\"mId\":1,\"mDescription\":\"DEFAULT\"}},{\"mTaxEntryId\":1,\"mTaxIdentificationNumber\":{\"value\":\"taxUS\"},\"mTaxCountry\":{\"mCountryCodeIso2\":\"US\",\"mCountryName\":\"United States\"},\"mTaxType\":{\"mId\":2,\"mDescription\":\"CPF\"}}],\"mSocialMediaAccounts\":[{\"mSocialMediaType\":{\"mSocialMediaTypeId\":1,\"mSocialMediaTypeDescription\":\"Twitter\"},\"mSocialMediaUsername\":\"socialMediaAccount\"}],\"mAddress\":{\"mAddressFull\":\"streetName, streetNumber\",\"mBuildingName\":{\"value\":\"buildingName\"},\"mBuildingNumber\":{\"value\":\"buildingNumber\"},\"mStreetName\":{\"value\":\"streetName\"},\"mStreetNumber\":{\"value\":\"streetNumber\"},\"mProvince\":{\"value\":\"province\"},\"mPoBox\":{\"value\":\"poBox\"},\"mCity\":\"nicosia\",\"mState\":{\"value\":\"state\"},\"mZipCode\":{\"value\":\"zipCode\"},\"mCountry\":{\"mCountryCodeIso2\":\"AU\",\"mCountryName\":\"Australia\"},\"mStreetType\":{\"value\":{\"mStreetTypeAbbreviation\":\"ACCS\",\"mStreetType\":\"Access\"}}},\"mNames\":{\"mTitle\":{\"value\":\"Mr\"},\"mFirstName\":\"firstName\",\"mLastName\":\"lastName\",\"mNativeFirstName\":{\"value\":\"nativeFirstName\"},\"mNativeLastName\":{\"value\":\"nativeLastName\"},\"mMiddleName\":{\"value\":\"middleName\"}},\"mPhones\":[{\"mPhoneNumber\":\"3_0123456\",\"mPrefix\":{\"value\":null},\"mRegionCode\":{\"value\":null},\"mLocal\":{\"value\":null},\"mInternational\":{\"value\":null},\"mPriorityOrder\":{\"value\":1},\"mIsVerified\":{\"value\":true}},{\"mPhoneNumber\":\"1_01234567\",\"mPrefix\":{\"value\":null},\"mRegionCode\":{\"value\":null},\"mLocal\":{\"value\":null},\"mInternational\":{\"value\":null},\"mPriorityOrder\":{\"value\":2},\"mIsVerified\":{\"value\":true}}],\"mEmail\":{\"mEmail\":\"<EMAIL>\",\"mIsVerified\":true},\"mConfirmations\":{\"mAgreedPromo\":{\"value\":true},\"mAgreedAcknowledgement\":{\"value\":true},\"mIsOpeAgreementOptIn\":null,\"mAwareForexComplexity\":0},\"mInvestorInfo\":{\"value\":{\"mIncomeOption\":{\"value\":{\"mEstimatedIncomeOptionId\":0,\"mEstimatedIncomeOptionDescription\":\"More than 100,000\",\"mRFCode\":\"\"}},\"mNetWorthOption\":{\"value\":{\"mNetWorthOptionId\":0,\"mNetWorthOptionDescription\":\"More than 1,000,000\",\"mRFCode\":\"\"}},\"mEducationLevelOption\":{\"value\":{\"mEducationLevelOptionId\":0,\"mEducationLevelOptionDescription\":\"Secondary\",\"mRFCode\":\"\"}},\"mEmploymentOption\":{\"value\":{\"mEmploymentOptionId\":0,\"mEmploymentOptionDescription\":\"Employed\"}},\"mTransactionPurposeOption\":{\"value\":{\"mTransactionPurposeOptionId\":0,\"mTransactionPurposeOptionDescription\":\"Speculative\"}},\"mNatureOfBusinessOption\":{\"value\":{\"mNatureOfBusinessOptionId\":0,\"mNatureOfBusinessOptionDescription\":\"Business to business\"}},\"mSourceOfFundsOption\":{\"value\":{\"mSourceOfFundsOptionId\":0,\"mSourceOfFundsOptionDescription\":\"Savings\"}},\"mInvestmentAmountOption\":{\"value\":{\"mInvestmentAmountOptionId\":0,\"mInvestmentAmountOptionDescription\":\"5 - 20,000\",\"mRFCode\":\"\"}},\"sourceOfFundsAdditionalInfo\":null}},\"mInvestExperience\":{\"value\":{\"mInvestDerive\":{\"value\":true},\"mInvestDeriveTradesFrequency\":{\"value\":{\"mTradesFrequencyOptionId\":1,\"mTradesFrequencyOptionDescription\":\"Daily\"}},\"mInvestDeriveTradesNo\":{\"value\":{\"mTradesNumberOptionId\":1,\"mTradesNumberOptionDescription\":\"0-10\"}},\"mInvestDeriveYears\":{\"value\":{\"mTradesYearsOptionId\":1,\"mTradesYearOptionDescription\":\"LT 1\"}},\"mInvestEquities\":{\"value\":true},\"mInvestEquitiesTradesFrequency\":{\"value\":{\"mTradesFrequencyOptionId\":2,\"mTradesFrequencyOptionDescription\":\"Weekly\"}},\"mInvestEquitiesTradesNo\":{\"value\":{\"mTradesNumberOptionId\":2,\"mTradesNumberOptionDescription\":\"10-30\"}},\"mInvestEquitiesYears\":{\"value\":{\"mTradesYearsOptionId\":2,\"mTradesYearOptionDescription\":\"1-2\"}},\"mInvestForex\":{\"value\":true},\"mInvestForexTradesFrequency\":{\"value\":{\"mTradesFrequencyOptionId\":3,\"mTradesFrequencyOptionDescription\":\"Monthly\"}},\"mInvestForexTradesNo\":{\"value\":{\"mTradesNumberOptionId\":3,\"mTradesNumberOptionDescription\":\"30-50\"}},\"mInvestForexYears\":{\"value\":{\"mTradesYearsOptionId\":3,\"mTradesYearOptionDescription\":\"2-5\"}},\"mInvestKnowledge\":{\"value\":true},\"mInvestRisk\":{\"value\":true},\"mTradingFrequency\":{\"value\":{\"mTradingFrequencyOptionId\":0,\"mTradingFrequencyOptionDescription\":\"Rarely\"}},\"mVolumeTraded\":{\"value\":{\"mVolumeTradedOptionId\":0,\"mVolumeTradedOptionDescription\":\"Less than 50 Lots\"}}}},\"mTradingKnowledge\":{\"value\":{\"mProfessionalExperienceLevel\":{\"value\":{\"mProfessionalExpLevelOptionId\":0,\"mProfessionalExpLevelOptionDescription\":\"Recent work experience in a financial institution\"}},\"mTradedDerivativesTimes\":{\"value\":{\"mTradedTimesOptionId\":1,\"mTradedTimesOptionDescription\":\"10 to 25 times\"}},\"mTradedSharesTimes\":{\"value\":{\"mTradedTimesOptionId\":0,\"mTradedTimesOptionDescription\":\"More than 25 times\"}},\"mTradedOTCTimes\":{\"value\":{\"mTradedTimesOptionId\":2,\"mTradedTimesOptionDescription\":\"Less than 10 times\"}},\"mUnderstandRisk\":{\"value\":true},\"mIsExperiencedTrader\":{\"value\":true}}},\"mBrandAccountTransfer\":{\"value\":{\"mTransferredEmail\":\"transferredEmail\"}},\"mNationalClientIdentifier\":{\"value\":{\"mNationality\":{\"value\":\"CY\"},\"mNCINumber\":{\"value\":\"nciNumber\"},\"mNCIType\":{\"value\":\"1\"},\"mNCIPriority\":{\"value\":1},\"mNCIUpdatedDate\":{\"value\":{\"date\":{\"year\":2017,\"month\":1,\"day\":1},\"time\":{\"hour\":0,\"minute\":0,\"second\":11,\"nano\":0}}}}},\"mLegalEntityIdentifier\":{\"value\":{\"mLEINumber\":\"leiNumber\"}},\"mHasTaxUS\":{\"value\":true},\"mHasTaxInAnotherCountry\":{\"value\":true},\"mNoTaxIdReason\":{\"value\":null},\"mAppropriateness\":{\"value\":{\"mAnnualDisposableIncomeOption\":{\"value\":{\"mAnnualDisposableIncomeOptId\":0,\"mAnnualDisposableIncomeOptDesc\":\"Not submitted\"}},\"mMoneyRiskLosingOption\":{\"value\":{\"mMoneyRiskLosingOptId\":1,\"mMoneyRiskLosingOptDesc\":\"More than 80%. I am a risk taker\"}},\"mDescribeInvestmentObjectiveOption\":{\"value\":{\"mDescribeInvestmentObjectiveOptId\":2,\"mDescribeInvestmentObjectiveOptDesc\":\"Hedging\"}},\"mRequiredMarginOneLotOption\":{\"value\":{\"mRequiredMarginOneLotOptId\":0,\"mRequiredMarginOneLotOptDesc\":\"Not submitted\"}},\"mLargestPotentialProfitLossOption\":{\"value\":{\"mLargestPotentialProfitLossOptId\":1,\"mLargestPotentialProfitLossOptDesc\":\"1:5\"}},\"mClosingOrderLimitLossesOption\":{\"value\":{\"mClosingOrderLimitLossesOptId\":2,\"mClosingOrderLimitLossesOptDesc\":\"A \\\"stop loss\\\" order\"}},\"mTransactionsPastThreeYearsOption\":{\"value\":{\"mTransactionsPastThreeYearsOptId\":0,\"mTransactionsPastThreeYearsOptDesc\":\"Not submitted\"}},\"mAverageAmountInvestedAnnuallyOption\":{\"value\":{\"mAverageAmountInvestedAnnuallyOptId\":1,\"mAverageAmountInvestedAnnuallyOptDesc\":\"More than $5,000\"}},\"mExperienceServicesProductOption\":{\"value\":{\"mExperienceServicesProductOptId\":2,\"mExperienceServicesProductOptDesc\":\"I have at least one year of experience in trading in the above products\"}},\"mMoneySavingOption\":{\"value\":{\"mMoneySavingOptId\":1,\"mMoneySavingOptDesc\":\"More than 50%\"}},\"mRisksOfLeverageTradingOption\":{\"value\":{\"mRisksOfLeverageTradingOptId\":1,\"mRisksOfLeverageTradingOptDesc\":\"It could magnify my profits and losses\"}},\"mPriceMovementsEffectsOption\":{\"value\":{\"mPriceMovementsEffectsOptId\":1,\"mPriceMovementsEffectsOptDesc\":\"When the market is moving against me and I don’t have enough equity to meet the margin requirement\"}},\"mRiskInCfdOption\":{\"value\":{\"mRiskInCfdOptId\":2,\"mRiskInCfdOptDesc\":\"Most of it.I’m happy to accept considerable risk\"}},\"mCfdTradingOption\":{\"value\":{\"mCfdTradingOptId\":3,\"mCfdTradingOptDesc\":\"To be able to profit from both rising and falling markets\"}},\"mOrderPlacingOption\":{\"value\":{\"mOrderPlacingOptId\":3,\"mOrderPlacingOptDesc\":\"There’s no such order\"}},\"mPreviousTradingExperienceOption\":{\"value\":{\"mPreviousTradingExperienceOptId\":4,\"mPreviousTradingExperienceOptDesc\":\"Never\"}},\"mTradingEducationOption\":{\"value\":{\"mTradingEducationOptId\":6,\"mTradingEducationOptDesc\":\"None\"}},\"mAnnualSaveOption\":{\"value\":{\"mAnnualSaveOptId\":5,\"mAnnualSaveOptDesc\":\"I don’t save any of it\"}},\"mCFDSellValueOption\":{\"value\":{\"mId\":1,\"mDescription\":\"decreases\"}}}},\"mProfessionalClient\":{\"value\":{\"mIsProfessionalClient\":{\"value\":true},\"mHasTradedTenTimes\":{\"value\":true},\"mHoldsInvestmentPortfolio\":{\"value\":true},\"mHasWorkedFinancialSector\":{\"value\":true},\"mSignificantlySizedTradesOption\":{\"value\":{\"mSignificantlySizedTradeId\":1,\"mSignificantlySizedTradeDescription\":\"0 - 9\"}},\"mSavingsInvestmentsAmountOption\":{\"value\":{\"mSavingsInvestmentsAmountTypeId\":2,\"mSavingsInvestmentsAmountTypeDescription\":\"500K - 1M\"}},\"mMostRelevantEmployerName\":{\"value\":\"employer name\"},\"mIsMostRelevantEmployerRegulated\":{\"value\":true},\"mMostRelevantEmployerRoleOption\":{\"value\":{\"mMostRelevantEmployerRoleTypeId\":3,\"mMostRelevantEmployerRoleTypeDescription\":\"Analyst\"}},\"mMostRelevantEmployerRoleOther\":{\"value\":\"employer role other\"},\"mHasAcceptedTreadedAsProfessionalClient\":{\"value\":true},\"mHasAcceptedMayLooseProtections\":{\"value\":true},\"mApplicationStatusOption\":{\"value\":{\"mApplicationStatusId\":1,\"mApplicationStatusDescription\":\"Pending\"}}}},\"mEconomicProfile\":{\"value\":{\"mPurposeOfAccountOpeningOption\":{\"value\":[{\"mPurposeOfAccountOpeningOptionId\":0,\"mPurposeOfAccountOpeningOptionDescription\":\"Trade in Forex CFDs\"},{\"mPurposeOfAccountOpeningOptionId\":1,\"mPurposeOfAccountOpeningOptionDescription\":\"Trade in Shares CFDs\"}]},\"mOriginOfIncomingFundsOption\":{\"value\":[{\"mOriginOfIncomingFundsOptionId\":0,\"mOriginOfIncomingFundsDescription\":\"Credit / Debit Cards\"},{\"mOriginOfIncomingFundsOptionId\":1,\"mOriginOfIncomingFundsDescription\":\"Electronic Payment / E-Wallet\"}]},\"mCountrySourceOfFunds\":{\"value\":[{\"mCountryCodeIso2\":\"AD\",\"mCountryName\":\"Andorra\"},{\"mCountryCodeIso2\":\"CY\",\"mCountryName\":\"Cyprus\"}]},\"mCountryDestinationOfFunds\":{\"value\":[{\"mCountryCodeIso2\":\"AD\",\"mCountryName\":\"Andorra\"},{\"mCountryCodeIso2\":\"CY\",\"mCountryName\":\"Cyprus\"}]},\"mEmploymentPositionOption\":{\"value\":{\"mEmploymentPositionOptionId\":0,\"mEmploymentPositionDescription\":\"Owner\"}},\"mEmploymentPositionOther\":{\"value\":\"Other\"},\"mSourceOfWealthOption\":{\"value\":{\"mSourceOfWealthOptionId\":0,\"mSourceOfWealthDescription\":\"Employment / Savings from salary\"}},\"mSourceOfWealthOther\":{\"value\":\"Other\"},\"mPoliticallyExposedPerson\":{\"value\":true},\"mPoliticallyExposedPersonFamily\":{\"value\":true},\"mCurrentJobDescription\":{\"value\":\"currentJobDescription\"},\"mCurrentNameOfEmployer\":{\"value\":\"currentNameOfEmployer\"},\"mCurrentEmploymentStartedYear\":{\"value\":2019},\"mCurrentEmploymentUpToYear\":{\"value\":2020},\"mCurrentEmploymentStartedMonth\":{\"value\":2},\"mCurrentEmploymentUpToMonth\":{\"value\":4},\"mPastJobDescription\":{\"value\":\"pastJobDescription\"},\"mPastNameOfEmployer\":{\"value\":\"pastNameOfEmployer\"},\"mPastEmploymentStartedYear\":{\"value\":2018},\"mPastEmploymentUpToYear\":{\"value\":2019},\"mPastEmploymentUpToMonth\":{\"value\":2},\"mPastEmploymentStartedMonth\":{\"value\":1},\"mTraveledRelocatedPastYears\":{\"value\":true},\"mTraveledRelocatedPastYearsClarification\":{\"value\":\"Travel in another country\"},\"mSourceOfWealthAdditionalInfo\":null,\"mPoliticallyExposedPersonOrFamily\":{\"value\":false},\"mPoliticallyExposedPersonOrFamilyInfo\":{\"value\":null}}},\"mLastUpdateDate\":{\"date\":{\"year\":2020,\"month\":3,\"day\":9},\"time\":{\"hour\":13,\"minute\":32,\"second\":56,\"nano\":*********}},\"mClosedDate\":{\"value\":{\"dateTime\":{\"date\":{\"year\":2011,\"month\":12,\"day\":3},\"time\":{\"hour\":10,\"minute\":15,\"second\":30,\"nano\":0}},\"offset\":{\"totalSeconds\":3600},\"zone\":{\"id\":\"+01:00\"}}},\"mIsAffiliate\":false,\"mPlaceOfBirth\":{\"value\":{\"mCountryCodeIso2\":\"CY\",\"mCountryName\":\"Cyprus\"}},\"mRegimeAcceptance\":{\"value\":{\"mAcceptRegime\":false,\"mAcceptRegimeDate\":{\"date\":{\"year\":2020,\"month\":3,\"day\":9},\"time\":{\"hour\":15,\"minute\":32,\"second\":59,\"nano\":*********}},\"mAuditLog\":null}},\"mXmProfileId\":{\"value\":12345},\"mRegistrationStatus\":\"COMPLETED\",\"mVulnerabilityCharacteristics\":{\"value\":{\"mHasVulnerability\":{\"value\":true},\"mVulnerabilityDetails\":{\"value\":\"Detail 15\"}}},\"moCommentReason\":{\"value\":\"Some reason\"},\"mXmProfileAffiliateId\":{\"value\":1},\"mPorState\":{\"value\":null}},\"mTradingAccounts\":[{\"mAccountName\":\"Nickname\",\"mLogin\":2000007,\"mServerId\":2,\"mServerInformation\":{\"mServerId\":2,\"mPlatformType\":\"MT4\",\"mServerLoginName\":\"XM.COM-Real 2\"},\"mCurrency\":\"EUR\",\"mRegistrationDate\":{\"date\":{\"year\":2017,\"month\":1,\"day\":1},\"time\":{\"hour\":0,\"minute\":0,\"second\":10,\"nano\":0}},\"mRegistrationIp\":\"registrationIp\",\"mRegistrationIpCountry\":{\"value\":{\"mCountryCodeIso2\":\"CY\",\"mCountryName\":\"Cyprus\"}},\"mGid\":{\"value\":0},\"mGwClickDate\":{\"value\":{\"date\":{\"year\":2017,\"month\":1,\"day\":1},\"time\":{\"hour\":0,\"minute\":0,\"second\":11,\"nano\":0}}},\"mSource\":{\"value\":\"EL-Website\"},\"mGroup\":\"EUR-2NR-XA0-2\",\"mAccountType\":{\"mAccountTypeId\":0,\"mAccountTypeDescription\":\"Standard\"},\"mClientType\":{\"mClientTypeId\":0,\"mClientTypeDescription\":\"Personal\"},\"mIsArchived\":false,\"mArchiveDate\":{\"value\":null},\"mClosedDate\":{\"value\":null},\"mIsIslamic\":true,\"mIsEnabled\":true,\"mIsReadOnly\":true,\"mIsSendReports\":true,\"mIsAgreeTermsBonus\":{\"value\":true},\"mAutoRebate\":{\"value\":{\"mIsEligible\":true,\"mHasAccepted\":{\"value\":true},\"mAcceptedDate\":{\"value\":{\"date\":{\"year\":2017,\"month\":1,\"day\":1},\"time\":{\"hour\":0,\"minute\":0,\"second\":11,\"nano\":0}}}}},\"mTradingAccountTransfer\":{\"value\":{\"mTransferBetweenBrands\":1,\"mTransferDate\":{\"value\":{\"date\":{\"year\":2017,\"month\":1,\"day\":1},\"time\":{\"hour\":0,\"minute\":0,\"second\":9,\"nano\":0}}}}},\"mLeverage\":100,\"mAccountStatus\":\"Active\",\"mComment\":{\"value\":\"comment\"},\"mAffiliateId\":{\"value\":null},\"mLeverageBlock\":true,\"mSocialTradingStatus\":{\"value\":\"MASTER\"}}],\"mWalletAccounts\":[{\"mWalletAccountId\":78577,\"mWalletBrand\":{\"mBrandId\":0,\"mBrandName\":\"XM\"},\"mLogin\":4357098,\"mServerId\":50,\"mCurrency\":\"USD\",\"mRegistrationDate\":{\"date\":{\"year\":2017,\"month\":11,\"day\":26},\"time\":{\"hour\":11,\"minute\":41,\"second\":17,\"nano\":********}},\"mRegistrationIp\":\"registrationIp\",\"mRegistrationIpCountry\":{\"value\":null},\"mSource\":{\"value\":null},\"mGroup\":\"Real50\\\\xm\\\\NoTrade\\\\USD-8NR-XA0-2\",\"mAccountType\":{\"mAccountTypeId\":4,\"mAccountTypeDescription\":\"AffPayment\"},\"mClientType\":{\"mClientTypeId\":2,\"mClientTypeDescription\":\"Wallet\"},\"mIsEnabled\":true,\"mIsReadOnly\":false,\"mSendReports\":true,\"mAgreedTermsBonus\":true,\"mAgreedPromo\":true,\"mLeverage\":1,\"mAffiliateId\":1015,\"mPlatformComment\":{\"value\":\"comment\"},\"mIsArchived\":false,\"mAccountStatus\":\"Active\",\"mArchiveDate\":{\"value\":{\"date\":{\"year\":2017,\"month\":11,\"day\":26},\"time\":{\"hour\":11,\"minute\":41,\"second\":17,\"nano\":********}}},\"mServerName\":\"real\",\"mServerType\":\"real\"}],\"mMinimumRequirements\":{\"value\":null},\"mRestrictedExperience\":{\"value\":null},\"mValidationFlow\":{\"mValidationFlowStatus\":0,\"mIslamicState\":0,\"mIsDefaultResponse\":true,\"mValidationFlowsData\":[{\"mId\":0,\"mDescription\":\"Test\",\"mStatus\":0,\"mData\":[{\"mType\":0,\"mStatus\":1,\"mError\":{\"mCode\":0,\"mErrorMessage\":\"Test message\"}}]}],\"mNextFlowId\":0},\"mComments\":[\"NOSF\"],\"mW8BENStatus\":\"NOT_SIGNED\",\"mCitizenship\":{\"value\":\"UK\"},\"mAssessmentScore\":17,\"mIsSwapFreeAbuser\":false,\"mBrandChangeInfo\":{\"value\":{\"mStatus\":\"PENDING\",\"mSourceBrand\":\"XMBZ\",\"mDestinationBrand\":\"XMMB\",\"mUpdateDate\":{\"date\":{\"year\":2024,\"month\":5,\"day\":26},\"time\":{\"hour\":12,\"minute\":0,\"second\":0,\"nano\":0}}}}}""";

}